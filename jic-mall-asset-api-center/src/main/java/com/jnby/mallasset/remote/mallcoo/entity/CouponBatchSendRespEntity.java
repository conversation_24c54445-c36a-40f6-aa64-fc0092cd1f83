package com.jnby.mallasset.remote.mallcoo.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * {
 * "BussinessID": null,
 * "TraceID": "6d025700bb79458ca73ae5051cf626ab",
 * "PICMID": "jBBn1JzXBMf1rjE4GjGjrMjFF8W5InJFSqwmB04DjXc=",
 * "Code": 638,
 * "IsSuccess": false,
 * "FailReason": "[638]已经领取过",
 * "VCode": "95701111111111",
 * "EnableTime": "2017-01-01T00:00:00",
 * "OverdueTime": "2017-04-29T23:59:59"
 * }
 */
@Builder
@ApiModel("批量发券回参")
@Data
public class CouponBatchSendRespEntity implements Serializable {

    @ApiModelProperty("事务ID")
    private String BussinessID;
    @ApiModelProperty("追踪ID")
    private String TraceID;
    @ApiModelProperty("投放ID")
    private String PICMID;
    @ApiModelProperty("返回状态")
    private int Code;
    @ApiModelProperty("券是否领取成功")
    private boolean IsSuccess;
    @ApiModelProperty("失败原因")
    private String FailReason;
    @ApiModelProperty("验证码")
    private String VCode;
    @ApiModelProperty("启用时间")
    private String EnableTime;
    @ApiModelProperty("过期时间")
    private String OverdueTime;
}
