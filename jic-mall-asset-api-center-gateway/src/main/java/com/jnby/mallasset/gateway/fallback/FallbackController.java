package com.jnby.mallasset.gateway.fallback;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

/**
 * 响应超时熔断处理器
 *
 * <AUTHOR>
 */
@RestController
public class FallbackController {

    /**
     * 全局熔断处理
     * @return
     */
    @RequestMapping("/fallback")
    public Mono<ServerResponse> fallback() {
        return ServerResponse.status(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .header("Content-Type","application/json;charset=UTF-8").body(BodyInserters.fromValue("访问接口超时，请稍后再试"));
    }

    /**
     * demo熔断处理
     * @return
     */
    @RequestMapping("/demo/fallback")
    public Mono<String> fallback2() {
        return Mono.just("访问超时，请稍后再试!");
    }
}
