package com.jnby.mallasset.remote.mallcoo.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MemberTagReqEntity implements Serializable {
    @ApiModelProperty(value = "手机号", required = true)
    @JSONField(ordinal = 1)
    private String mobile;
    @ApiModelProperty(value = "标签ID集合", required = true)
    @JSONField(ordinal = 2)
    private List<Long> tagIDList;
    @ApiModelProperty(value = "备注", required = true)
    @JSONField(ordinal = 3)
    private String desc;
}
