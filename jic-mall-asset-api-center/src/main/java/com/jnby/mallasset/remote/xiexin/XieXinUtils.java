package com.jnby.mallasset.remote.xiexin;

import com.alibaba.fastjson.JSON;
import com.jnby.mallasset.config.retrofit.RetrofitConfig;
import com.jnby.mallasset.remote.xiexin.entity.vip.*;
import lombok.extern.slf4j.Slf4j;
import retrofit2.Response;

@Slf4j
public class XieXinUtils {

    public static void main(String[] args) {
        try {
            IXiexinSoapRemoteHttpApi api = RetrofitConfig.getXiexinApi("http://**************:8333", IXiexinSoapRemoteHttpApi.class);
            XiexinVipSoapEnvelope soapEnvelope = new XiexinVipSoapEnvelope();
            XiexinVipSoapBody soapBody = new XiexinVipSoapBody();
            XiexinVipRequestModel requestModel = new XiexinVipRequestModel();
            requestModel.setCondType("3");
            requestModel.setCondValue("15820584615");
            soapBody.setRequestModel(requestModel);
            soapEnvelope.setBody(soapBody);

            XiexinVipSoapHeader soapHeader = new XiexinVipSoapHeader();
            XiexinVipRequestHeader requestHeader = new XiexinVipRequestHeader();
            requestHeader.setUserId("jnby");
            requestHeader.setPassword("Mjoljk2N");
            soapHeader.setRequestHeader(requestHeader);
            soapEnvelope.setHeader(soapHeader);
            Response<XiexinVipResponseSoapEnvelope> response = api.getVipCard(soapEnvelope).execute();
            XiexinVipResponseSoapEnvelope body = response.body();
            System.out.println(JSON.toJSONString(body));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
