package com.jnby.mallasset.remote.kkmall.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.kkmall.entity-KkMallOrderReqEntity.java
 * 文件简介: KKMALL退货单请求
 *
 * <AUTHOR>
 * @date 2024/8/1 15:52
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class KkMallOrderRefundRespEntity implements Serializable {
    @ApiModelProperty(value = "扣除积分", required = true)
    private double returnPoint;
    @ApiModelProperty(value = "交易记录id", required = true)
    private String sourceTransactionId;
    @ApiModelProperty(value = "退货单据id", required = true)
    private String id;
    @ApiModelProperty(value = "会员id", required = true)
    private String customerId;

}
