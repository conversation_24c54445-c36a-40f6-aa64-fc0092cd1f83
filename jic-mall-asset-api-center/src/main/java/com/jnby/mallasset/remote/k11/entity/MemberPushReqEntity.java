package com.jnby.mallasset.remote.k11.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 文件名: com.jnby.mallasset.remote.haiancheng.entity-BaseHacResp.java
 * 文件简介: 海岸城基础返回对象
 *
 * <AUTHOR>
 * @date 2025/1/13 9:51
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class MemberPushReqEntity implements Serializable {
    @ApiModelProperty(value = "联系电话")
    private String phone;
    @ApiModelProperty(value = "电话区号：如 86 、852")
    private String phone_code;
    @ApiModelProperty(value = "即mall_code商场编码，会员的发卡地点。如：SH01")
    private String reg_origin;
    @ApiModelProperty(value = "会员中文名称")
    private String nick_name;
    @ApiModelProperty(value = "渠道 如果需要传 找运营同事提供")
    private int origin;

}
