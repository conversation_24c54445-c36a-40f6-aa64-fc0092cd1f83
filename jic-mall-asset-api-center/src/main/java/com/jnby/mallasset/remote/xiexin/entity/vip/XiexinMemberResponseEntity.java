package com.jnby.mallasset.remote.xiexin.entity.vip;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DrcMemberRegisterReqEntity.java
 * 文件简介: 订单消费请求
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class XiexinMemberResponseEntity implements Serializable {
    @ApiModelProperty(value = "是否存在")
    private Integer is_exist;
}
