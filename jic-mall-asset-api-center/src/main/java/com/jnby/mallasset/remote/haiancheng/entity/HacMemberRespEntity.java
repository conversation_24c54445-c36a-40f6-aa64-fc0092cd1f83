package com.jnby.mallasset.remote.haiancheng.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.haiancheng.entity-HacMemberRespEntity.java
 * 文件简介:
 *
 * <AUTHOR>
 * @date 2025/1/13 9:53
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class HacMemberRespEntity implements Serializable {
    @ApiModelProperty(value = "主键ID")
    private String id;
    @ApiModelProperty(value = "手机")
    private String mobile;
    @ApiModelProperty(value = "会员状态")
    private String memberStatus;
    @ApiModelProperty(value = "卡号")
    private String cardNo;
    @ApiModelProperty("开卡门店ID")
    private String openCardMallId;
    @ApiModelProperty("会员积分余额")
    private Double pointBalance;
    @ApiModelProperty("会员余额")
    private Double prepayBalance;

}
