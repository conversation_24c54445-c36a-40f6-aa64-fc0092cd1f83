1、签名步骤：使用私钥加签，公钥验签
（1）首先将原始参数map排序，使用：
String originalParamJsonStr = JSON.toJSONString(originalParamMap,SerializerFeature.MapSortField);
（2）使用珑珠提供的工具类将排序后的map加签：RSAUtils.sign(originalParamJsonStr)
（3）将加完签后的签名put到原始map中，再进行一次排序：
String originalParamJsonStr = JSON.toJSONString(originalParamMap,SerializerFeature.MapSortField);
（4）签名结束
2.、数据加密
加密方案：采用AES和RSA混合加密的方式实现数据安全传输。
加密步骤(以合作单位系统调用珑珠为例),反向同理
（1）首先合作单位系统和珑珠互换公钥
（2）将1中排序后的报文采用AES来加密，AES的pass为随机生成的字符串例如UUID，每次请求均不同，请使用珑珠提供的工具类生成：
encryptData = AESUtil.encrypt(originalParamMap,pass);
（3）将AES的pass使用RSA的方式加密，用的是珑珠的公钥加密，工具类：encryptKey = RSAUtils.publicEncrypt(pass,longballPublickey);
（4）报文数据加密完成