package com.jnby.mallasset.strategy.category;

import com.jnby.common.ResponseResult;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;

public interface IBaseOrderService {
    /**
     * 推送订单
     */
    ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef);
    /**
     * 推送退款单
     */
    ResponseResult pushRefundOrder(OrderRefundReq OrderRefundReq,CashierMallAssetStoreRef cashierMallAssetStoreRef);
    /**
     * 预退货
     */
    ResponseResult prepareRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef cashierMallAssetStoreRef);
    /**
     * 自收银退货
     */
    ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef cashierMallAssetStoreRef);

}
