package com.jnby.mallasset.remote.mallcoo.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@ApiModel("批量返还券入参")
@Data
public class CouponBatchReturnReqEntity implements Serializable {

    @ApiModelProperty("待撤销券集合")
    @JSONField(ordinal = 1, name = "CouponInfoList")
    private List<ReturnCoupon> CouponInfoList;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ReturnCoupon implements Serializable {
        @ApiModelProperty("券码")
        @JSONField(ordinal = 1, name = "VCode")
        private String VCode;
        @ApiModelProperty("追踪ID【不可重复(当前应用下必须唯一)】(保持请求的的唯一性，防止重复提交)")
        @JSONField(ordinal = 2, name = "TraceID")
        private String TraceID;
    }
}
