package com.jnby.mallasset.remote.wanke;

import cn.com.scpgroup.SignUtil;
import com.alibaba.fastjson.JSON;
import com.jnby.mallasset.config.retrofit.RetrofitConfig;
import com.jnby.mallasset.remote.wanke.entity.BaseWanKeResp;
import com.jnby.mallasset.remote.wanke.entity.WanKeMemberRespEntity;
import com.jnby.mallasset.remote.wanke.entity.WanKeOrderRefundReqEntity;
import retrofit2.Response;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

public class WanKeUtils {

    public static void main(String[] args) {

        long timestamp = new Date().getTime();
        String appId = "14336f5343f99ba62a2691f9e7f47138";
        String orgCode = "G001Z003C0999";
        String privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJuBCvW36D3uonoEB4fAHu54FJvsvTcyRa4Qe7vPYr6fBYdujXmr37uPqREKumPyo9myCc/f8ZhVKJ/1TqJpUQgirnw6/PQ1KLKZdKEM9Iz4g9PsyYV/T5907GSJhix/h4pGMfYruH7QRAPUuQuksXFCxH4VKUDJn1F6TXjZ9hTHAgMBAAECgYBDOaovR+4SuBNthEhtG1VD6o2eSt4R+p28/ks/igw6NC0Du3tV2kPCpfyE7YpeowrKlfk/8KRVIpuJa1cvRzBMDs63nCqo2L8WLWCjkzHdJkLSrDEA+ojZPz9v0g53UIeFA/2+ovcw33TCEX2RLPubauHSMb0YUH6ZAdHn5duhgQJBAPwr4zLuophUD8SwyphLh1nJji4rCs37TjyvlS5m6/d7eTKDY5maQ9Pj6VhlgSd6VCf69/nQFsiBXIAOnYKOYvcCQQCd3XBqvdsIgDdNRqqUarVht/ysMGnj8vNIkWSKTdPuYM7LlozIEBmaH9Pg18zAitpmBoX3Wz3nnCeAYa7aB5ixAkAwprb6yJeOFAnGxOURZOEELaLEvYEkJGv/wVBi13CHGsdTO44nwF99recDKvI0D6HU5NKa287JeEvEWQ2Cdr4ZAkBwXOgiIcZgU5qJN04824ME7cc77C3CoO+G7G3Kf0DkwCURBbRoxgTjDdpqhE9pkKuIyQXbnSb/zsbS+zRQXe3RAkEAnRPf/kkSnvDEW59zIyC/dejVY/csnvOdy9Yatg6oqus0jLE1j7U43MbzBClGv5uUtyCN1Sc+l8HLHrkUWqajEw==";
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put("appId",appId);
        headers.put("orgCode",orgCode);
        System.out.println("timestamp==" + timestamp);
        headers.put("timestamp",String.valueOf(timestamp));

        String orderNo = "***************-**********";
        String oriOrderNo = "***************";
        String storeCode = "S23050201";
        String payAmount = "263600";
        String refundAmount = "263600";
        String refundTime = "2025-01-14 20:07:04";
        String memberId = "*************";
        String telephone = "***********";

        Map<String,Object> paramMap = new LinkedHashMap<>(32);
        paramMap.put("accountType","2"); // accountType:1-会员Id；2-手机号
        paramMap.put("accountValue","***********");

        String sign = SignUtil.generateSignByPost(appId, orgCode, timestamp, privateKey, paramMap);
        headers.put("sign", sign);
        System.out.println("sign==" + sign);
        try {
            IWanKeRemoteHttpApi api = RetrofitConfig.getMainApi("https://crm-test.scpgroup.com.cn", IWanKeRemoteHttpApi.class);
            WanKeOrderRefundReqEntity req = new WanKeOrderRefundReqEntity();
            req.setOrderNo(orderNo);
            req.setOriOrderNo(oriOrderNo);
            req.setStoreCode(storeCode);
            req.setChannel("1");
            req.setPayAmount(payAmount);
            req.setRefundAmount(refundAmount);
            req.setRefundTime(refundTime);
            req.setMemberId(memberId);
            req.setTelephone(telephone);
            req.setPayType("3");
            req.setPayMethod("3");
            System.out.println("param========" + JSON.toJSONString(req));


            Response<BaseWanKeResp<WanKeMemberRespEntity>> execute = api.queryMemberFromTest(headers,paramMap).execute();
            BaseWanKeResp<WanKeMemberRespEntity> body = execute.body();
            System.out.println(body.isSuccess());
            System.out.println(JSON.toJSONString(body));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
