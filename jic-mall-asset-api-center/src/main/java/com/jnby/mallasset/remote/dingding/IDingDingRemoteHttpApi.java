package com.jnby.mallasset.remote.dingding;


import com.jnby.mallasset.dto.req.OrderCallBackRequest;
import com.jnby.mallasset.dto.res.OrderCallBackResponse;
import com.jnby.mallasset.remote.dingding.entity.DingDingMsg;
import com.jnby.mallasset.remote.dingding.entity.DingDingResponse;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;


/**
 * 文件名: com.jnby.mallasset.remote.dingding-IDingDingRemoteHttpApi.java
 * 文件简介: 钉钉发送消息
 *
 * <AUTHOR>
 * @date 2024/8/18 16:23
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface IDingDingRemoteHttpApi {

    /**
     * 功能描述: 推送钉钉消息
     * 使用场景:
     *
     * @param dingDingMsg
     * @return retrofit2.Call<com.jnby.mallasset.dto.res.OrderCallBackResponse>
     * <AUTHOR>
     * @date 2024/8/18 16:26
     */
    @POST("/robot/send?access_token=618443d7717146743a7a170ef3518a74c531cb089168037e36254bde7e655b66")
    Call<DingDingResponse> pushMsg(@Body DingDingMsg dingDingMsg);
}
