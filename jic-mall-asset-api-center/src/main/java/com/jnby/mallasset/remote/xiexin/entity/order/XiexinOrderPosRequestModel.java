package com.jnby.mallasset.remote.xiexin.entity.order;

import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

import java.math.BigDecimal;

@Data
@Root(name = "SaleShopItem", strict = false)
@Namespace(reference = "http://tempuri.org/")
public class XiexinOrderPosRequestModel {

    @Element(name = "storeCode" , required = false)
    private String storeCode;
    @Element(name = "saleMoney" , required = false)
    private BigDecimal saleMoney;
    @Element(name = "hth" , required = false)
    private Integer hth;
    @Element(name = "vipId" , required = false)
    private Integer vipId;
    @Element(name = "billId" , required = false)
    private Integer billId;
    @Element(name = "saleDate" , required = false)
    private String saleDate;
    @Element(name = "updateCent" , required = false)
    private Double updateCent;
    @Element(name = "updateType" , required = false)
    private Integer updateType;
}
