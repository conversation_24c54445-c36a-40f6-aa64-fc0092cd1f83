package com.jnby.mallasset.remote.huarun;


import com.jnby.mallasset.remote.huarun.entity.BaseHuaRunResp;
import com.jnby.mallasset.remote.huarun.entity.CarCouponReqEntity;
import com.jnby.mallasset.remote.huarun.entity.CarCouponRespEntity;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.HeaderMap;
import retrofit2.http.POST;

import java.util.Map;


/**
 * 文件名: com.jnby.mallasset.remote.huarun-IHuaRunRemoteHttpApi.java
 * 文件简介: 华润退款
 *
 * <AUTHOR>
 * @date 2024/7/19 15:56
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface IHuaRunCouponRemoteHttpApi {

    /**
     * 功能描述: 发放停车券
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooResp>
     * <AUTHOR>
     * @date 2024/7/20 10:01
     */
    @POST("/1.0/api/parking-plus-voucher")
    Call<BaseHuaRunResp<CarCouponRespEntity>> sendCarVoucher(@HeaderMap Map<String, String> headers, @Body CarCouponReqEntity req);

}
