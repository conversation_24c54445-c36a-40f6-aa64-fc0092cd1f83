package com.jnby.mallasset.gateway.config;
import com.jnby.mallasset.gateway.handler.HystrixFallbackHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import static org.springframework.web.reactive.function.server.RequestPredicates.GET;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;
import static org.springframework.web.reactive.function.server.ServerResponse.ok;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-11-12 14:53
 */
@Configuration
public class GateWayConfig {

    public static final long DEFAULT_TIMEOUT = 3000;

    public static String SERVER_ADDR;

    public static String NAMESPACE;

    public static String DATA_ID;

    public static String ROUTE_GROUP;

    public static String USERNAME;

    public static String PASSWORD;

    /**
     * 路由配置文件数据获取方式yml,nacos,database
     */
    public static String DATA_TYPE;

    @Value("${spring.cloud.nacos.discovery.server-addr}")
    public void setServerAddr(String serverAddr) {
        SERVER_ADDR = serverAddr;
    }

    @Value("${spring.cloud.nacos.discovery.namespace}")
    public void setNamespace(String namespace) {
        NAMESPACE = namespace;
    }

    @Value("${jnby.route.config.data-id:#{null}}")
    public void setRouteDataId(String dataId) {
        DATA_ID = dataId + ".json";
    }

    @Value("${jnby.route.config.group:DEFAULT_GROUP:#{null}}")
    public void setRouteGroup(String routeGroup) {
        ROUTE_GROUP = routeGroup;
    }

    @Value("${spring.cloud.nacos.config.username}")
    public void setUsername(String username) {
        USERNAME = username;
    }
    @Value("${spring.cloud.nacos.config.password}")
    public void setPassword(String password) {
        PASSWORD = password;
    }


    /**
     * 路由断言
     * @return
     */
    @Bean
    public RouterFunction routerFunction() {
        return route(
                RequestPredicates.path("/globalFallback").and(RequestPredicates.accept(MediaType.APPLICATION_JSON)), hystrixFallbackHandler);

    }

    /**
     * 映射接口文档默认地址（通过9999端口直接访问）
     * @param indexHtml
     * @return
     */
    @Bean
    public RouterFunction<ServerResponse> indexRouter(@Value("classpath:/META-INF/resources/doc.html") final org.springframework.core.io.Resource indexHtml) {
        return route(GET("/"), request -> ok().contentType(MediaType.TEXT_HTML).syncBody(indexHtml));
    }

    @Autowired
    private HystrixFallbackHandler hystrixFallbackHandler;
}
