package com.jnby.mallasset.strategy.platform.wanke;

import cn.com.scpgroup.SignUtil;
import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.remote.wanke.IWanKeRemoteHttpApi;
import com.jnby.mallasset.remote.wanke.entity.BaseWanKeResp;
import com.jnby.mallasset.remote.wanke.entity.WanKeMemberReqEntity;
import com.jnby.mallasset.remote.wanke.entity.WanKeMemberRespEntity;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import static com.jnby.mallasset.config.WebConfig.ENV;

/**
 * 万科会员平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.WAN_KE, category = PlatformCategoryTypeEnum.MEMBER)
@AllArgsConstructor
@Slf4j
public class WanKeMemberStrategyService extends AbstractMemberService {

    private IWanKeRemoteHttpApi wanKeRemoteHttpApi;
    private CashierMallMemberLogMapper cashierMallMemberLogMapper;


    // 拓展渠道
    private final static String EXPANDING_CHANNEL = "99";
    // 账号类型-手机号
    private final static String ACCOUNT_TYPE_IN_MOBILE = "2";

    @Override
    public ResponseResult openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("万科注册会员输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult commonResponse = new ResponseResult();
        try {
            CashierMallMemberLog cashierMallMemberLog = recordCashierMallMemberLog(memberRegisterReq, cashierMallAssetStoreRef);
            // 查询是不是老会员
            CommonMemberResponse commonMemberResponse = new CommonMemberResponse();
            ResponseResult<WanKeMemberRespEntity> result = getMemberInfo(memberRegisterReq,cashierMallAssetStoreRef);
            log.info("万科注册查询会员getMemberInfo返回参数：{}", JSON.toJSONString(result));
            if(result.getCode() == 200 && result.getData() != null){
                // 说明是老会员
                commonResponse.setCode(200);
                WanKeMemberRespEntity wanKeMemberRespEntity = result.getData();
                commonMemberResponse.setMemberFlag(1);
                commonMemberResponse.setMemberId(wanKeMemberRespEntity.getMemberId());
                commonResponse.setMsg(result.getMsg());
                commonResponse.setData(commonMemberResponse);
                // 更新执行标识
                cashierMallMemberLog.setOpenUserId(wanKeMemberRespEntity.getMemberId());
                cashierMallMemberLog.setIsExecute("Y");
                cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
                return commonResponse;
            }
            // 新会员注册
            long timestamp = new Date().getTime();
            String appId = cashierMallAssetStoreRef.getPlatformAppId();
            String orgCode = cashierMallAssetStoreRef.getPlatformMallId();
            String privateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
            String mobile = memberRegisterReq.getMobile();
            Map<String,Object> bodyParamMap = new HashMap<>();
            bodyParamMap.put("expandingChannel",EXPANDING_CHANNEL);
            bodyParamMap.put("mobileNo",mobile);
            // 使用SignSDK生成签名字符串
            String sign = SignUtil.generateSignByPost(appId, orgCode, timestamp, privateKey, bodyParamMap);
            log.info("万科注册会员生成签名：{}", sign);
            Map<String, String> headers = new LinkedHashMap<>();
            headers.put("appId",appId);
            headers.put("orgCode",orgCode);
            headers.put("timestamp",String.valueOf(timestamp));
            headers.put("sign",sign);
            WanKeMemberReqEntity req = new WanKeMemberReqEntity();
            req.setExpandingChannel(EXPANDING_CHANNEL);
            req.setMobileNo(mobile);
            log.info("万科注册会员调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseWanKeResp<WanKeMemberRespEntity>> execute = wanKeRemoteHttpApi.registerMember(headers,req).execute();
            BaseWanKeResp<WanKeMemberRespEntity> body = execute.body();
            log.info("万科注册会员返回参数：{}", JSON.toJSONString(body));
            commonResponse.setCode(body.getStatus());
            commonResponse.setMsg(body.getMessage());
            if(body.getStatus() == 200){
                commonResponse.setCode(200);
                String memberId = body.getData().getMemberId();
                commonMemberResponse.setMemberFlag(0);
                commonMemberResponse.setMemberId(memberId);
                commonResponse.setData(commonMemberResponse);
                // 更新执行标识
                cashierMallMemberLog.setOpenUserId(memberId);
                cashierMallMemberLog.setIsExecute("Y");
                cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
            }else {
                commonResponse.setData(body.getData());
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 万科注册会员异常",memberRegisterReq.getMobile(), e);
        }
        return commonResponse;
    }

    private CashierMallMemberLog recordCashierMallMemberLog(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallMemberLog cashierMallMemberLog = new CashierMallMemberLog();
        cashierMallMemberLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallMemberLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallMemberLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallMemberLog.setBjStoreId(memberRegisterReq.getStoreId());
        cashierMallMemberLog.setMobile(memberRegisterReq.getMobile());
        cashierMallMemberLog.setIsExecute("N");
        cashierMallMemberLogMapper.insertEntity(cashierMallMemberLog);
        return cashierMallMemberLog;
    }

    @Override
    public ResponseResult<WanKeMemberRespEntity> getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("万科查询会员输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult<WanKeMemberRespEntity> commonResponse = new ResponseResult<>();
        try {
            long timestamp = new Date().getTime();
            String appId = cashierMallAssetStoreRef.getPlatformAppId();
            String orgCode = cashierMallAssetStoreRef.getPlatformMallId();
            String privateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();

            Map<String,Object> paramMap = new LinkedHashMap<>(32);
            paramMap.put("accountType",ACCOUNT_TYPE_IN_MOBILE); // accountType:1-会员Id；2-手机号
            paramMap.put("accountValue",memberRegisterReq.getMobile());
            String sign = SignUtil.generateSignByGet(appId, orgCode, timestamp, privateKey, paramMap);
            log.info("万科查询会员生成签名：{}", sign);
            Map<String, String> headers = new LinkedHashMap<>();
            headers.put("appId",appId);
            headers.put("orgCode",orgCode);
            headers.put("timestamp",String.valueOf(timestamp));
            headers.put("sign",sign);
            log.info("万科查询会员调用远程接口参数：{}", JSON.toJSONString(paramMap));
            Response<BaseWanKeResp<WanKeMemberRespEntity>> execute = null;
            if ("dev".equals(ENV) || "test".equals(ENV)) {
                execute = wanKeRemoteHttpApi.queryMemberFromTest(headers,paramMap).execute();
            } else {
                execute = wanKeRemoteHttpApi.queryMember(headers,paramMap).execute();
            }
            BaseWanKeResp<WanKeMemberRespEntity> body = execute.body();
            log.info("万科查询会员返回参数：{}", JSON.toJSONString(body));
            commonResponse.setCode(body.getStatus());
            commonResponse.setMsg(body.getMessage());
            commonResponse.setData(body.getData());
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 万科查询会员异常",memberRegisterReq.getMobile(), e);
        }
        return commonResponse;
    }
}
