package com.jnby.mallasset.remote.dayuecheng;


import com.jnby.mallasset.remote.dayuecheng.entity.DycOrderPosReqEntity;
import com.jnby.mallasset.remote.dayuecheng.entity.DycOrderPosRespEntity;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.HeaderMap;
import retrofit2.http.POST;

import java.util.Map;


/**
 * 大悦城POS
 */
public interface IDayuechengPosRemoteHttpApi {

    @POST("/order4openapi/api/collect")
    Call<DycOrderPosRespEntity> orderInfo(@HeaderMap Map<String, String> headers, @Body DycOrderPosReqEntity req);

}
