package com.jnby.mallasset.strategy.platform.k11;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.dto.req.order.PayItem;
import com.jnby.mallasset.dto.req.order.ProductItem;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.remote.dingding.IDingDingRemoteHttpApi;
import com.jnby.mallasset.remote.dingding.entity.DingDingMsg;
import com.jnby.mallasset.remote.dingding.entity.DingDingResponse;
import com.jnby.mallasset.remote.k11.IKelevenPosOderRemoteHttpApi;
import com.jnby.mallasset.remote.k11.IKelevenRemoteHttpApi;
import com.jnby.mallasset.remote.k11.entity.*;
import com.jnby.mallasset.remote.k11.entity.pos.*;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.DateUtil;
import com.jnby.mallasset.util.JsonUtil;
import com.jnby.mallasset.util.OrderNoUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * 凯德订单平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.K_ELEVEN, category = PlatformCategoryTypeEnum.ORDER)
@Slf4j
@AllArgsConstructor
public class KElevenOrderStrategyService extends AbstractOrderService {

    private CashierMallOrderLogMapper cashierMallOrderLogMapper;
    private IKelevenRemoteHttpApi kelevenRemoteHttpApi;
    private IDingDingRemoteHttpApi dingDingRemoteHttpApi;
    private IKelevenPosOderRemoteHttpApi kelevenPosOderRemoteHttpApi;
    private KElevenMemberStrategyService kElevenMemberStrategyService;

    @Override
    public ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef mallAssetStoreRef) {
        log.info("K11 订单同步输入参数：{}", JSON.toJSONString(orderConsumeReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderConsumeReq, mallAssetStoreRef);

            String integralSkuCode = mallAssetStoreRef.getIntegralSkuCode();
            String[] fields = integralSkuCode.split(",");
            String apiKey = fields[0].split("#")[1];
            String apiSecret = fields[1].split("#")[1];
            String interfaceId = fields[2].split("#")[1];
            String skey = fields[4].split("#")[1];
            String strStoreCode = fields[5].split("#")[1];
            String tenderCode = fields[6].split("#")[1];
            String skuCode = fields[7].split("#")[1];
            String areaCode = fields[8].split("#")[1];

            String timestamp = String.valueOf(System.currentTimeMillis()/1000);
            String platformMallStoreId = mallAssetStoreRef.getPlatformMallStoreId();
            
            // 查询会员信息
            MemberRegisterReq memberRegisterReq = new MemberRegisterReq();
            memberRegisterReq.setStoreId(orderConsumeReq.getStoreId());
            memberRegisterReq.setMobile(orderConsumeReq.getMemberTel());
            memberRegisterReq.setNickName(orderConsumeReq.getMemberName());
            String memberCode;
            ResponseResult memberResult = kElevenMemberStrategyService.openCard(memberRegisterReq,mallAssetStoreRef);
            if(memberResult.getCode() == 200 && memberResult.getData() != null){
                CommonMemberResponse commonMemberResponse = (CommonMemberResponse) memberResult.getData();
                memberCode = commonMemberResponse.getMemberId();
            }else{
                commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                commonResponse.setMsg("会员信息不存在");
                return commonResponse;
            }
            
            OrderPushReqEntity orderPushReqEntity = initOrderPushReqEntity(orderConsumeReq, platformMallStoreId,memberCode,areaCode);

            String content = JsonUtil.toJson(orderPushReqEntity);
            Map<String, String> params = new TreeMap<>();
            params.put("content", content);
            params.put("timestamp", timestamp);
            params.put("apiKey", apiKey);
            params.put("apiSecret", apiSecret);
            params.put("interfaceId", interfaceId);

            String sign = KElevenGenerateSign.genSignature(params);
            log.info("K11 订单同步生成的签名为：{}",sign);

            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("apiKey",apiKey);
            paramMap.put("interfaceId",interfaceId);
            paramMap.put("timestamp",timestamp);
            paramMap.put("sign",sign);
            paramMap.put("content",content);
            log.info("K11 订单同步传输参数：{}", JSON.toJSONString(paramMap));
            Response<String> execute = kelevenRemoteHttpApi.consumeOrder(paramMap).execute();
            String result = KElevenGenerateSign.decodeSignature(execute.body(),skey);
            log.info("K11 订单同步返回参数：{}",result);
            BaseElevenResultResp baseElevenResultResp = JSONObject.parseObject(result,BaseElevenResultResp.class);
            if(baseElevenResultResp != null && baseElevenResultResp.getCode() == 0){
                if(baseElevenResultResp.getData() != null){
                    OrderConsumeEntity orderConsumeEntity = JSONObject.parseObject(baseElevenResultResp.getData().toString(),OrderConsumeEntity.class);
                    if(orderConsumeEntity.getStatus() == 1){
                        commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                        OrderRespEntity orderRespEntity = new OrderRespEntity();
                        orderRespEntity.setOrdNum(orderConsumeReq.getOrdNum());
                        orderRespEntity.setExchangeId(orderConsumeReq.getOrdNum());
                        orderRespEntity.setExchangeNo(orderConsumeReq.getOrdNum());
                        commonResponse.setData(orderRespEntity);
                        // 更新返回的参数
                        cashierMallOrderLog.setIsExecute("Y");
                        cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);

                        // POS 订单数据
                        executePosOrder(orderConsumeReq, mallAssetStoreRef, strStoreCode, tenderCode, skuCode,orderConsumeEntity.getMember_code());
                    }else{
                        commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                        commonResponse.setMsg(orderConsumeEntity.getBonus_description());
                    }
                }
            }else {
                commonResponse.setCode(baseElevenResultResp.getCode());
                commonResponse.setMsg(baseElevenResultResp.getMsg());
            }
            log.info("K11 订单同步返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} K11 订单同步失败",orderConsumeReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    private void executePosOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef mallAssetStoreRef, String strStoreCode, String tenderCode, String skuCode,String memberCode) throws IOException {
        KElevenSoapEnvelope req = new KElevenSoapEnvelope();
        KElevenSoapBody body = new KElevenSoapBody();
        req.setBody(body);
        KElevenRequestModel requestModel = new KElevenRequestModel();
        body.setRequestModel(requestModel);
        requestModel.setStrCallUserCode(mallAssetStoreRef.getPlatformPublicKey());
        requestModel.setStrCallPassword(mallAssetStoreRef.getPlatformPrivateKey());
        requestModel.setStrStoreCode(strStoreCode);
        requestModel.setStrSalesDocNo(orderConsumeReq.getOrdNum());
        requestModel.setStrType("SA");
        requestModel.setStrTenderCode(tenderCode);
        requestModel.setStrVipCode(null);
        requestModel.setStrRemark("JNBY");
        String dateTime = DateUtil.timeStamp3Date(orderConsumeReq.getOrdFinishTime(),DateUtil.DATEFORMATE_YYYYMMDDHHMMSS);
        String day = dateTime.substring(0,8);
        String time = dateTime.substring(8);
        requestModel.setStrSalesDate(day);
        requestModel.setStrSalesTime(time);

//        List<ProductItem> productItemList = orderConsumeReq.getProductItemList();
//        List<String> stringList = new LinkedList<>();
//        for (ProductItem productItem : productItemList) {
//            int qty = productItem.getItemQty();
//            BigDecimal amt = productItem.getItemPrice();
//            String str = "{" + skuCode + "," + qty + "," + amt + "}";
//            stringList.add(str);
//        }
//
//        String strItem = String.join(",",stringList);
        String strItem = "{" + skuCode + "," + 1 + "," + orderConsumeReq.getOrdAmount() + "}";
        requestModel.setStrItems(strItem);
        log.info("K11 POS订单传输参数：{}", JSON.toJSONString(req));
        Response<KElevenResponseSoapEnvelope> posExecute = kelevenPosOderRemoteHttpApi.PostSales(req).execute();
        KElevenResponseSoapEnvelope kElevenResponseSoapEnvelope = posExecute.body();
        log.info("K11 POS订单返回参数：{}", JSON.toJSONString(kElevenResponseSoapEnvelope));
        if(kElevenResponseSoapEnvelope != null){
            KElevenResponseSoapBody responseSoapBody = kElevenResponseSoapEnvelope.getBody();
            KElevenPostSalesResult response = responseSoapBody.getResponse().getResult();
            if(response.getResponse() != null && response.getResponse().getResult().getErrorCode() != 0){
                // POS失败钉钉告警，人工处理
                pushPosMsg(JSON.toJSONString(req), JSON.toJSONString(kElevenResponseSoapEnvelope));
            }
        }else{
            pushPosMsg(JSON.toJSONString(req), JSON.toJSONString(kElevenResponseSoapEnvelope));
        }
    }


    private void pushPosMsg(String reqParam, String body) throws IOException {
        String msg = "数据推送异常：\n" + "功能：K11同步POS数据" + "\n请求参数: \n" + reqParam + "\n请求结果: \n" + body;
        DingDingMsg dingDingMsg = new DingDingMsg();
        Map<String,Object> param = new HashMap<>();
        param.put("content",msg);
        dingDingMsg.setText(param);
        dingDingMsg.setMsgtype("text");
        Response<DingDingResponse> execute = dingDingRemoteHttpApi.pushMsg(dingDingMsg).execute();
        log.info("推送消息返回结果：{}",execute.body());
    }

    private OrderPushReqEntity initOrderPushReqEntity(OrderConsumeReq orderConsumeReq, String platformMallStoreId,String memberCode,String areaCode) {
        OrderPushReqEntity orderPushReqEntity = new OrderPushReqEntity();
        orderPushReqEntity.setOrder_code(orderConsumeReq.getOrdNum());
        orderPushReqEntity.setArea_code(areaCode);
        orderPushReqEntity.setOrigin_amount(orderConsumeReq.getOrdAmount());
        orderPushReqEntity.setSales_time(orderConsumeReq.getOrdFinishTime()/1000);
        orderPushReqEntity.setSales_amount(orderConsumeReq.getOrdAmount());
        orderPushReqEntity.setStore_code(platformMallStoreId);
        List<OrderPayItemEntity> sales_payments = new ArrayList<>();
        List<PayItem> payItemList = orderConsumeReq.getPayItemList();
        for (PayItem payItem : payItemList) {
            OrderPayItemEntity orderPayItemEntity = new OrderPayItemEntity();
            orderPayItemEntity.setPrice(payItem.getPayamount());
            orderPayItemEntity.setKpoint(0.0);
            orderPayItemEntity.setIs_real(1);
            orderPayItemEntity.setType("WECHAT_PAY");
            sales_payments.add(orderPayItemEntity);
        }
        orderPushReqEntity.setSales_payments(sales_payments);
        orderPushReqEntity.setMember_code(memberCode);
        return orderPushReqEntity;
    }


    private CashierMallOrderLog recordCashierMallOrderLog(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeNo(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setBjStoreId(orderConsumeReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(1);
        cashierMallOrderLog.setRefundAmount(orderConsumeReq.getOrdAmount());
        cashierMallOrderLogMapper.insertAddLog(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult pushRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef assetStoreRef) {
        log.info("K11 订单退货退款输入参数：{}", JSON.toJSONString(orderRefundReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            String integralSkuCode = assetStoreRef.getIntegralSkuCode();
            String[] fields = integralSkuCode.split(",");
            String apiKey = fields[0].split("#")[1];
            String apiSecret = fields[1].split("#")[1];
            String interfaceId = fields[2].split("#")[1];
            String skey = fields[4].split("#")[1];

            String strStoreCode = fields[5].split("#")[1];
            String tenderCode = fields[6].split("#")[1];
            String skuCode = fields[7].split("#")[1];
            String areaCode = fields[8].split("#")[1];

            String timestamp = String.valueOf(System.currentTimeMillis()/1000);
            String platformMallStoreId = assetStoreRef.getPlatformMallStoreId();

            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderRefundReq, assetStoreRef);

            BigDecimal refundAmount = orderRefundReq.getRefundAmount();
            BigDecimal ordAmount = orderRefundReq.getOrdAmount();
            CashierMallOrderLog orderLog = cashierMallOrderLogMapper.selectOrderLogByOrderNumAndExchangeId(orderRefundReq.getOrdNum(),orderRefundReq.getExchangeId());

            if(ordAmount.compareTo(refundAmount) == 0){
                // 整单退
                Map<String, Object> paramMap = initAllRefundParamMap(orderRefundReq, platformMallStoreId, timestamp, apiKey, apiSecret, interfaceId);

                Response<String> execute = kelevenRemoteHttpApi.returnOrder(paramMap).execute();
                String result = KElevenGenerateSign.decodeSignature(execute.body(),skey);
                log.info("K11 订单退货退款调用远程接口返回参数：{}",result);
                BaseElevenResultResp baseElevenResultResp = JSONObject.parseObject(result,BaseElevenResultResp.class);
                if(baseElevenResultResp != null && baseElevenResultResp.getCode() == 0) {
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                    OrderRespEntity orderRespEntity = new OrderRespEntity();
                    orderRespEntity.setOrdNum(orderRefundReq.getOrdNum());
                    orderRespEntity.setExchangeId(orderRefundReq.getRefundNo());
                    orderRespEntity.setExchangeNo(orderRefundReq.getRefundNo());
                    commonResponse.setData(orderRespEntity);
                    // 更新返回的参数
                    cashierMallOrderLog.setIsExecute("Y");
                    cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);

                    // POS退款数据
                    executePosRefundOrder(orderRefundReq, assetStoreRef, strStoreCode, tenderCode, skuCode);
                }
            }else{
                // 先整单退再部分退
                String exchangeNo = orderLog.getExchangeNo();
                OrderReturnReqEntity orderReturnReqEntity = new OrderReturnReqEntity();
                orderReturnReqEntity.setOrder_code(exchangeNo);
                orderReturnReqEntity.setReturn_code(orderRefundReq.getRefundNo());
                orderReturnReqEntity.setStore_code(platformMallStoreId);
                orderReturnReqEntity.setType("ALL");
                orderReturnReqEntity.setReturn_amount(orderLog.getRefundAmount());
                orderReturnReqEntity.setSales_time(orderRefundReq.getOrdFinishTime()/1000);
                orderReturnReqEntity.setReturn_at(orderRefundReq.getRefundTime()/1000);

                String content = JsonUtil.toJson(orderReturnReqEntity);
                Map<String, String> params = new TreeMap<>();
                params.put("content", content);
                params.put("timestamp", timestamp);
                params.put("apiKey", apiKey);
                params.put("apiSecret", apiSecret);
                params.put("interfaceId", interfaceId);

                String sign = KElevenGenerateSign.genSignature(params);
                log.info("K11 订单同步生成的签名为：{}",sign);

                Map<String,Object> paramMap = new HashMap<>();
                paramMap.put("apiKey",apiKey);
                paramMap.put("interfaceId",interfaceId);
                paramMap.put("timestamp",timestamp);
                paramMap.put("sign",sign);
                paramMap.put("content",content);

                log.info("K11 订单退货退款调用远程接口传输参数：{}", JSON.toJSONString(paramMap));
                Response<String> execute = kelevenRemoteHttpApi.returnOrder(paramMap).execute();
                String result = KElevenGenerateSign.decodeSignature(execute.body(),skey);
                log.info("K11 订单退货退款调用远程接口返回参数：{}",result);
                BaseElevenResultResp baseElevenResultResp = JSONObject.parseObject(result,BaseElevenResultResp.class);
                if(baseElevenResultResp != null && baseElevenResultResp.getCode() == 0) {
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                    OrderRespEntity orderRespEntity = new OrderRespEntity();
                    orderRespEntity.setOrdNum(orderRefundReq.getOrdNum());
                    orderRespEntity.setExchangeId(orderRefundReq.getRefundNo());
                    orderRespEntity.setExchangeNo(orderRefundReq.getRefundNo());
                    commonResponse.setData(orderRespEntity);
                    // 更新返回的参数
                    cashierMallOrderLog.setIsExecute("Y");
                    cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);

                    // 全退之后创建一笔新单
                    CashierMallOrderLog mallOrderLog = cashierMallOrderLogMapper.selectSumRefundAmount(orderRefundReq.getOrdNum());
                    BigDecimal refundAmountSum = mallOrderLog.getRefundAmount();
                    BigDecimal amount = ordAmount.subtract(refundAmountSum);
                    int isNeedCreate = amount.compareTo(BigDecimal.ZERO);
                    if(isNeedCreate != 0){
                        Long seq = cashierMallOrderLogMapper.selectElevenOrderNoSeq();
                        String docNo = "RE" + OrderNoUtils.generalOrderNo(seq);
                        // 记录手动创建的单子
                        recordNewLog(orderRefundReq, assetStoreRef, docNo,amount);

                        // 查询会员信息
                        MemberRegisterReq memberRegisterReq = new MemberRegisterReq();
                        memberRegisterReq.setStoreId(orderRefundReq.getStoreId());
                        memberRegisterReq.setMobile(orderRefundReq.getMemberTel());
                        String memberCode;
                        ResponseResult memberResult = kElevenMemberStrategyService.openCard(memberRegisterReq,assetStoreRef);
                        if(memberResult.getCode() == 200 && memberResult.getData() != null){
                            CommonMemberResponse commonMemberResponse = (CommonMemberResponse) memberResult.getData();
                            memberCode = commonMemberResponse.getMemberId();
                        }else{
                            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                            commonResponse.setMsg("会员信息不存在");
                            return commonResponse;
                        }
                        
                        // 初始化部分退单参数
                        OrderPushReqEntity orderPushReqEntity = initSubOrderPushReqEntity(orderRefundReq, docNo, amount, platformMallStoreId,memberCode,areaCode);

                        String subContent = JsonUtil.toJson(orderPushReqEntity);
                        Map<String, String> subParams = new TreeMap<>();
                        subParams.put("content", subContent);
                        subParams.put("timestamp", timestamp);
                        subParams.put("apiKey", apiKey);
                        subParams.put("apiSecret", apiSecret);
                        subParams.put("interfaceId", interfaceId);

                        String subSign = KElevenGenerateSign.genSignature(subParams);
                        log.info("K11 订单部分退款创建新单同步生成的签名为：{}",subSign);

                        Map<String,Object> subParamMap = new HashMap<>();
                        subParamMap.put("apiKey",apiKey);
                        subParamMap.put("interfaceId",interfaceId);
                        subParamMap.put("timestamp",timestamp);
                        subParamMap.put("sign",subSign);
                        subParamMap.put("content",subContent);
                        log.info("K11 订单部分退款创建新单传输参数：{}", JSON.toJSONString(subParamMap));
                        Response<String> subExecute = kelevenRemoteHttpApi.consumeOrder(subParamMap).execute();
                        String subResult = KElevenGenerateSign.decodeSignature(subExecute.body(),skey);
                        log.info("K11 订单部分退款创建新单返回参数：{}",subResult);

                        BaseElevenResultResp subBaseElevenResultResp = JSONObject.parseObject(subResult,BaseElevenResultResp.class);
                        if(subBaseElevenResultResp != null && subBaseElevenResultResp.getCode() == 0){
                            if(subBaseElevenResultResp.getData() != null){
                                OrderConsumeEntity orderConsumeEntity = JSONObject.parseObject(subBaseElevenResultResp.getData().toString(),OrderConsumeEntity.class);
                                if(orderConsumeEntity.getStatus() == 1){
                                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                                    commonResponse.setMsg(subBaseElevenResultResp.getMsg());
                                    // 更新原单的 exchangeNo
                                    CashierMallOrderLog mallOrderLogNew = new CashierMallOrderLog();
                                    mallOrderLogNew.setOrdNum(orderRefundReq.getOrdNum());
                                    mallOrderLogNew.setExchangeNo(docNo);
                                    mallOrderLogNew.setExchangeId(orderRefundReq.getExchangeId());
                                    mallOrderLogNew.setRemark(ResultCodeEnum.SUCCESS.getMsg());
                                    mallOrderLogNew.setRefundAmount(amount);
                                    cashierMallOrderLogMapper.updateExchangeNoWithParam(mallOrderLogNew);

                                    // POS 订单数据
                                    executeSubPosOrder(orderRefundReq, assetStoreRef, strStoreCode, docNo, tenderCode, skuCode, amount);
                                }else{
                                    commonResponse.setCode(subBaseElevenResultResp.getCode());
                                    commonResponse.setMsg("K11原单已完成部分退款，但新单积分失败：" + orderConsumeEntity.getBonus_description());
                                    // 推送钉钉告警
                                    pushDingMsg(JSON.toJSONString(subParamMap), orderConsumeEntity);
                                }
                            }
                        }else {
                            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                            commonResponse.setMsg(subBaseElevenResultResp.getMsg());
                        }
                    }
                }
            }
            log.info("K11 订单退货退款返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} K11 订单退货退款失败",orderRefundReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    private void executeSubPosOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef, String strStoreCode, String docNo, String tenderCode, String skuCode, BigDecimal amount) throws IOException {
        KElevenSoapEnvelope req = new KElevenSoapEnvelope();
        KElevenSoapBody body = new KElevenSoapBody();
        req.setBody(body);
        KElevenRequestModel requestModel = new KElevenRequestModel();
        body.setRequestModel(requestModel);
        requestModel.setStrCallUserCode(assetStoreRef.getPlatformPublicKey());
        requestModel.setStrCallPassword(assetStoreRef.getPlatformPrivateKey());
        requestModel.setStrStoreCode(strStoreCode);
        requestModel.setStrSalesDocNo(docNo);
        requestModel.setStrType("SA");
        requestModel.setStrTenderCode(tenderCode);
        requestModel.setStrVipCode(null);
        requestModel.setStrRemark("JNBY");
        String dateTime = DateUtil.timeStamp3Date(orderRefundReq.getRefundTime(),DateUtil.DATEFORMATE_YYYYMMDDHHMMSS);
        String day = dateTime.substring(0,8);
        String time = dateTime.substring(8);
        requestModel.setStrSalesDate(day);
        requestModel.setStrSalesTime(time);
//        List<ProductItem> productItemList = orderRefundReq.getProductItemList();
//        List<String> stringList = new LinkedList<>();
//        for (ProductItem productItem : productItemList) {
//            int qty = productItem.getItemQty();
//            BigDecimal amt = productItem.getItemPrice();
//            String str = "{" + skuCode + "," + qty + "," + amt + "}";
//            stringList.add(str);
//        }
//        String strItem = String.join(",",stringList);
        String strItem = "{" + skuCode + "," + 1 + "," + amount + "}";
        requestModel.setStrItems(strItem);
        log.info("K11 订单部分退款创建新单POS传输参数：{}", JSON.toJSONString(req));
        Response<KElevenResponseSoapEnvelope> posExecute = kelevenPosOderRemoteHttpApi.PostSales(req).execute();
        KElevenResponseSoapEnvelope kElevenResponseSoapEnvelope = posExecute.body();
        log.info("K11 订单部分退款创建新单POS返回参数：{}", JSON.toJSONString(kElevenResponseSoapEnvelope));
        if(kElevenResponseSoapEnvelope != null){
            KElevenResponseSoapBody responseSoapBody = kElevenResponseSoapEnvelope.getBody();
            KElevenPostSalesResult response = responseSoapBody.getResponse().getResult();
            if(response.getResponse() != null && response.getResponse().getResult().getErrorCode() != 0){
                // POS失败钉钉告警，人工处理
                pushPosMsg(JSON.toJSONString(req), JSON.toJSONString(kElevenResponseSoapEnvelope));
            }
        }else{
            pushPosMsg(JSON.toJSONString(req), JSON.toJSONString(kElevenResponseSoapEnvelope));
        }
    }

    private void executePosRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef, String strStoreCode, String tenderCode, String skuCode) throws IOException {
        KElevenSoapEnvelope req = new KElevenSoapEnvelope();
        KElevenSoapBody body = new KElevenSoapBody();
        req.setBody(body);
        KElevenRequestModel requestModel = new KElevenRequestModel();
        body.setRequestModel(requestModel);
        requestModel.setStrCallUserCode(assetStoreRef.getPlatformPublicKey());
        requestModel.setStrCallPassword(assetStoreRef.getPlatformPrivateKey());
        requestModel.setStrStoreCode(strStoreCode);
        requestModel.setStrSalesDocNo(orderRefundReq.getRefundNo());
        requestModel.setStrType("SR");
        requestModel.setStrTenderCode(tenderCode);
        requestModel.setStrVipCode(null);
        requestModel.setStrRemark("JNBY");
        String dateTime = DateUtil.timeStamp3Date(orderRefundReq.getRefundTime(),DateUtil.DATEFORMATE_YYYYMMDDHHMMSS);
        String day = dateTime.substring(0,8);
        String time = dateTime.substring(8);
        requestModel.setStrSalesDate(day);
        requestModel.setStrSalesTime(time);
//        List<ProductItem> productItemList = orderRefundReq.getProductItemList();
//        List<String> stringList = new LinkedList<>();
//        for (ProductItem productItem : productItemList) {
//            int qty = productItem.getItemQty();
//            BigDecimal amt = productItem.getItemPrice();
//            String str = "{" + skuCode + "," + qty + "," + amt + "}";
//            stringList.add(str);
//        }
//        String strItem = String.join(",",stringList);
        String strItem = "{" + skuCode + "," + 1 + "," + orderRefundReq.getRefundAmount() + "}";
        requestModel.setStrItems(strItem);
        log.info("K11 POS退单传输参数：{}", JSON.toJSONString(req));
        Response<KElevenResponseSoapEnvelope> posExecute = kelevenPosOderRemoteHttpApi.PostSales(req).execute();
        KElevenResponseSoapEnvelope kElevenResponseSoapEnvelope = posExecute.body();
        log.info("K11 POS退单返回参数：{}", JSON.toJSONString(kElevenResponseSoapEnvelope));
        if(kElevenResponseSoapEnvelope != null){
            KElevenResponseSoapBody responseSoapBody = kElevenResponseSoapEnvelope.getBody();
            KElevenPostSalesResult response = responseSoapBody.getResponse().getResult();
            if(response.getResponse() != null && response.getResponse().getResult().getErrorCode() == 0){
                // POS失败钉钉告警，人工处理
                pushPosMsg(JSON.toJSONString(req), JSON.toJSONString(kElevenResponseSoapEnvelope));
            }
        }else{
            pushPosMsg(JSON.toJSONString(req), JSON.toJSONString(kElevenResponseSoapEnvelope));
        }
    }

    private Map<String, Object> initAllRefundParamMap(OrderRefundReq orderRefundReq, String platformMallStoreId, String timestamp, String apiKey, String apiSecret, String interfaceId) {
        OrderReturnReqEntity orderReturnReqEntity = new OrderReturnReqEntity();
        orderReturnReqEntity.setOrder_code(orderRefundReq.getOrdNum());
        orderReturnReqEntity.setReturn_code(orderRefundReq.getRefundNo());
        orderReturnReqEntity.setStore_code(platformMallStoreId);
        orderReturnReqEntity.setType("ALL");
        orderReturnReqEntity.setReturn_amount(orderRefundReq.getRefundAmount());
        orderReturnReqEntity.setSales_time(orderRefundReq.getOrdFinishTime()/1000);
        orderReturnReqEntity.setReturn_at(orderRefundReq.getRefundTime()/1000);

        String content = JsonUtil.toJson(orderReturnReqEntity);
        Map<String, String> params = new TreeMap<>();
        params.put("content", content);
        params.put("timestamp", timestamp);
        params.put("apiKey", apiKey);
        params.put("apiSecret", apiSecret);
        params.put("interfaceId", interfaceId);

        String sign = KElevenGenerateSign.genSignature(params);
        log.info("K11 订单同步生成的签名为：{}",sign);

        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("apiKey", apiKey);
        paramMap.put("interfaceId", interfaceId);
        paramMap.put("timestamp", timestamp);
        paramMap.put("sign",sign);
        paramMap.put("content",content);
        log.info("K11 订单退货退款调用远程接口传输参数：{}", JSON.toJSONString(paramMap));
        return paramMap;
    }

    private OrderPushReqEntity initSubOrderPushReqEntity(OrderRefundReq orderRefundReq, String docNo, BigDecimal amount, String platformMallStoreId,String memberCode,String areaCode) {
        OrderPushReqEntity orderPushReqEntity = new OrderPushReqEntity();
        orderPushReqEntity.setOrder_code(docNo);
        orderPushReqEntity.setSales_time(orderRefundReq.getRefundTime()/1000);
        orderPushReqEntity.setSales_amount(amount);
        orderPushReqEntity.setStore_code(platformMallStoreId);
        orderPushReqEntity.setMember_code(memberCode);
        orderPushReqEntity.setArea_code(areaCode);
        orderPushReqEntity.setOrigin_amount(amount);
        List<OrderPayItemEntity> sales_payments = new ArrayList<>();
        OrderPayItemEntity orderPayItemEntity = new OrderPayItemEntity();

        orderPayItemEntity.setPrice(amount);
        orderPayItemEntity.setKpoint(0.0);
        orderPayItemEntity.setIs_real(1);
        orderPayItemEntity.setType("WECHAT_PAY");
        sales_payments.add(orderPayItemEntity);
        orderPushReqEntity.setSales_payments(sales_payments);
        return orderPushReqEntity;
    }

    private void pushDingMsg(String reqJson, OrderConsumeEntity orderConsumeEntity) throws Exception{
        String msg = "数据推送异常：\n" + "功能：K11订单原单退款成功，创建新单失败" + "\n请求参数: \n" + reqJson + "\n请求结果: \n" + JSON.toJSON(orderConsumeEntity);
        DingDingMsg dingDingMsg = new DingDingMsg();
        Map<String,Object> param = new HashMap<>();
        param.put("content",msg);
        dingDingMsg.setText(param);
        dingDingMsg.setMsgtype("text");
        Response<DingDingResponse> dingDingResponse = dingDingRemoteHttpApi.pushMsg(dingDingMsg).execute();
        log.info("K11订单推送消息返回结果：{}",dingDingResponse.body());
    }

    private void recordNewLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef, String docNo, BigDecimal amount) {
        CashierMallOrderLog logInfo = new CashierMallOrderLog();
        logInfo.setOrdNum(docNo);
        logInfo.setExchangeId(orderRefundReq.getOrdNum());
        logInfo.setExchangeNo(orderRefundReq.getRefundNo());
        logInfo.setBjStoreId(orderRefundReq.getStoreId());
        logInfo.setMallId(assetStoreRef.getPlatformMallId());
        logInfo.setIsCallback("Y");
        logInfo.setIsExecute("Y");
        logInfo.setMallPlatform(assetStoreRef.getApiPlatform());
        logInfo.setMallStoreId(assetStoreRef.getPlatformMallStoreId());
        logInfo.setType(1);
        logInfo.setRefundAmount(amount);
        cashierMallOrderLogMapper.insertAddLog(logInfo);
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderRefundReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setExchangeNo(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setBjStoreId(orderRefundReq.getStoreId());
        cashierMallOrderLog.setMallId(assetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(assetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(assetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(2);
        cashierMallOrderLog.setRefundAmount(orderRefundReq.getRefundAmount());
        cashierMallOrderLogMapper.insertAddLog(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult prepareRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

}
