package com.jnby.mallasset.module.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-07-15 13:35:45
 * @Description: 商场资产规则配置
 */
@Data
@ApiModel(value="CashierMallOrderLog对象", description="订单流水表")
public class CashierMallOrderLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long id;
    @ApiModelProperty(value = "0未删除、1已删除")
    private Integer isDelete;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "商场ID")
    private String mallId;
    @ApiModelProperty(value = "商场门店ID")
    private String mallStoreId;
    @ApiModelProperty(value = "伯俊门店ID")
    private String bjStoreId;
    @ApiModelProperty("商场平台：1-猫酷；2-银泰；3-华润")
    private Integer mallPlatform;
    @ApiModelProperty("订单号")
    private String ordNum;
    @ApiModelProperty("业务ID")
    private String exchangeId;
    @ApiModelProperty("外部单号")
    private String exchangeNo;
    @ApiModelProperty("是否已回调：Y-是；N-否")
    private String isCallback;
    @ApiModelProperty("是否执行成功：Y-是；N-否")
    private String isExecute;
    @ApiModelProperty("订单类型：1-消费；2-售后；3-预退货")
    private Integer type;
    @ApiModelProperty("更新业务ID")
    private String updateExchangeId;
    @ApiModelProperty("更新外部单号")
    private String updateExchangeNo;
    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("优惠金额")
    private BigDecimal promotionAmount;
    @ApiModelProperty("会员类型：1-颐堤港；2-龙湖未注册会员")
    private Integer memberType;
    @ApiModelProperty("实付是否0金额：Y-是；N-否")
    private String isZero;
    @ApiModelProperty("外部订单id")
    private String outerOrderId;
    @ApiModelProperty("交易积分")
    private Double tradeCent;
    @ApiModelProperty("会员id")
    private String memberId;
    @ApiModelProperty("商品数量")
    private Integer qty;
    private int count;
}
