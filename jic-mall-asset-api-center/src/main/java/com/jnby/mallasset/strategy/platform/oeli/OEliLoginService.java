package com.jnby.mallasset.strategy.platform.oeli;

import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import com.jnby.mallasset.constant.MallRedisKeyConstant;
import com.jnby.mallasset.module.mapper.box.CashierMallStoreConfigMapper;
import com.jnby.mallasset.module.model.CashierMallStoreConfig;
import com.jnby.mallasset.remote.huarun.entity.BaseHuaRunResp;
import com.jnby.mallasset.remote.huarun.entity.UserLoginReqEntity;
import com.jnby.mallasset.remote.huarun.entity.UserLoginRespEntity;
import com.jnby.mallasset.remote.oeli.IOeliRemoteHttpApi;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;

/**
 * 天目里用户token
 */
@Service
@AllArgsConstructor
@Slf4j
public class OEliLoginService {

    private IOeliRemoteHttpApi oeliRemoteHttpApi;
    private RedisPoolUtil redisPoolUtil;
    private CashierMallStoreConfigMapper cashierMallStoreConfigMapper;

    public String getUserToken(String storeId) throws IOException {
        String tokenKey = MallRedisKeyConstant.User.TOKEN_KEY_OELI + storeId;
        String token = RedisTemplateUtil.get(redisPoolUtil,tokenKey);
        if(token != null){
            return token;
        }
        // 根据storeId查询用户名、密码
        CashierMallStoreConfig config = cashierMallStoreConfigMapper.selectByStoreId(storeId);
        UserLoginReqEntity req = new UserLoginReqEntity();
        req.setUserName(config.getPlatformPublicKey());
        req.setUserPassWord(config.getPlatformPrivateKey());
        Response<BaseHuaRunResp<UserLoginRespEntity>> execute = oeliRemoteHttpApi.userLogin(req).execute();
        BaseHuaRunResp<UserLoginRespEntity> body = execute.body();
        if(body.isSuccess()){
            token = body.getQuery().getUserToken();
            RedisTemplateUtil.setex(redisPoolUtil, tokenKey, token, MallRedisKeyConstant.Expire.TWO_HOUR);
        }
        return token;
    }
}
