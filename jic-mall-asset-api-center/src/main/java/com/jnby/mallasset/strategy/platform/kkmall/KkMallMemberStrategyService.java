package com.jnby.mallasset.strategy.platform.kkmall;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.remote.kkmall.IKkMallRemoteHttpApi;
import com.jnby.mallasset.remote.kkmall.entity.BaseKkMallResp;
import com.jnby.mallasset.remote.kkmall.entity.KkMallMemberReqEntity;
import com.jnby.mallasset.remote.kkmall.entity.KkMallMemberRespEntity;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.HashMap;
import java.util.Map;

/**
 * 凯德会员平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.KK_MALL, category = PlatformCategoryTypeEnum.MEMBER)
@AllArgsConstructor
@Slf4j
public class KkMallMemberStrategyService extends AbstractMemberService {

    private IKkMallRemoteHttpApi kkMallRemoteHttpApi;
    private KkMallTokenService kkMallTokenService;
    private CashierMallMemberLogMapper cashierMallMemberLogMapper;

    @Override
    public ResponseResult openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef mallAssetStoreRef) {
        log.info("KKMALL注册会员输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            CashierMallMemberLog cashierMallMemberLog = recordMemberLog(memberRegisterReq, mallAssetStoreRef);

            // 先查询会员是否存在
            ResponseResult<KkMallMemberRespEntity> result = getMemberInfo(memberRegisterReq,mallAssetStoreRef);
            CommonMemberResponse commonMemberResponse = new CommonMemberResponse();
            commonResponse.setCode(result.getCode());
            if(result.getCode() == 200 ){
                // 说明是老会员
                KkMallMemberRespEntity kkMallMemberRespEntity = result.getData();
                commonMemberResponse.setMemberFlag(1);
                commonMemberResponse.setMemberId(kkMallMemberRespEntity.getCusotmerId());
                commonResponse.setMsg(result.getMsg());
                commonResponse.setData(commonMemberResponse);
                // 更新执行标识
                cashierMallMemberLog.setOpenUserId(kkMallMemberRespEntity.getMemberCode());
                cashierMallMemberLog.setIsExecute("Y");
                cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
                return commonResponse;
            }

            Map<String, String> header = new HashMap<>();
            String token = kkMallTokenService.getUserToken(memberRegisterReq.getStoreId());
            log.info("KKMALL注册会员获取token：{}", token);
            header.put("X-Access-Token",token);

            KkMallMemberReqEntity req = new KkMallMemberReqEntity();
            req.setMallId(mallAssetStoreRef.getPlatformMallId());
            req.setMobileNo(memberRegisterReq.getMobile());
            log.info("KKMALL注册会员调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseKkMallResp<KkMallMemberRespEntity>> execute = kkMallRemoteHttpApi.registMember(header,req).execute();
            BaseKkMallResp<KkMallMemberRespEntity> body = execute.body();
            commonResponse.setCode(result.getCode());
            commonResponse.setMsg(result.getMsg());
            if(body.getCode() == 0){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                KkMallMemberRespEntity mallMemberRespEntity = body.getResult();
                commonMemberResponse.setMemberFlag(0);
                commonMemberResponse.setMemberId(mallMemberRespEntity.getCusotmerId());
                commonResponse.setData(commonMemberResponse);
            }else {
                commonResponse.setData(body.getResult());
            }
            log.info("KKMAL注册会员返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} KKMALL注册会员异常",memberRegisterReq.getMobile(), e);
        }
        return commonResponse;
    }

    private CashierMallMemberLog recordMemberLog(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef mallAssetStoreRef) {
        String storeId = memberRegisterReq.getStoreId();
        String mobile = memberRegisterReq.getMobile();
        CashierMallMemberLog cashierMallMemberLog = new CashierMallMemberLog();
        cashierMallMemberLog.setMallPlatform(mallAssetStoreRef.getApiPlatform());
        cashierMallMemberLog.setMallId(mallAssetStoreRef.getPlatformMallId());
        cashierMallMemberLog.setMallStoreId(mallAssetStoreRef.getPlatformMallStoreId());
        cashierMallMemberLog.setBjStoreId(storeId);
        cashierMallMemberLog.setMobile(mobile);
        cashierMallMemberLog.setIsExecute("N");
        cashierMallMemberLogMapper.insertEntity(cashierMallMemberLog);
        return cashierMallMemberLog;
    }

    @Override
    public ResponseResult<KkMallMemberRespEntity> getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef mallAssetStoreRef) {
        log.info("KKMALL查询会员输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult<KkMallMemberRespEntity> commonResponse = new ResponseResult<>();
        try{
            Map<String, String> header = new HashMap<>();
            String token = kkMallTokenService.getUserToken(memberRegisterReq.getStoreId());
            log.info("KKMALL查询会员获取token：{}", token);
            header.put("X-Access-Token",token);
            log.info("KKMALL查询会员调用远程接口参数：{}", JSON.toJSONString(memberRegisterReq.getMobile()));
            Response<BaseKkMallResp<KkMallMemberRespEntity>> execute = kkMallRemoteHttpApi.queryMember(header,memberRegisterReq.getMobile()).execute();
            BaseKkMallResp<KkMallMemberRespEntity> body = execute.body();
            if(body.isSuccess() && body.getCode() == 0){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
            }else {
                commonResponse.setCode(body.getCode());
            }
            commonResponse.setMsg(body.getMessage());
            commonResponse.setData(body.getResult());
            log.info("KKMALL查询会员返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} KKMALL查询会员异常",memberRegisterReq.getMobile(), e);
        }
        return commonResponse;
    }
}
