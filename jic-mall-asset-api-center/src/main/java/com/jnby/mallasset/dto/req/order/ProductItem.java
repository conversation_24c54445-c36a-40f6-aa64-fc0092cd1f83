package com.jnby.mallasset.dto.req.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel("商品明细")
public class ProductItem implements Serializable {

    @ApiModelProperty(value = "商品价格")
    private BigDecimal itemPrice;
    @ApiModelProperty(value = "商品规格")
    private String itemNo;
    @ApiModelProperty(value = "商品数量")
    private int itemQty;
    @ApiModelProperty(value = "商品名称")
    private String itemName;
    @ApiModelProperty(value = "商品id")
    private String itemId;
    @ApiModelProperty(value = "外部折扣")
    private BigDecimal itemOuterDis;
}
