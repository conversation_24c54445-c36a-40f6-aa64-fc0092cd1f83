package com.jnby.mallasset.util;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * Base64工具类
 *
 * <AUTHOR>
 * @date 2020/7/14 14:48
 */
public class BASE64Utils {

    private static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;

    /**
     * BASE64解密
     *
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] decodeByBASE64(byte[] key) throws Exception {
        return Base64Tool.decode(key);
    }

    /**
     * BASE64加密
     *
     * @param key
     * @return
     * @throws Exception
     */
    public static String encodeByBASE64(byte[] key) throws Exception {
        return new String(Base64Tool.encode(key));
    }

    /**
     * BASE64编码
     *
     * @param string
     * @return
     * @throws Exception
     */
    public static String encode(String string) {
        if (string == null && string.length() < 1) {
            return string;
        }
        try {
            return new String(Base64Tool.encode(string.getBytes(DEFAULT_CHARSET)));
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * BASE64解码
     *
     * @param string
     * @return
     */
    public static String decode(String string) {
        if (string == null && string.length() < 1) {
            return string;
        }
        try {
            byte[] result = Base64Tool.decode(string.getBytes(DEFAULT_CHARSET));
            return new String(result);
        } catch (Exception e) {
            return null;
        }
    }

}
