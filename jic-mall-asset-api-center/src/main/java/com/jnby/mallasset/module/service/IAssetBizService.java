package com.jnby.mallasset.module.service;

import com.jnby.common.ResponseResult;
import com.jnby.mallasset.api.dto.BaseReq;
import com.jnby.mallasset.api.dto.asset.*;
import com.jnby.mallasset.api.dto.coupon.CouponInfoRespDto;
import com.jnby.mallasset.api.dto.points.PointsInfoRespDto;
import com.jnby.mallasset.dto.req.coupon.CouponSendReqDto;

import java.util.List;

/**
 * 资产业务接口
 *
 * <AUTHOR>
 */
public interface IAssetBizService {
    /**
     * 查询资产集合，包含券和积分
     *
     * @param req 商家、消费者基础信息
     * @return 券列表
     */
    ResponseResult<List<CouponInfoRespDto>> listCoupon(BaseReq req);

    /**
     * 使用资产，包含券和积分
     *
     * @param req 积分、券信息
     * @return 业务ID
     */
    ResponseResult<AssetUseRespDto> useAsset(AssetOperateReqDto req);

    /**
     * 返还资产，包含券和积分
     *
     * @param req 积分、券信息
     * @return 业务ID
     */
    ResponseResult<AssetUseRespDto> returnAsset(AssetOperateReqDto req);

    /**
     * 获取配置
     *
     * @param req 商家
     * @return 配置
     */
    ResponseResult<MallConfigRespDto> getMallConfig(MallConfigReqDto req);

    /**
     * 获取总积分和使用规则
     *
     * @param req 商家
     * @return 积分
     */
    ResponseResult<PointsInfoRespDto> getPoints(BaseReq req);

    /**
     * 判断是否是商城会员
     * @param req 用户信息
     * @return true 会员 false 非会员
     */
    Boolean isMallMember(BaseReq req);

    /**
     * 是否勾选隐私协议
     */
    Boolean isChosePrivacy(BaseReq req);

    /**
     * 提交勾选协议
     */
    ResponseResult<Boolean> submitChosePrivacy(BaseReq req);

    /**
     * 发券
     */
    ResponseResult<AssetUseRespDto> sendCoupon(CouponSendReqDto req);

    /**
     * vid转storeCode，获取不到返回DEFAULT
     * @param vid
     * @return
     */
    String vid2StoreCode(String vid);

    ResponseResult<List<PayConfigRespDto>> listPayConfig(PayConfigReqDto req);

    ResponseResult flushPayConfigCache(String storeId);
}
