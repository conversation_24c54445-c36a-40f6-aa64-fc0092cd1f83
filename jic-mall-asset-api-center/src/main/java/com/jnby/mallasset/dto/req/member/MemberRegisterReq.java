package com.jnby.mallasset.dto.req.member;

import com.jnby.mallasset.api.dto.BaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("会员注册请求入参")
public class MemberRegisterReq extends BaseReq {

    @ApiModelProperty(value = "会员手机号", required = true)
    private String mobile;
    @ApiModelProperty(value = "会员昵称")
    private String nickName;
    @ApiModelProperty(value = "1-男；2-女；0-未知")
    private int sex;
    @ApiModelProperty(value = "是否新会员：Y-是；N-否")
    private String isNew;
}
