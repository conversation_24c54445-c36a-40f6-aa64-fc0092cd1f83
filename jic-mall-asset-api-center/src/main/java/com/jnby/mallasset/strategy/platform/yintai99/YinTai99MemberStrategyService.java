package com.jnby.mallasset.strategy.platform.yintai99;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.remote.yintai99.IYinTai99RemoteHttpApi;
import com.jnby.mallasset.remote.yintai99.entity.YinTai99MemberEntityReq;
import com.jnby.mallasset.remote.yintai99.entity.YinTai99MemberEntityResp;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;

/**
 * 银泰会员平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.YIN_TAI_99, category = PlatformCategoryTypeEnum.MEMBER)
@Slf4j
@AllArgsConstructor
public class YinTai99MemberStrategyService extends AbstractMemberService {

    private CashierMallMemberLogMapper cashierMallMemberLogMapper;

    private IYinTai99RemoteHttpApi yinTai99RemoteHttpApi;

    @Override
    public ResponseResult openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef assetStoreRef) {
        log.info("银泰in99注册会员输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            CashierMallMemberLog cashierMallMemberLog = recordMemberLog(memberRegisterReq, assetStoreRef);
            // 先查询会员是否存在
            ResponseResult<YinTai99MemberEntityResp> result = getMemberInfo(memberRegisterReq,assetStoreRef);
            CommonMemberResponse commonMemberResponse = new CommonMemberResponse();
            if(result.getCode() == ResultCodeEnum.SUCCESS.getCode()){
                // 会员存在直接返回会员信息
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                // 说明是老会员
                YinTai99MemberEntityResp memberQueryRespEntity = result.getData();
                String vipCode = memberQueryRespEntity.getVipBasicInfo().get(0).getVipCode();
                commonMemberResponse.setMemberFlag(1);
                commonMemberResponse.setMemberId(vipCode);
                commonMemberResponse.setMemberCardNo(vipCode);
                commonResponse.setMsg(result.getMsg());
                commonResponse.setData(commonMemberResponse);
                // 更新执行标识
                cashierMallMemberLog.setOpenUserId(vipCode);
                cashierMallMemberLog.setIsExecute("Y");
                cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
                return commonResponse;
            }
            // 注册会员
            registerMemberBusi(memberRegisterReq, assetStoreRef, commonResponse, commonMemberResponse);
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 银泰in99注册会员异常", memberRegisterReq.getMobile(), e);
        }
        return commonResponse;
    }

    private void registerMemberBusi(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef assetStoreRef, ResponseResult commonResponse, CommonMemberResponse commonMemberResponse) throws IOException {
        String integralSkuCode = assetStoreRef.getIntegralSkuCode();
        String[] items = integralSkuCode.split(",");
        String[] apiKeys = items[0].split("#");
        String apiKey = apiKeys[1];
        String[] mediaTypes = items[7].split("#");
        String mediaType = mediaTypes[1];
        String[] mediaIds = items[8].split("#");
        String mediaId = mediaIds[1];
        String[] mallIds = items[9].split("#");
        String mallId = mallIds[1];

        YinTai99MemberEntityReq req = new YinTai99MemberEntityReq();
        req.setApiKey(apiKey);
        req.setSignature("");
        req.setMobileNumber(memberRegisterReq.getMobile());
        req.setMediaType(mediaType);
        req.setMediaId(mediaId);
        req.setMallId(mallId);
        log.info("银泰in99远程注册会员传输参数：{}", JSON.toJSONString(req));
        Response<YinTai99MemberEntityResp> response = yinTai99RemoteHttpApi.registerMember(req).execute();
        YinTai99MemberEntityResp body = response.body();
        log.info("银泰in99远程注册会员返回结果：{}", JSON.toJSONString(body));
        if(body != null && body.getErrorCode() == 0) {
            commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
            commonMemberResponse.setMemberFlag(0);
            commonMemberResponse.setMemberId(body.getVipCode());
            commonMemberResponse.setMemberCardNo(body.getVipCode());
            commonResponse.setMsg(body.getErrorMessage());
            commonResponse.setData(commonMemberResponse);
        }else {
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(body.getErrorMessage());
        }
    }

    private CashierMallMemberLog recordMemberLog(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef mallAssetStoreRef) {
        String storeId = memberRegisterReq.getStoreId();
        String mobile = memberRegisterReq.getMobile();
        CashierMallMemberLog cashierMallMemberLog = new CashierMallMemberLog();
        cashierMallMemberLog.setMallPlatform(mallAssetStoreRef.getApiPlatform());
        cashierMallMemberLog.setMallId(mallAssetStoreRef.getPlatformMallId());
        cashierMallMemberLog.setMallStoreId(mallAssetStoreRef.getPlatformMallStoreId());
        cashierMallMemberLog.setBjStoreId(storeId);
        cashierMallMemberLog.setMobile(mobile);
        cashierMallMemberLog.setIsExecute("N");
        cashierMallMemberLogMapper.insertEntity(cashierMallMemberLog);
        return cashierMallMemberLog;
    }

    @Override
    public ResponseResult<YinTai99MemberEntityResp> getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("银泰in99查询会员输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult<YinTai99MemberEntityResp> commonResponse = new ResponseResult<>();
        try{
            String integralSkuCode = cashierMallAssetStoreRef.getIntegralSkuCode();
            String[] items = integralSkuCode.split(",");
            String[] apiKeys = items[0].split("#");
            String apiKey = apiKeys[1];
            YinTai99MemberEntityReq req = new YinTai99MemberEntityReq();
            req.setApiKey(apiKey);
            req.setSignature("");
            req.setMobileNumber(memberRegisterReq.getMobile());

            log.info("银泰in99远程查询会员传输参数：{}", JSON.toJSONString(req));
            Response<YinTai99MemberEntityResp> response = yinTai99RemoteHttpApi.searchMember(req).execute();
            YinTai99MemberEntityResp body = response.body();
            log.info("银泰in99远程查询会员返回结果：{}", JSON.toJSONString(body));

            if(body != null && body.getErrorCode() == 0) {
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setMsg(body.getErrorMessage());
                commonResponse.setData(body);
            }else {
                commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                commonResponse.setMsg(body.getErrorMessage());
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 银泰in99查询会员异常", memberRegisterReq.getMobile(), e);
        }
        return commonResponse;
    }
}
