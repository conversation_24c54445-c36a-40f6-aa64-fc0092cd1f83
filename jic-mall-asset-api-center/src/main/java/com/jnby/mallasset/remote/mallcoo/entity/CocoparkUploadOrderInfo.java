package com.jnby.mallasset.remote.mallcoo.entity;

import com.jnby.mallasset.remote.kkmall.entity.SalesItem;
import com.jnby.mallasset.remote.kkmall.entity.SalesTender;
import com.jnby.mallasset.remote.kkmall.entity.SalesTotal;
import com.jnby.mallasset.remote.kkmall.entity.TransHeader;
import lombok.Data;

import java.util.List;

@Data
public class CocoparkUploadOrderInfo {

    private String apiKey;

    private String signature;

    private String docKey;

    private TransHeader transHeader;

    private SalesTotal salesTotal;

    private List<SalesItem> salesItem;

    private List<SalesTender> salesTender;
}
