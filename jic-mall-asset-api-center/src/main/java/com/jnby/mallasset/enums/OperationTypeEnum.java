package com.jnby.mallasset.enums;

/**
 * 操作类型枚举
 */
public enum OperationTypeEnum {

    // 操作类型.1=使用，2=返还，3=冻结，4=解冻
    USE(1, "使用"),
    RETURN(2, "返还"),
    FREEZE(3, "冻结"),
    UNFREEZE(4, "解冻"),
    SEND(5, "发放"),
    ;
    /**
     * 编号
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;

    OperationTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
