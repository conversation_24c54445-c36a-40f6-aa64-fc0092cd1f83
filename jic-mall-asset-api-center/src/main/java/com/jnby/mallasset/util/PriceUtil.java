package com.jnby.mallasset.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

public class PriceUtil {
    /**
     * 根据规则取值
     * 当小数点后有值，返回2位小数；没值，返回整数位。例如"1.01",返回"1.01"；"1.00"返回"1"
     * @param value 0.00
     * @return 0
     */
    public static String formatString(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }
        BigDecimal decimal = new BigDecimal(value);
        if (decimal.scale() > 0 && decimal.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0) {
            return decimal.setScale(0, BigDecimal.ROUND_DOWN).toString();
        } else {
            return value;
        }
    }
}
