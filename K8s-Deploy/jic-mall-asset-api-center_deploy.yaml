apiVersion: apps/v1
kind: Deployment
metadata:
  name: jic-mall-asset-api-center
  labels:
    app: jic-mall-asset-api-center
spec:
  replicas: 1
  template:
    metadata:
      name: jic-mall-asset-api-center
      labels:
        app: jic-mall-asset-api-center
    spec:
      containers:
        - name: jic-mall-asset-api-center
          image: "harbor.jnby.com/jic-mall-asset/jic-mall-asset-api-center:latest"
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 9601
              name: asset-center
              protocol: TCP
          lifecycle:
            preStop:
              exec:
                command:
                  - sh
                  - c
                  - "sleep 5"
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 20
            successThreshold: 1
            tcpSocket:
              port: 9601
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 20
            successThreshold: 1
            tcpSocket:
              port: 9601
            timeoutSeconds: 1
      restartPolicy: Always
      imagePullSecrets:
        - name: 152harbor

  selector:
    matchLabels:
      app: jic-mall-asset-api-center

