package com.jnby.mallasset.remote.xiexin.entity.vip;

import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Root;

import java.util.List;

@Data
@Root(name = "vippoint", strict = false)
public class XiexinVipPoint {

    @ElementList(entry = "VipPoint",inline = true,required = false)
    private List<VipPoint> vipPointInfo;
}
