package com.jnby.mallasset.remote.haiancheng.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 文件名: com.jnby.mallasset.remote.haiancheng.entity-HacOrderReqEntity.java
 * 文件简介: 订单请求参数
 *
 * <AUTHOR>
 * @date 2025/1/13 14:19
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class HacOrderCreateReqEntity implements Serializable {
    @ApiModelProperty(value = "会员ID", required = true)
    private String memberId;
    @ApiModelProperty(value = "订单号", required = true)
    private String thirdPartyOrderNo;
    @ApiModelProperty(value = "商品编码", required = true)
    private String skuCode;
    @ApiModelProperty(value = "专柜商铺ID", required = true)
    private Integer storeId;
    @ApiModelProperty(value = "消费金额", required = true)
    private BigDecimal totalPaidAmount;
    @ApiModelProperty(value = "商品数量", required = true)
    private Integer totalBuyQty;
}
