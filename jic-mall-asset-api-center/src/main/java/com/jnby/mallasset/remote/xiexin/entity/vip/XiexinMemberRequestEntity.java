package com.jnby.mallasset.remote.xiexin.entity.vip;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DrcMemberRegisterReqEntity.java
 * 文件简介: 订单消费请求
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class XiexinMemberRequestEntity implements Serializable {
    @ApiModelProperty(value = "应用标识", required = true)
    private String app_no;
    @ApiModelProperty(value = "请求时间", required = true)
    private String timestamp;
    @ApiModelProperty(value = "门店id", required = true)
    private String store_id;
    @ApiModelProperty(value = "手机号", required = true)
    private String mobile;
    @ApiModelProperty(value = "随机字符串", required = true)
    private String nonce_str;
    @ApiModelProperty(value = "签名", required = true)
    private String sign;
}
