package com.jnby.mallasset.strategy.platform.guangHuan;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.remote.guangHuan.IGuangHuanRemoteHttpApi;
import com.jnby.mallasset.remote.guangHuan.entity.BaseGuangHuanResp;
import com.jnby.mallasset.remote.guangHuan.entity.MemberReqEntity;
import com.jnby.mallasset.remote.guangHuan.entity.MemberRespEntity;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.HashMap;
import java.util.Map;

/**
 * 凯德会员平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.GUANG_HUAN, category = PlatformCategoryTypeEnum.MEMBER)
@AllArgsConstructor
@Slf4j
public class GuangHuanMemberStrategyService extends AbstractMemberService {

    private CashierMallMemberLogMapper cashierMallMemberLogMapper;
    private IGuangHuanRemoteHttpApi guangHuanRemoteHttpApi;

    @Override
    public ResponseResult openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef mallAssetStoreRef) {
        log.info("重庆光环注册会员输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            CashierMallMemberLog cashierMallMemberLog = recordMemberLog(memberRegisterReq, mallAssetStoreRef);

            // 先查询会员是否存在
            ResponseResult<MemberRespEntity> result = getMemberInfo(memberRegisterReq,mallAssetStoreRef);
            CommonMemberResponse commonMemberResponse = new CommonMemberResponse();
            commonResponse.setCode(result.getCode());
            if(result.getCode() == 200 ){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                // 说明是老会员
                MemberRespEntity memberRespEntity = result.getData();
                commonMemberResponse.setMemberFlag(1);
                commonMemberResponse.setMemberId(memberRespEntity.getId());
                commonResponse.setMsg(result.getMsg());
                commonResponse.setData(commonMemberResponse);
                // 更新执行标识
                cashierMallMemberLog.setOpenUserId(memberRespEntity.getId());
                cashierMallMemberLog.setIsExecute("Y");
                cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
                return commonResponse;
            }

            Map<String, String> header = new HashMap<>();
            header.put("Authorization",mallAssetStoreRef.getPlatformPublicKey());

            MemberReqEntity req = new MemberReqEntity();
            String integralSkuCode = mallAssetStoreRef.getIntegralSkuCode();
            String[] items = integralSkuCode.split(",");
            String[] expandingChannels = items[0].split("-");
            String expandingChannel = expandingChannels[1];
            String[] fromOrgIds = items[1].split("-");
            String fromOrgId = fromOrgIds[1];
            req.setExpandingChannel(expandingChannel);
            req.setFromOrgId(fromOrgId);
            req.setMobileNo(memberRegisterReq.getMobile());
            log.info("重庆光环注册会员调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseGuangHuanResp<String>> execute = guangHuanRemoteHttpApi.memberOpenCard(header,req).execute();
            BaseGuangHuanResp<String> body = execute.body();
            commonResponse.setCode(result.getCode());
            commonResponse.setMsg(result.getMsg());
            if(body.getStatus().equals("0000")){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                ResponseResult<MemberRespEntity> memberInfo = getMemberInfo(memberRegisterReq,mallAssetStoreRef);
                if(memberInfo.getCode() == 200 ){
                    MemberRespEntity memberRespEntity = memberInfo.getData();
                    log.info("重庆光环注册会员成功查询信息：{}", JSON.toJSONString(memberRespEntity));
                    commonMemberResponse.setMemberFlag(0);
                    commonMemberResponse.setMemberId(memberRespEntity.getId());
                }
                commonResponse.setData(commonMemberResponse);
            }else {
                commonResponse.setData(body.getData());
            }
            log.info("重庆光环注册会员返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 重庆光环注册会员异常",memberRegisterReq.getMobile(), e);
        }
        return commonResponse;
    }

    private CashierMallMemberLog recordMemberLog(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef mallAssetStoreRef) {
        String storeId = memberRegisterReq.getStoreId();
        String mobile = memberRegisterReq.getMobile();
        CashierMallMemberLog cashierMallMemberLog = new CashierMallMemberLog();
        cashierMallMemberLog.setMallPlatform(mallAssetStoreRef.getApiPlatform());
        cashierMallMemberLog.setMallId(mallAssetStoreRef.getPlatformMallId());
        cashierMallMemberLog.setMallStoreId(mallAssetStoreRef.getPlatformMallStoreId());
        cashierMallMemberLog.setBjStoreId(storeId);
        cashierMallMemberLog.setMobile(mobile);
        cashierMallMemberLog.setIsExecute("N");
        cashierMallMemberLogMapper.insertEntity(cashierMallMemberLog);
        return cashierMallMemberLog;
    }

    @Override
    public ResponseResult<MemberRespEntity> getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef mallAssetStoreRef) {
        log.info("重庆光环查询会员输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult<MemberRespEntity> commonResponse = new ResponseResult<>();
        try{
            Map<String, String> header = new HashMap<>();
            header.put("Authorization",mallAssetStoreRef.getPlatformPublicKey());
            log.info("重庆光环查询会员调用远程接口参数：{}", JSON.toJSONString(memberRegisterReq.getMobile()));
            Response<BaseGuangHuanResp<MemberRespEntity>> execute = guangHuanRemoteHttpApi.memberInfo(header,memberRegisterReq.getMobile()).execute();
            BaseGuangHuanResp<MemberRespEntity> body = execute.body();
            if(body.getStatus().equals("0000")){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
            }else {
                commonResponse.setCode(Integer.parseInt(body.getStatus()));
            }
            commonResponse.setMsg(body.getMessage());
            commonResponse.setData(body.getData());
            log.info("重庆光环查询会员返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 重庆光环查询会员异常",memberRegisterReq.getMobile(), e);
        }
        return commonResponse;
    }
}
