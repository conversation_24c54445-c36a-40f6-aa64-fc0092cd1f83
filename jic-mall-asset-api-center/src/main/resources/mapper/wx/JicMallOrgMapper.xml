<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.mallasset.module.mapper.wx.JicMallOrgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.mallasset.module.model.wx.JicMallOrg">
        <id column="ID" property="id" />
        <result column="VID" property="vid" />
        <result column="BRAND_ID" property="brandId" />
        <result column="INSIDE_ID" property="insideId" />
        <result column="VID_TYPE" property="vidType" />
        <result column="VID_CODE" property="vidCode" />
        <result column="VID_NAME" property="vidName" />
        <result column="PARENT_VID" property="parentVid" />
        <result column="VID_STATUS" property="vidStatus" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VID, BRAND_ID, INSIDE_ID, VID_TYPE, VID_CODE, VID_NAME, PARENT_VID, VID_STATUS, CREATE_TIME, UPDATE_TIME
    </sql>

</mapper>