package com.jnby.mallasset.config;

import com.jnby.mallasset.intercept.AuthInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    public static String ENV = "prod";
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new AuthInterceptor())
                // 可以添加特定的路径模式来限制拦截器的作用范围
                .addPathPatterns("/**")
                // 排除某些路径
                .excludePathPatterns(
                        "/test/**",
                        "/**/order/callback",
                        // swagger start ---
                        "/webjars/**",
                        "/swagger-resources/**",
                        "/doc.html"
                        // swagger end ---
                );
    }

    @Value("${spring.profiles.active}")
    public void setEnv(String env) {
        System.out.println("当前环境：" + env);
        ENV = env;
    }
}
