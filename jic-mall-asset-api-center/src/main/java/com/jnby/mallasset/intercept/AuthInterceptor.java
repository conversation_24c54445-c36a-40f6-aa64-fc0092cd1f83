package com.jnby.mallasset.intercept;

import com.jnby.mallasset.config.exception.MallException;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Base64;

@Component
public class AuthInterceptor implements HandlerInterceptor {
    private static final String NAME = "jnby";
    private static final String PASSWORD = "Jnby@1994";
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String auth = request.getHeader("auth");
        if (StringUtils.isBlank(auth)) {
            throw new MallException(SystemErrorEnum.AUTH_ERROR);
        }
        if (!passAuth(auth)) {
            throw new MallException(SystemErrorEnum.AUTH_INVALID_ERROR);
        }
        return true;
    }



    public static boolean passAuth(String base64Auth) {
        if (StringUtils.isBlank(base64Auth)) {
            return false;
        }
        Base64.Decoder decoder = Base64.getDecoder();
        byte[] decode = decoder.decode(base64Auth);
        String auth = new String(decode);
        String[] split = auth.split(":");
        if (split.length != 2) {
            return false;
        }
        String username = split[0];
        String password = split[1];
        if (!NAME.equals(username) || !PASSWORD.equals(password)) {
            return false;
        }
        return true;
    }

//    public static void main(String[] args) {
//        String base64Auth = "am5ieTpKbmJ5QDE5OTQ=";
//        System.out.println(AuthInterceptor.passAuth(base64Auth));
//    }
}
