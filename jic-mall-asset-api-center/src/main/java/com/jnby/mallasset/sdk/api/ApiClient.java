package com.jnby.mallasset.sdk.api;

import com.jnby.mallasset.sdk.model.WhaleResponse;
import com.jnby.mallasset.sdk.util.PostUtil;

/**
 * <AUTHOR>
 */
public class ApiClient implements Api {

    private PostUtil postUtil = new PostUtil();
    private String host;

    private ApiClient() {
    }

    public ApiClient(String _host) {
        _host = _host.endsWith("/") ? _host.substring(0, _host.length() - 1) : _host;
        this.host = _host;
    }

    @Override
    public String test() {
        return postUtil.doGet(host + ApiUrl.Test);
    }

    @Override
    public WhaleResponse cameraFootfall(int categoryType, String body, String appId, String secret, String iv) {
        return postUtil.doPost(host + ApiUrl.CameraFootfall, categoryType, body, appId, secret, iv);
    }

    @Override
    public WhaleResponse carParking(int categoryType, String body, String appId, String secret, String iv) {
        return postUtil.doPost(host + ApiUrl.CarParking, categoryType, body, appId, secret, iv);
    }

    @Override
    public WhaleResponse crm(int categoryType, String body, String appId, String secret, String iv) {
        return postUtil.doPost(host + ApiUrl.Crm, categoryType, body, appId, secret, iv);
    }

    @Override
    public WhaleResponse pos(int categoryType, String body, String appId, String secret, String iv) {
        return postUtil.doPost(host + ApiUrl.Pos, categoryType, body, appId, secret, iv);
    }

    @Override
    public WhaleResponse wifi(int categoryType, String body, String appId, String secret, String iv) {
        return postUtil.doPost(host + ApiUrl.Wifi, categoryType, body, appId, secret, iv);
    }

    @Override
    public WhaleResponse application(int categoryType, String body, String appId, String secret, String iv) {
        return postUtil.doPost(host + ApiUrl.Application, categoryType, body, appId, secret, iv);
    }

    @Override
    public WhaleResponse tag(int categoryType, String body, String appId, String secret, String iv) {
        return postUtil.doPost(host + ApiUrl.Tag, categoryType, body, appId, secret, iv);
    }

    @Override
    public WhaleResponse biEvent(int categoryType, String body, String appId, String secret, String iv) {
        return postUtil.doPost(host + ApiUrl.BiEvent, categoryType, body, appId, secret, iv);
    }
}
