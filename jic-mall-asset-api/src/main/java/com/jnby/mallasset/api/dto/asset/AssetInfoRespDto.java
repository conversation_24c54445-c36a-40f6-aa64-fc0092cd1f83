package com.jnby.mallasset.api.dto.asset;

import com.jnby.mallasset.api.dto.coupon.CouponInfoRespDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("资产列表回参")
@Data
public class AssetInfoRespDto implements Serializable {
    @ApiModelProperty("可用积分")
    private Integer canUsePoints;
    @ApiModelProperty("可用券列表")
    private List<CouponInfoRespDto> canUseCouponList;
}
