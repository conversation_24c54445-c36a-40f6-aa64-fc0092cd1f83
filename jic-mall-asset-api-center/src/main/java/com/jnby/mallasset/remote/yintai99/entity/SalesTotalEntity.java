package com.jnby.mallasset.remote.yintai99.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SalesTotalEntity implements Serializable {
    @ApiModelProperty("收銀員編號")
    private String cashier;
    @ApiModelProperty("會員編號")
    private String vipCode;
    @ApiModelProperty("淨銷售數量")
    private int netQty;
    @ApiModelProperty("淨銷售金額（實收金額）")
    private BigDecimal netAmount;
    @ApiModelProperty("擴展參數")
    private String extendParameter;
    @ApiModelProperty("預留字段")
    private String calculateVipBonus;
}
