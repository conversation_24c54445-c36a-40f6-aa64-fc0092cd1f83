package com.jnby.mallasset.dto.req;

import com.jnby.mallasset.dto.res.Query;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("订单回调请求")
public class OrderCallBackRequest {

    @ApiModelProperty(value = "结果")
    private int result;
    @ApiModelProperty(value = "返回消息")
    private String msg;
    @ApiModelProperty(value = "返回时间")
    private String returnTime;
    @ApiModelProperty(value = "返回数据")
    private Query query;
}
