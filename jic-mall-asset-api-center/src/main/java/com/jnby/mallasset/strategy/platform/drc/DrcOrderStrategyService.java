package com.jnby.mallasset.strategy.platform.drc;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.remote.dingding.IDingDingRemoteHttpApi;
import com.jnby.mallasset.remote.dingding.entity.DingDingMsg;
import com.jnby.mallasset.remote.dingding.entity.DingDingResponse;
import com.jnby.mallasset.remote.drc.IDrcRemoteHttpApi;
import com.jnby.mallasset.remote.drc.entity.BaseDrcDataResp;
import com.jnby.mallasset.remote.drc.entity.BaseDrcResp;
import com.jnby.mallasset.remote.drc.entity.DrcOrderReqEntity;
import com.jnby.mallasset.remote.drc.entity.DrcOrderRespEntity;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.DateUtil;
import com.jnby.mallasset.util.OrderNoUtils;
import com.jnby.mallasset.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 大融城订单平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.DA_RONG_CHENG, category = PlatformCategoryTypeEnum.ORDER)
@Slf4j
@AllArgsConstructor
public class DrcOrderStrategyService extends AbstractOrderService {

    private IDrcRemoteHttpApi drcRemoteHttpApi;
    private CashierMallOrderLogMapper cashierMallOrderLogMapper;
    private DrcAccessTokenService drcAccessTokenService;
    private IDingDingRemoteHttpApi dingDingRemoteHttpApi;

    @Override
    public ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("大融城订单同步输入参数：{}", JSON.toJSONString(orderConsumeReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordMallOrderLog(orderConsumeReq, cashierMallAssetStoreRef);

            String accessToken = drcAccessTokenService.getAccessToken(orderConsumeReq.getStoreId());
            Map<String, String> header = new HashMap<>();
            header.put("token",accessToken);

            DrcOrderReqEntity req = new DrcOrderReqEntity();
            req.setMall_id(cashierMallAssetStoreRef.getPlatformMallId());
            req.setMember_phone(orderConsumeReq.getMemberTel());
            req.setMember_code(orderConsumeReq.getOuterMemberId());
            req.setStore_code(cashierMallAssetStoreRef.getPlatformMallStoreId());
            req.setSales_amount(orderConsumeReq.getOrdAmount());
            req.setDoc_no(orderConsumeReq.getOrdNum());
            req.setSales_date_time(DateUtil.timeStamp3Date(orderConsumeReq.getOrdFinishTime(),null));
            req.setPayway("微信");
            req.setData_source("CRM");
            req.setUu_id(StringUtil.randomUUID());
            req.setOperators(cashierMallAssetStoreRef.getPlatformAppId());
            log.info("大融城订单同步调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseDrcResp<BaseDrcDataResp<DrcOrderRespEntity>>> execute = drcRemoteHttpApi.orderSales(header,req).execute();
            BaseDrcResp<BaseDrcDataResp<DrcOrderRespEntity>> body = execute.body();
            commonResponse.setMsg(body.getMsg());
            if(body.getCode() == 200){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());

                BaseDrcDataResp<DrcOrderRespEntity> baseDrcDataResp = body.getResult();
                DrcOrderRespEntity drcOrderRespEntity = baseDrcDataResp.getData();
                drcOrderRespEntity.setOrdNum(orderConsumeReq.getOrdNum());
                drcOrderRespEntity.setExchangeId(orderConsumeReq.getOrdNum());
                drcOrderRespEntity.setExchangeNo(orderConsumeReq.getOrdNum());
                commonResponse.setData(drcOrderRespEntity);
                // 更新返回的参数
                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
            }else {
                commonResponse.setCode(body.getCode());
            }
            log.info("大融城订单同步返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 大融城订单同步失败",orderConsumeReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    private CashierMallOrderLog recordMallOrderLog(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeNo(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setBjStoreId(orderConsumeReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(1);
        cashierMallOrderLog.setRefundAmount(orderConsumeReq.getOrdAmount());
        cashierMallOrderLogMapper.insertAddLog(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult pushRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("大融城订单退货退款输入参数：{}", JSON.toJSONString(orderRefundReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordRefundMallOrderLog(orderRefundReq, cashierMallAssetStoreRef);

            // 判断退款金额和总金额是否相等
            BigDecimal refundAmount = orderRefundReq.getRefundAmount();
            BigDecimal ordAmount = orderRefundReq.getOrdAmount();
            CashierMallOrderLog orderLog = cashierMallOrderLogMapper.selectOrderLogByOrderNumAndExchangeId(orderRefundReq.getOrdNum(),orderRefundReq.getExchangeId());
            if(ordAmount.compareTo(refundAmount) == 0){
                String accessToken = drcAccessTokenService.getAccessToken(orderRefundReq.getStoreId());
                Map<String, String> header = new HashMap<>();
                header.put("token",accessToken);

                DrcOrderReqEntity req = refundAllDrcOrderReqEntity(orderRefundReq, cashierMallAssetStoreRef, refundAmount, orderLog);
                log.info("大融城订单整单退款调用远程接口参数：{}", JSON.toJSONString(req));
                Response<BaseDrcResp<BaseDrcDataResp<DrcOrderRespEntity>>> execute = drcRemoteHttpApi.orderSales(header,req).execute();
                BaseDrcResp<BaseDrcDataResp<DrcOrderRespEntity>> body = execute.body();
                commonResponse.setMsg(body.getMsg());
                if(body.getCode() == 200){
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    BaseDrcDataResp<DrcOrderRespEntity> baseDrcDataResp = body.getResult();
                    DrcOrderRespEntity drcOrderRespEntity = baseDrcDataResp.getData();
                    drcOrderRespEntity.setOrdNum(orderRefundReq.getOrdNum());
                    drcOrderRespEntity.setExchangeId(orderRefundReq.getRefundNo());
                    drcOrderRespEntity.setExchangeNo(orderRefundReq.getRefundNo());
                    commonResponse.setData(drcOrderRespEntity);
                    // 更新返回的参数
                    cashierMallOrderLog.setIsExecute("Y");
                    cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
                }else {
                    commonResponse.setCode(body.getCode());
                }
                log.info("大融城订单整单退货返回参数：{}", JSON.toJSONString(commonResponse));
            }else {
                // 部分退逻辑,先退上笔单的总额，再创建剩余金额的新单
                String accessToken = drcAccessTokenService.getAccessToken(orderRefundReq.getStoreId());
                Map<String, String> header = new HashMap<>();
                header.put("token",accessToken);
                DrcOrderReqEntity req = new DrcOrderReqEntity();
                req.setMember_phone(orderRefundReq.getMemberTel());
                req.setMember_code(orderRefundReq.getOuterMemberId());
                req.setStore_code(cashierMallAssetStoreRef.getPlatformMallStoreId());
                req.setSales_amount(orderLog.getRefundAmount().multiply(new BigDecimal(-1)));
                req.setDoc_no(orderRefundReq.getRefundNo());
                req.setOradoc_no(orderLog.getExchangeNo());
                req.setSales_date_time(DateUtil.timeStamp3Date(orderRefundReq.getOrdFinishTime(),null));
                req.setPayway("微信");
                req.setData_source("CRM");
                req.setUu_id(StringUtil.randomUUID());
                req.setMall_id(cashierMallAssetStoreRef.getPlatformMallId());
                req.setOperators(cashierMallAssetStoreRef.getPlatformAppId());
                log.info("大融城订单部分退款调用远程接口参数：{}", JSON.toJSONString(req));
                Response<BaseDrcResp<BaseDrcDataResp<DrcOrderRespEntity>>> execute = drcRemoteHttpApi.orderSales(header,req).execute();
                BaseDrcResp<BaseDrcDataResp<DrcOrderRespEntity>> body = execute.body();
                log.info("大融城订单部分退款调用返回参数：{}", JSON.toJSONString(body));
                commonResponse.setMsg(body.getMsg());
                if(body.getCode() == 200){
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    BaseDrcDataResp<DrcOrderRespEntity> baseDrcDataResp = body.getResult();
                    DrcOrderRespEntity drcOrderRespEntity = baseDrcDataResp.getData();
                    drcOrderRespEntity.setOrdNum(orderRefundReq.getOrdNum());
                    drcOrderRespEntity.setExchangeId(orderRefundReq.getRefundNo());
                    drcOrderRespEntity.setExchangeNo(orderRefundReq.getRefundNo());
                    commonResponse.setData(drcOrderRespEntity);
                    // 更新返回的参数
                    cashierMallOrderLog.setIsExecute("Y");
                    cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
                }else {
                    commonResponse.setCode(body.getCode());
                }

                if(commonResponse.getCode() == 200){
                    // 创建一笔新单,查询剩余金额
                    CashierMallOrderLog mallOrderLog = cashierMallOrderLogMapper.selectSumRefundAmount(orderRefundReq.getOrdNum());
                    BigDecimal refundAmountSum = mallOrderLog.getRefundAmount();
                    int isNeedCreate = ordAmount.subtract(refundAmountSum).compareTo(BigDecimal.ZERO);
                    if(isNeedCreate != 0){
                        Long seq = cashierMallOrderLogMapper.selectDrcOrderNoSeq();
                        String docNo = "RE" + OrderNoUtils.generalOrderNo(seq);
                        // 不为0需要新建订单
                        BigDecimal amount = ordAmount.subtract(refundAmountSum);
                        CashierMallOrderLog logInfo = new CashierMallOrderLog();
                        logInfo.setOrdNum(docNo);
                        logInfo.setExchangeId(orderRefundReq.getOrdNum());
                        logInfo.setExchangeNo(orderRefundReq.getRefundNo());
                        logInfo.setBjStoreId(orderRefundReq.getStoreId());
                        logInfo.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
                        logInfo.setIsCallback("Y");
                        logInfo.setIsExecute("Y");
                        logInfo.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
                        logInfo.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
                        logInfo.setType(1);
                        logInfo.setRefundAmount(BigDecimal.ZERO);
                        cashierMallOrderLogMapper.insertEntity(logInfo);

                        DrcOrderReqEntity orderReqEntity = new DrcOrderReqEntity();
                        orderReqEntity.setMember_phone(orderRefundReq.getMemberTel());
                        orderReqEntity.setMember_code(orderRefundReq.getOuterMemberId());
                        orderReqEntity.setStore_code(cashierMallAssetStoreRef.getPlatformMallStoreId());
                        orderReqEntity.setSales_amount(amount);
                        orderReqEntity.setDoc_no(docNo);
                        orderReqEntity.setSales_date_time(DateUtil.timeStamp3Date(orderRefundReq.getOrdFinishTime(),null));
                        orderReqEntity.setPayway("微信");
                        orderReqEntity.setData_source("CRM");
                        orderReqEntity.setUu_id(StringUtil.randomUUID());
                        orderReqEntity.setMall_id(cashierMallAssetStoreRef.getPlatformMallId());
                        orderReqEntity.setOperators(cashierMallAssetStoreRef.getPlatformAppId());
                        log.info("大融城订单同步创建新订单调用远程接口参数：{}", JSON.toJSONString(orderReqEntity));
                        Response<BaseDrcResp<BaseDrcDataResp<DrcOrderRespEntity>>> response = drcRemoteHttpApi.orderSales(header,orderReqEntity).execute();
                        BaseDrcResp<BaseDrcDataResp<DrcOrderRespEntity>> resp = response.body();
                        log.info("大融城订单同步创建新订单返回参数：{}", JSON.toJSONString(resp));
                        commonResponse.setMsg(resp.getMsg());
                        if(body.getCode() == 200){
                            // 更新原单的exchangeNo
                            CashierMallOrderLog mallOrderLog1 = new CashierMallOrderLog();
                            mallOrderLog1.setOrdNum(orderRefundReq.getOrdNum());
                            mallOrderLog1.setExchangeNo(docNo);
                            mallOrderLog1.setExchangeId(orderRefundReq.getExchangeId());
                            mallOrderLog1.setRemark(ResultCodeEnum.SUCCESS.getMsg());
                            mallOrderLog1.setRefundAmount(amount);
                            cashierMallOrderLogMapper.updateExchangeNoWithParam(mallOrderLog1);

                            logInfo.setRemark(ResultCodeEnum.SUCCESS.getMsg());
                            cashierMallOrderLogMapper.updateOrderLogByParam(logInfo);
                        }else {
                            commonResponse.setCode(body.getCode());
                            commonResponse.setMsg("大融城原单已完成部分退款，但创建新单失败：" + resp.getMsg());
                            // 推送钉钉告警
                            pushDingDingMsg(orderReqEntity, resp);
                        }
                    }
                }
            }
            log.info("大融城订单退货退款返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(SystemErrorEnum.UNKNOWN_ERROR.getErrorMsg());
            log.error("{} 大融城订单退货退款失败",orderRefundReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    private void pushDingDingMsg(DrcOrderReqEntity orderReqEntity, BaseDrcResp<BaseDrcDataResp<DrcOrderRespEntity>> resp) throws IOException {
        String msg = "数据推送异常：\n" + "功能：大融城订单原单退款成功，创建新单失败" + "\n请求参数: \n" + JSON.toJSON(orderReqEntity) + "\n请求结果: \n" + JSON.toJSON(resp);
        DingDingMsg dingDingMsg = new DingDingMsg();
        Map<String,Object> param = new HashMap<>();
        param.put("content",msg);
        dingDingMsg.setText(param);
        dingDingMsg.setMsgtype("text");
        Response<DingDingResponse> dingDingResponse = dingDingRemoteHttpApi.pushMsg(dingDingMsg).execute();
        log.info("大融城订单推送消息返回结果：{}",dingDingResponse.body());
    }

    private DrcOrderReqEntity refundAllDrcOrderReqEntity(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef, BigDecimal refundAmount, CashierMallOrderLog orderLog) {
        DrcOrderReqEntity req = new DrcOrderReqEntity();
        req.setMember_phone(orderRefundReq.getMemberTel());
        req.setMember_code(orderRefundReq.getOuterMemberId());
        req.setStore_code(cashierMallAssetStoreRef.getPlatformMallStoreId());
        req.setSales_amount(refundAmount.multiply(new BigDecimal(-1)));
        req.setDoc_no(orderRefundReq.getRefundNo());
        req.setOradoc_no(orderLog.getExchangeNo());
        req.setSales_date_time(DateUtil.timeStamp3Date(orderRefundReq.getOrdFinishTime(),null));
        req.setPayway("微信");
        req.setData_source("CRM");
        req.setUu_id(StringUtil.randomUUID());
        req.setMall_id(cashierMallAssetStoreRef.getPlatformMallId());
        req.setOperators(cashierMallAssetStoreRef.getPlatformAppId());
        return req;
    }


    private CashierMallOrderLog recordRefundMallOrderLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderRefundReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setExchangeNo(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setBjStoreId(orderRefundReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(2);
        cashierMallOrderLog.setRefundAmount(orderRefundReq.getRefundAmount());
        cashierMallOrderLogMapper.insertAddLog(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult prepareRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

}
