package com.jnby.mallasset.sdk.util;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR>
 */
public class AesUtil {

    /**
     * 加密
     *
     * @param input
     * @param key
     * @param iv
     * @return
     * @throws Exception
     */
    public static String aesEncrypt(String input, String key, String iv) {
        if (key == null) {
            return "Key不能为空";
        }
        if (key.length() != 16) {
            return "Key需要16位长度";
        }

        String result = null;
        try {
            byte[] raw = key.getBytes("utf-8");
            SecretKeySpec keySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes("utf-8"));
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivParameterSpec);
            byte[] encrypted = cipher.doFinal(input.getBytes("utf-8"));
            //将+替换为%2B
            /*.replace("+", "%2B")*/
//            result = new BASE64Encoder().encode(encrypted);
            //换行符问题
            result = org.apache.commons.codec.binary.Base64.encodeBase64String(encrypted);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 解密
     *
     * @param input
     * @param key
     * @param iv
     * @return
     * @throws Exception
     */
    public static String aesDecrypt(String input, String key, String iv) {

        if (key == null) {
            return "Key不能为空";
        }
        if (key.length() != 16) {
            return "Key需要16位长度";
        }
        String result = null;
        try {
            byte[] raw = key.getBytes("utf-8");
            SecretKeySpec keySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes("utf-8"));

            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivParameterSpec);
            //byte[] encrypted = cipher.doFinal(new BASE64Decoder().decodeBuffer(input));
            byte[] encrypted = cipher.doFinal(org.apache.commons.codec.binary.Base64.decodeBase64(input));
            result = new String(encrypted, "utf-8");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }
}
