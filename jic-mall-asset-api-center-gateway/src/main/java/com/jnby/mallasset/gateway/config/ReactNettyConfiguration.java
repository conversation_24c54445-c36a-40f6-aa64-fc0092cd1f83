package com.jnby.mallasset.gateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorResourceFactory;

@Configuration
public class ReactNettyConfiguration {

    @Bean
    public ReactorResourceFactory reactorClientResourceFactory() {
        System.setProperty("reactor.netty.ioSelectCount","1");

        // 这里工作线程数为2-4倍都可以。看具体情况
        int ioWorkerCount = Math.max(Runtime.getRuntime().availableProcessors()*100, 4);
        System.setProperty("reactor.netty.ioWorkerCount",String.valueOf(ioWorkerCount));
        return new ReactorResourceFactory();
    }

    public static void main(String[] args) {
        int ioWorkerCount = Math.max(Runtime.getRuntime().availableProcessors()*100, 4);
        System.out.println(ioWorkerCount);
    }
}
