package com.jnby.mallasset.strategy.category;

import com.jnby.mallasset.api.dto.coupon.CouponInfoRespDto;
import com.jnby.mallasset.strategy.context.CouponContext;
import com.jnby.mallasset.strategy.context.UserContext;

import java.util.List;

public abstract class AbstractCouponService extends AbstractMallService implements IBaseCouponService, ICouponAssetTemplate {

    @Override
    public Boolean useCoupon(UserContext user, CouponContext req) {
        checkUser(user);
        preCheckUseCoupon(user, req);
        doUseCoupon(user, req);
        return true;
    }

    @Override
    public Boolean returnCoupon(UserContext user, CouponContext req) {
        checkUser(user);
        preCheckReturnCoupon(user, req);
        doReturnCoupon(user, req);
        return true;
    }

    @Override
    public List<CouponInfoRespDto> getCanUseCouponList(UserContext user) {
        checkUser(user);
        return doList(user);
    }

    @Override
    public Boolean sendCoupon(UserContext user, CouponContext req) {
        checkUser(user);
        preCheckSendCoupon(user, req);
        doSendCoupon(user, req);
        return true;
    }
}
