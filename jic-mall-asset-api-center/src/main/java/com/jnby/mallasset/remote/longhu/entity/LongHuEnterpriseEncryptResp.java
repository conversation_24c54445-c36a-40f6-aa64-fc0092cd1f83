package com.jnby.mallasset.remote.longhu.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 龙湖企业端加密响应实体
 *
 * <AUTHOR>
 * @date 2025/6/9 18:03
 */
@Data
public class LongHuEnterpriseEncryptResp {

    @ApiModelProperty(value = "响应代码 状态码0000为成功（只代表接口调用成功，不代表发放成功）")
    private String code;

    @ApiModelProperty(value = "响应消息 (成功/失败原因)")
    private String msg;

    @ApiModelProperty(value = "毫秒")
    private String timestamp;

    @ApiModelProperty(value = "响应数据")
    private LongHuEnterpriseGrantResp data;

    @ApiModelProperty(value = "加签后的参数加密字符串")
    private String encryptData;

    @ApiModelProperty(value = "加密key")
    private String encryptKey;

    @ApiModelProperty(value = "加密类型")
    private String encryptType;

    @ApiModelProperty(value = "业务系统ID")
    private String appId;
}
