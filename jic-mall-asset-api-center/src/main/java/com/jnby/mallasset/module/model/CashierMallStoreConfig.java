package com.jnby.mallasset.module.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-07-15 13:35:45
 * @Description: 商场资产规则配置
 */
@Data
@TableName("CASHIER_MALL_STORE_CONFIG")
@ApiModel(value="CashierMallStoreConfig对象", description="商场门店配置")
public class CashierMallStoreConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID")
    private Long id;
    @ApiModelProperty(value = "0未删除、1已删除")
    @TableField("IS_DELETE")
    private Boolean isDelete;
    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "商场资产配置ID")
    @TableField("MALL_ASSET_CONFIG_ID")
    private Long mallAssetConfigId;
    @ApiModelProperty(value = "商场名称")
    @TableField("MALL_NAME")
    private String mallName;
    @ApiModelProperty(value = "平台商场门店ID")
    @TableField("PLATFORM_MALL_STORE_ID")
    private String platformMallStoreId;
    @ApiModelProperty(value = "商场门店名称")
    @TableField("MALL_STORE_NAME")
    private String mallStoreName;
    @ApiModelProperty(value = "支付平台：1微信直连 2收钱吧 3支付宝直连 4电银")
    @TableField("PAY_CHANNEL")
    private String payChannel;
    @ApiModelProperty(value = "支付密钥")
    @TableField("PAY_SIGN")
    private String paySign;
    @ApiModelProperty(value = "伯俊门店ID")
    @TableField("BJ_STORE_ID")
    private String bjStoreId;
    @ApiModelProperty(value = "积分换算比例。1积分换算多少元。例如0.01=1积分兑换0.01元")
    @TableField("POINTS_DEDUCTION_SCALE")
    private BigDecimal pointsDeductionScale;
    @ApiModelProperty(value = "单订单积分抵扣起扣门槛")
    @TableField("POINTS_DEDUCTION_THRESHOLD")
    private Long pointsDeductionThreshold;
    @ApiModelProperty(value = "单订单积分抵扣上限")
    @TableField("POINTS_DEDUCTION_UPPER_LIMIT")
    private Long pointsDeductionUpperLimit;
    @ApiModelProperty(value = "单订单积分抵扣阶梯单位。100代表积分每满100才可以抵扣计算")
    @TableField("POINTS_DEDUCTION_LADDER")
    private Long pointsDeductionLadder;
    @ApiModelProperty(value = "微信直连的商户ID")
    @TableField("MCH_ID")
    private String mchId;
    @ApiModelProperty(value = "平台商场ID")
    @TableField("PLATFORM_MALL_ID")
    private String platformMallId;
    @ApiModelProperty(value = "平台APP_ID")
    @TableField("PLATFORM_APP_ID")
    private String platformAppId;
    @ApiModelProperty(value = "平台公钥")
    @TableField("PLATFORM_PUBLIC_KEY")
    private String platformPublicKey;
    @ApiModelProperty(value = "平台私钥")
    @TableField("PLATFORM_PRIVATE_KEY")
    private String platformPrivateKey;
    @ApiModelProperty(value = "收钱吧APP_ID")
    @TableField("SQB_APP_ID")
    private String sqbAppId;
    @ApiModelProperty(value = "收钱吧终端ID")
    @TableField("SQB_TERMINAL_SN")
    private String sqbTerminalSn;
    @ApiModelProperty(value = "是否联域收银。是代表前端展示联域商场模块")
    @TableField("HAS_LINK_CASHIER")
    private Boolean hasLinkCashier;
    @ApiModelProperty(value = "积分商品条码/货号等信息")
    @TableField("INTEGRAL_SKU_CODE")
    private String integralSkuCode;
    @ApiModelProperty(value = "门店收款方式：1=自收银、自退款，2=商场收银、自退款，3=商场收银、商场退款，4=华润自研")
    @TableField("COLLECTION_TYPE")
    private Integer collectionType;
    @ApiModelProperty(value = "使用资产后是否整单退。true=整单退，false=部分退")
    @TableField("USE_ASSET_ONE_TIME_REFUND")
    private Boolean useAssetOneTimeRefund;
}
