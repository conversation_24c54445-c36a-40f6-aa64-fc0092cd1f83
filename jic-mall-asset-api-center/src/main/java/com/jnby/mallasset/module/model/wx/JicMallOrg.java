package com.jnby.mallasset.module.model.wx;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: CodeGenerator
 * @Date: 2024-08-20 16:29:25
 * @Description: 
 */
@TableName("JIC_MALL_ORG")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="JicMallOrg对象", description="")
public class JicMallOrg implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private Long id;
    @TableField("VID")
    private String vid;
    @TableField("BRAND_ID")
    private String brandId;
    @TableField("INSIDE_ID")
    private Long insideId;
    // 2=品牌、10=门店、100=自提点
    @TableField("VID_TYPE")
    private Integer vidType;
    @TableField("VID_CODE")
    private String vidCode;
    @TableField("VID_NAME")
    private String vidName;
    @TableField("PARENT_VID")
    private String parentVid;
    @TableField("VID_STATUS")
    private Integer vidStatus;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public JicMallOrg setId(Long id) {
        this.id = id;
        return this;
    }

    public String getVid() {
        return vid;
    }

    public JicMallOrg setVid(String vid) {
        this.vid = vid;
        return this;
    }

    public String getBrandId() {
        return brandId;
    }

    public JicMallOrg setBrandId(String brandId) {
        this.brandId = brandId;
        return this;
    }

    public Long getInsideId() {
        return insideId;
    }

    public JicMallOrg setInsideId(Long insideId) {
        this.insideId = insideId;
        return this;
    }

    public Integer getVidType() {
        return vidType;
    }

    public JicMallOrg setVidType(Integer vidType) {
        this.vidType = vidType;
        return this;
    }

    public String getVidCode() {
        return vidCode;
    }

    public JicMallOrg setVidCode(String vidCode) {
        this.vidCode = vidCode;
        return this;
    }

    public String getVidName() {
        return vidName;
    }

    public JicMallOrg setVidName(String vidName) {
        this.vidName = vidName;
        return this;
    }

    public String getParentVid() {
        return parentVid;
    }

    public JicMallOrg setParentVid(String parentVid) {
        this.parentVid = parentVid;
        return this;
    }

    public Integer getVidStatus() {
        return vidStatus;
    }

    public JicMallOrg setVidStatus(Integer vidStatus) {
        this.vidStatus = vidStatus;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public JicMallOrg setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public JicMallOrg setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return "JicMallOrgModel{" +
            "id=" + id +
            ", vid=" + vid +
            ", brandId=" + brandId +
            ", insideId=" + insideId +
            ", vidType=" + vidType +
            ", vidCode=" + vidCode +
            ", vidName=" + vidName +
            ", parentVid=" + parentVid +
            ", vidStatus=" + vidStatus +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            "}";
    }
}
