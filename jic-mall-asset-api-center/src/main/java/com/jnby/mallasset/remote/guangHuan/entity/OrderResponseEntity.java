package com.jnby.mallasset.remote.guangHuan.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.kkmall.entity-KkMallOrderRespEntity.java
 * 文件简介: KKMALL订单返回
 *
 * <AUTHOR>
 * @date 2024/8/1 15:58
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class OrderResponseEntity implements Serializable {
    @ApiModelProperty("订单号")
    private String ordNum;
    @ApiModelProperty("业务ID，唯一值")
    private String exchangeId;
    @ApiModelProperty("外部单号")
    private String exchangeNo;
}
