package com.jnby.mallasset.remote.mallcoo.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BaseMallCooPosResp<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "状态码", required = true)
    private Integer code;

    @ApiModelProperty(value = "错误消息", required = true)
    private String message;

    @ApiModelProperty(value = "返回内容", required = true)
    private String content;

    @ApiModelProperty("响应数据")
    private T data;

    public boolean isSuccess(){
        if (this.code == 1) {
            return true;
        }
        return false;
    }
}
