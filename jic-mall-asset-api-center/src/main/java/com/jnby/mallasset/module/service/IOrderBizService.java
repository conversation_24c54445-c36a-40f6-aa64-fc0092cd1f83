package com.jnby.mallasset.module.service;

import com.jnby.common.ResponseResult;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderPosReq;
import com.jnby.mallasset.dto.req.order.OrderPosResult;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.remote.huarun.entity.BaseHuaRunResp;
import com.jnby.mallasset.remote.huarun.entity.SynchronizationOrderRespEntity;

/**
 * 文件名: com.jnby.mallasset.module.service-IMallCooBizService.java
 * 文件简介: 猫酷服务
 *
 * <AUTHOR>
 * @date 2024/7/16 20:18
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface IOrderBizService {

    /**
     * 功能描述: 消费加积分
     * 使用场景:
     *
     * @param orderConsumeReq
     * @return com.jnby.mallasset.dto.res.CommonResponse
     * <AUTHOR>
     * @date 2024/7/17 18:10
     */
    ResponseResult consumeAddScore(OrderConsumeReq orderConsumeReq);

    /**
     * 功能描述: 退货退积分
     * 使用场景:
     *
     * @param orderRefundReq
     * @return com.jnby.mallasset.dto.res.CommonResponse
     * <AUTHOR>
     * @date 2024/7/17 19:10
     */
    ResponseResult consumeReturnScore(OrderRefundReq orderRefundReq);

    /**
     * 功能描述: 预退货
     * 使用场景:
     *
     * @param orderRefundReq
     * @return com.jnby.mallasset.dto.res.CommonResponse
     * <AUTHOR>
     * @date 2024/7/17 19:10
     */
    ResponseResult consumePrepareReturn(OrderRefundReq orderRefundReq);

    /**
     * 功能描述: 自收银退货
     * 使用场景:
     *
     * @param orderRefundReq
     * @return com.jnby.mallasset.dto.res.CommonResponse
     * <AUTHOR>
     * @date 2024/7/17 19:10
     */
    ResponseResult shopPrepareReturn(OrderRefundReq orderRefundReq);

    /**
     * 功能描述: 华润订单回调
     * 使用场景:
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/7/20 10:52
     */
    String callback(BaseHuaRunResp<SynchronizationOrderRespEntity> req);

    /**
     * 功能描述: 颐堤港传输POS订单数据
     * 使用场景:
     *
     * @param req
     * @return void
     * <AUTHOR>
     * @date 2024/12/13 14:39
     */
    OrderPosResult posYiDiGangOrder(OrderPosReq req);

}
