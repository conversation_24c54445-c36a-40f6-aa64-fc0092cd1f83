spring:
  cloud:
    gateway:
      thread-pool:
        max-threads: 200 # 最大线程数
        queue-capacity: 1000 # 等待队列容量
      httpclient:
        pool:
          # 最大连接数
          max-connections: 10000
          # 最大连接时间
          max-life-time: 10
          # 返回时间
          acquire-timeout: 10
          # 最大空闲时间
          max-idle-time: 10000
          # 设置固定链接池
          type: fixed
      globalcors:
        cors-configurations:
          '[/**]':
            # 允许携带认证信息
            # 允许跨域的源(网站域名/ip)，设置*为全部
            # 允许跨域请求里的head字段，设置*为全部
            # 允许跨域的method， 默认为GET和OPTIONS，设置*为全部
            # 跨域允许的有效期
            allowCredentials: true
            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
      discovery:
        locator:
#          lowerCaseServiceId: true
          enabled: true
#      routes:
#        - id: web
#          uri: lb://jnby-box-center
#          predicates:
#            - Path=/gateway/api/**
#          filters:
#            - StripPrefix=2
#            - RemoveRequestHeader=Origin
      default-filters:
        - name: Hystrix
          args:
            name: default
            #转发地址
            fallbackUri: 'forward:/fallback'
        - name: Retry
          args:
            #重试次数，默认值是 3 次
            retries: 1
            #HTTP 的状态返回码
            statuses: BAD_GATEWAY,BAD_REQUEST
            #指定哪些方法的请求需要进行重试逻辑，默认值是 GET 方法
            methods: GET,POST


ribbon:
  ReadTimeout: 30000  #ribbon读取超时时间，接口处理时间，不包括建立连接时间
  ConnectTimeout: 30000 #ribbon请求连接时间
  OkToRetryOnAllOperations: false #网关默认开启重试，此属性设置为false 只对GET请求重试，保证幂等性
  MaxAutoRetries: 1  #Max number of retries on the same server (excluding the first try)
  MaxAutoRetriesNextServer: 1 #Max number of next servers to retry (excluding the first server)
  ServerListRefreshInterval: 30000 # refresh the server list from the source

hystrix:
  threadpool:
    default:
      coreSize: 200
      maxQueueSize: 1000
      queueSizeRejectionThreshold: 800
  command:
    default:
      execution:
        isolation:
          strategy: THREAD
          thread:
            timeoutInMilliseconds: 30000  #超过此时间后进入熔断，这个时间应该大于后端及其ribbon的时间，否则后端接口未执行完就进入熔断
