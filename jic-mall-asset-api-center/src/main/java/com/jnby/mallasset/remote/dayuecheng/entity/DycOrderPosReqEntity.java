package com.jnby.mallasset.remote.dayuecheng.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DycMemberRegisterReqEntity.java
 * 文件简介: 大悦城公共参数请求
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */

@Data
public class DycOrderPosReqEntity implements Serializable {
    @ApiModelProperty(value = "收银员编号")
    private String cashierId;
    @ApiModelProperty(value = "备注")
    private String comments;
    @ApiModelProperty(value = "商品列表")
    private List<ItemProduct> itemList;
    @ApiModelProperty(value = "商场编号")
    private String mall;
    @ApiModelProperty(value = "会员手机号")
    private String mobile;
    @ApiModelProperty(value = "订单号")
    private String orderId;
    @ApiModelProperty(value = "付款明细")
    private List<ItemPay> payList;
    @ApiModelProperty(value = "店铺编号")
    private String store;
    @ApiModelProperty(value = "收银机编号")
    private String tillId;
    @ApiModelProperty(value = "订单日期")
    private String tradeDate;
    @ApiModelProperty(value = "订单时间")
    private String tradeTime;
    @ApiModelProperty(value = "录入订单时间")
    private String uploadTime;
    @ApiModelProperty(value = "订单总金额")
    private BigDecimal totalAmt;
    @ApiModelProperty(value = "订单类型")
    private String type;
    @ApiModelProperty(value = "来源")
    private String source;
    @ApiModelProperty(value = "会员卡号")
    private String memberNo;
    @ApiModelProperty(value = "关联原订单号")
    private String refOrderId;
}
