package com.jnby.mallasset.remote.mallcoo;


import com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooPosResp;
import com.jnby.mallasset.remote.mallcoo.entity.pos.GuangHuanUploadPosReq;
import com.jnby.mallasset.remote.mallcoo.entity.pos.GuangHuanUploadPosResp;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.HeaderMap;
import retrofit2.http.POST;
import retrofit2.http.Url;

import java.util.Map;


public interface IMallCooPosRemoteHttpApi {

    /**
     * 功能描述: pos颐堤港订单数据
     * 使用场景:
     *
     * @param headers
     * @param data
     * @return retrofit2.Call<com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooResp>
     * <AUTHOR>
     * @date 2024/12/15 15:49
     */
    @POST("/api/Pos/10012")
    Call<BaseMallCooPosResp> posOrder(@HeaderMap Map<String, String> headers, @Body RequestBody data);

    /**
     * 功能描述: pos光环订单数据
     * 使用场景:
     *
     * @param url
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.mallcoo.entity.pos.GuangHuanUploadPosResp>
     * <AUTHOR>
     * @date 2025/4/11 15:20
     */
    @POST
    Call<GuangHuanUploadPosResp> posGuangHuanOrderData(@Url String url, @Body GuangHuanUploadPosReq req);

}
