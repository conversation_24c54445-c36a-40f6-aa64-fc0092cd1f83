package com.jnby.mallasset.strategy.platform.yintai99;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.dto.req.order.PayItem;
import com.jnby.mallasset.dto.req.order.ProductItem;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.remote.yintai99.IYinTai99RemoteHttpApi;
import com.jnby.mallasset.remote.yintai99.entity.*;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 凯德订单平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.YIN_TAI_99, category = PlatformCategoryTypeEnum.ORDER)
@Slf4j
@AllArgsConstructor
public class YinTai99OrderStrategyService extends AbstractOrderService {

    private CashierMallOrderLogMapper cashierMallOrderLogMapper;
    private IYinTai99RemoteHttpApi yinTai99RemoteHttpApi;
    private YinTai99MemberStrategyService yinTai99MemberStrategyService;

    @Override
    public ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("银泰in99订单同步输入参数：{}", JSON.toJSONString(orderConsumeReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderConsumeReq, cashierMallAssetStoreRef);
            String platformMallStoreId = cashierMallAssetStoreRef.getPlatformMallStoreId();
            String integralSkuCode = cashierMallAssetStoreRef.getIntegralSkuCode();
            String[] items = integralSkuCode.split(",");
            String[] apiKeys = items[0].split("#");
            String apiKey = apiKeys[1];
            String[] tillIds = items[1].split("#");
            String tillId = tillIds[1];
            String[] itemOrgIds = items[2].split("#");
            String itemOrgId = itemOrgIds[1];
            String[] itemCodes = items[3].split("#");
            String itemCode = itemCodes[1];
            String[] baseCurrencyCodes = items[4].split("#");
            String baseCurrencyCode = baseCurrencyCodes[1];
            String[] tenderCodes = items[5].split("#");
            String tenderCode = tenderCodes[1];
            String[] cashiers = items[6].split("#");
            String cashier = cashiers[1];

            String dateTime = DateUtil.timeStamp3Date(orderConsumeReq.getOrdFinishTime(),null);
            String day = dateTime.substring(0,10);
            String ordNum = orderConsumeReq.getOrdNum();

            YinTai99PosEntityReq req = new YinTai99PosEntityReq();
            req.setApiKey(apiKey);
            req.setSignature("");
            req.setBonusCalcMethod("1");
            String docKey = day.concat(".").concat(platformMallStoreId).concat(".").concat(tillId).concat(".").concat(ordNum);
            req.setDocKey(docKey);

            TransHeaderEntity transHeader = new TransHeaderEntity();
            transHeader.setTxDate(day);
            transHeader.setLedgerDatetime(dateTime);
            transHeader.setStoreCode(platformMallStoreId);
            transHeader.setTillId(tillId);
            transHeader.setDocNo(ordNum);
            transHeader.setVoidDocNo("");
            transHeader.setTxAttrib("");
            req.setTransHeader(transHeader);

            SalesTotalEntity salesTotal = new SalesTotalEntity();
            salesTotal.setCashier(cashier);
            // 获取会员信息
            MemberRegisterReq memberRegisterReq = new MemberRegisterReq();
            memberRegisterReq.setMobile(orderConsumeReq.getMemberTel());
            memberRegisterReq.setStoreId(orderConsumeReq.getStoreId());
            String vipCode = null;
            ResponseResult result = yinTai99MemberStrategyService.openCard(memberRegisterReq,cashierMallAssetStoreRef);
            if(result.getCode() == ResultCodeEnum.SUCCESS.getCode() && result.getData() != null){
                CommonMemberResponse commonMemberResponse = (CommonMemberResponse) result.getData();
                vipCode = commonMemberResponse.getMemberId();
            }
            salesTotal.setVipCode(vipCode);
            salesTotal.setNetQty(orderConsumeReq.getTotQty());
            salesTotal.setNetAmount(orderConsumeReq.getOrdAmount());
            salesTotal.setCalculateVipBonus("0");
            req.setSalesTotal(salesTotal);

            List<SalesItemEntity> salesItem = new ArrayList<>();
            List<ProductItem> productItemList = orderConsumeReq.getProductItemList();
            for(int i=0; i<productItemList.size(); i++){
                ProductItem productItem = productItemList.get(i);
                SalesItemEntity salesItemEntity = new SalesItemEntity();
                salesItemEntity.setSalesLineNumber(i+1);
                salesItemEntity.setItemOrgId(itemOrgId);
                salesItemEntity.setItemCode(itemCode);
                salesItemEntity.setItemLotNum("*");
                salesItemEntity.setInventoryType(0);
                salesItemEntity.setQty(productItem.getItemQty());
                salesItemEntity.setTotalDiscountLess(BigDecimal.ZERO);
                salesItemEntity.setItemDiscountLess(BigDecimal.ZERO);
                salesItemEntity.setNetAmount(productItem.getItemPrice().multiply(new BigDecimal(productItem.getItemQty())));
                salesItemEntity.setVipBonusEarn(productItem.getItemPrice().multiply(new BigDecimal(productItem.getItemQty())).intValue());
                salesItem.add(salesItemEntity);
            }
            req.setSalesItem(salesItem);

            List<SalesTenderEntity> salesTender = new ArrayList<>();
            List<PayItem> payItemList = orderConsumeReq.getPayItemList();
            for (PayItem payItem : payItemList) {
                if(payItem.getId() == 62 || payItem.getId() == 231){
                    SalesTenderEntity salesTenderEntity = new SalesTenderEntity();
                    salesTenderEntity.setTenderCode(tenderCode);
                    salesTenderEntity.setBaseCurrencyCode(baseCurrencyCode);
                    salesTenderEntity.setPayAmount(payItem.getPayamount());
                    salesTenderEntity.setBaseAmount(payItem.getPayamount());
                    salesTenderEntity.setExcessAmount(BigDecimal.ZERO);
                    salesTender.add(salesTenderEntity);
                }
            }
            req.setSalesTender(salesTender);
            log.info("银泰in99订单POS传输参数：{}", JSON.toJSONString(req));
            Response<YinTai99PosEntityResp> response = yinTai99RemoteHttpApi.posOrder(req).execute();
            YinTai99PosEntityResp body = response.body();
            log.info("银泰in99订单POS返回结果：{}", JSON.toJSONString(body));
            if(body.getErrorCode() == 0){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                YInTai99OrderResponse yInTai99OrderResponse = new YInTai99OrderResponse();
                yInTai99OrderResponse.setOrdNum(ordNum);
                yInTai99OrderResponse.setExchangeId(ordNum);
                yInTai99OrderResponse.setExchangeNo(ordNum);
                commonResponse.setData(yInTai99OrderResponse);

                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            }else {
                commonResponse.setCode(body.getErrorCode());
                commonResponse.setMsg(body.getErrorMessage());
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 银泰in99订单同步异常",orderConsumeReq.getOrdNum(), e);
        }
        return commonResponse;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeNo(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setBjStoreId(orderConsumeReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(1);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult pushRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("银泰in99订单退货退款输入参数：{}", JSON.toJSONString(orderRefundReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderRefundReq, cashierMallAssetStoreRef);

            String platformMallStoreId = cashierMallAssetStoreRef.getPlatformMallStoreId();
            String integralSkuCode = cashierMallAssetStoreRef.getIntegralSkuCode();
            String[] items = integralSkuCode.split(",");
            String[] apiKeys = items[0].split("#");
            String apiKey = apiKeys[1];
            String[] tillIds = items[1].split("#");
            String tillId = tillIds[1];
            String[] itemOrgIds = items[2].split("#");
            String itemOrgId = itemOrgIds[1];
            String[] itemCodes = items[3].split("#");
            String itemCode = itemCodes[1];
            String[] baseCurrencyCodes = items[4].split("#");
            String baseCurrencyCode = baseCurrencyCodes[1];
            String[] tenderCodes = items[5].split("#");
            String tenderCode = tenderCodes[1];
            String[] cashiers = items[6].split("#");
            String cashier = cashiers[1];

            String[] pushTimes = items[10].split("#");
            String pushTime = pushTimes[1];

            String dateTime = DateUtil.timeStamp3Date(orderRefundReq.getRefundTime(),null);
            String day = dateTime.substring(0,10);
            String ordNum = orderRefundReq.getRefundNo();

            YinTai99PosEntityReq req = new YinTai99PosEntityReq();
            req.setApiKey(apiKey);
            req.setSignature("");
            req.setBonusCalcMethod("1");
            String docKey = day.concat(".").concat(platformMallStoreId).concat(".").concat(tillId).concat(".").concat(ordNum);
            req.setDocKey(docKey);

            TransHeaderEntity transHeader = new TransHeaderEntity();
            transHeader.setTxDate(day);
            transHeader.setLedgerDatetime(dateTime);
            transHeader.setStoreCode(platformMallStoreId);
            transHeader.setTillId(tillId);
            transHeader.setDocNo(ordNum);
            transHeader.setVoidDocNo("");
            transHeader.setTxAttrib("");
            req.setTransHeader(transHeader);

            SalesTotalEntity salesTotal = new SalesTotalEntity();
            req.setSalesTotal(salesTotal);
            salesTotal.setCashier(cashier);
            salesTotal.setVipCode(null);
            salesTotal.setNetAmount(orderRefundReq.getRefundAmount().multiply(new BigDecimal(-1)));
            salesTotal.setCalculateVipBonus("0");

            // 兼容历史数据
            String vipCode = null;
            Long ordFinishTime = orderRefundReq.getOrdFinishTime();
            Long finalTime = DateUtil.getTimeStampTime(pushTime);
            if(ordFinishTime >= finalTime){
                log.info("上线后订单扣除积分");
                MemberRegisterReq memberRegisterReq = new MemberRegisterReq();
                memberRegisterReq.setMobile(orderRefundReq.getMemberTel());
                memberRegisterReq.setStoreId(orderRefundReq.getStoreId());
                ResponseResult result = yinTai99MemberStrategyService.openCard(memberRegisterReq,cashierMallAssetStoreRef);
                if(result.getCode() == ResultCodeEnum.SUCCESS.getCode() && result.getData() != null){
                    CommonMemberResponse commonMemberResponse = (CommonMemberResponse) result.getData();
                    vipCode = commonMemberResponse.getMemberId();
                }
                salesTotal.setVipCode(vipCode);
            }else {
                log.info("历史订单不用扣除积分");
            }

            List<SalesItemEntity> salesItem = new ArrayList<>();
            List<ProductItem> productItemList = orderRefundReq.getProductItemList();
            int total = 0;
            for(int i=0; i<productItemList.size(); i++){
                ProductItem productItem = productItemList.get(i);
                SalesItemEntity salesItemEntity = new SalesItemEntity();
                salesItemEntity.setSalesLineNumber(i+1);
                salesItemEntity.setItemOrgId(itemOrgId);
                salesItemEntity.setItemCode(itemCode);
                salesItemEntity.setItemLotNum("*");
                salesItemEntity.setInventoryType(0);
                salesItemEntity.setQty(productItem.getItemQty());
                salesItemEntity.setTotalDiscountLess(BigDecimal.ZERO);
                salesItemEntity.setItemDiscountLess(BigDecimal.ZERO);
                salesItemEntity.setNetAmount(productItem.getItemPrice().multiply(new BigDecimal(-1)));
                salesItemEntity.setVipBonusEarn(productItem.getItemPrice().multiply(new BigDecimal(-1)).intValue());
                salesItem.add(salesItemEntity);
                total += productItem.getItemQty();
            }
            req.setSalesItem(salesItem);
            salesTotal.setNetQty(total);

            List<SalesTenderEntity> salesTender = new ArrayList<>();
            List<PayItem> payItemList = orderRefundReq.getPayItemList();
            for (PayItem payItem : payItemList) {
                if(payItem.getId() == 62 || payItem.getId() == 231){
                    SalesTenderEntity salesTenderEntity = new SalesTenderEntity();
                    salesTenderEntity.setTenderCode(tenderCode);
                    salesTenderEntity.setBaseCurrencyCode(baseCurrencyCode);
                    salesTenderEntity.setPayAmount(payItem.getPayamount());
                    salesTenderEntity.setBaseAmount(payItem.getPayamount());
                    salesTenderEntity.setExcessAmount(BigDecimal.ZERO);
                    salesTender.add(salesTenderEntity);
                }
            }
            req.setSalesTender(salesTender);
            log.info("银泰in99订单POS退货退款传输参数：{}", JSON.toJSONString(req));
            Response<YinTai99PosEntityResp> response = yinTai99RemoteHttpApi.posOrder(req).execute();
            YinTai99PosEntityResp body = response.body();
            log.info("银泰in99订单POS退货退款返回结果：{}", JSON.toJSONString(body));
            if(body.getErrorCode() == 0){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                YInTai99OrderResponse yInTai99OrderResponse = new YInTai99OrderResponse();
                yInTai99OrderResponse.setOrdNum(ordNum);
                yInTai99OrderResponse.setExchangeId(ordNum);
                yInTai99OrderResponse.setExchangeNo(ordNum);
                commonResponse.setData(yInTai99OrderResponse);

                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            }else {
                commonResponse.setCode(body.getErrorCode());
                commonResponse.setMsg(body.getErrorMessage());
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 银泰in99订单退货退款异常",orderRefundReq.getRefundNo(), e);
        }
        return commonResponse;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderRefundReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setExchangeNo(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setBjStoreId(orderRefundReq.getStoreId());
        cashierMallOrderLog.setMallId(assetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(assetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(assetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(2);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }



    @Override
    public ResponseResult prepareRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

}
