package com.jnby.mallasset.remote.mallcoo.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Builder
@ApiModel("批量使用券回参")
@Data
public class CouponBatchUseRespEntity implements Serializable {
    @ApiModelProperty("猫酷核销商户ID")
    private Long McShopID;
    @ApiModelProperty("核销时间")
    private String OperatorTime;
    @ApiModelProperty("POS机ID【第三方核销的时候传入的】")
    private String POSID;
    @ApiModelProperty("流水号【第三方核销的时候传入的】")
    private String TradeSerialNo;
    @ApiModelProperty("订单编号【第三方核销的时候传入的】")
    private String OrderNo;
    @ApiModelProperty("第三方核销人名称【第三方核销的时候传入的】")
    private String ThirdOperator;
    @ApiModelProperty("券核销结果")
    private List<VCodeResultInfo> UseInfoList;

    @Builder
    @Data
    public static class VCodeResultInfo implements Serializable {
        @ApiModelProperty("券模板编号")
        private String CouponRuleNo;
        @ApiModelProperty("核销时间")
        private String OperatorTime;
        @ApiModelProperty("核销结果代码")
        private Integer Code;
        @ApiModelProperty("核销结果描述")
        private String CodeMsg;
        @ApiModelProperty("券码")
        private String VCode;
    }

}
