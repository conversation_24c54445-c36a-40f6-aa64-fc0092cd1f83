package com.jnby.mallasset.config.swagger;

import com.github.xiaoymin.swaggerbootstrapui.annotations.EnableSwaggerBootstrapUI;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 * @date 2021/3/5
 */
@ConditionalOnExpression("'${spring.profiles.active}'.equals('dev') || '${spring.profiles.active}'.equals('test')")
@Configuration
@EnableSwaggerBootstrapUI
@EnableSwagger2
public class Swagger2 {

    @Bean
    public Docket createRestApi() {

        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.jnby.mallasset.api"))
                .paths(PathSelectors.any())
                .build()
                .groupName("商场资产");
    }


    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("商场资产_API")
                .termsOfServiceUrl("")
                .version("1.0")
                .build();
    }

}
