package com.jnby.mallasset.api.dto;

import com.google.common.base.Preconditions;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
@ApiModel("入参基类")
public class BaseReq {

    @ApiModelProperty(value = "伯俊门店CODE", required = true)
    private String storeId;

    @ApiModelProperty(value = "消费者ID:手机号", required = true)
    private String customerId;

    public void check() {
        Preconditions.checkArgument(StringUtils.isNotBlank(storeId), "伯俊门店CODE不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(customerId), "消费者ID不能为空");
    }
}
