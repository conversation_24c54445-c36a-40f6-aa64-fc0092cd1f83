package com.jnby.mallasset.remote.mallcoo.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel("积分明细回参")
@Data
public class PointsListDetailRespEntity implements Serializable {
    @ApiModelProperty(value = "当前总积分")
    private Double CurScore;
    @ApiModelProperty(value = "积分明细")
    private List<ScoreDetailModel> ScoreDetailModel;

    @Data
    public static class ScoreDetailModel {
        @ApiModelProperty(value = "积分记录ID")
        private String ScoreRecordID;
        @ApiModelProperty(value = "积分类型")
        private String ScoreType;
        @ApiModelProperty(value = "本次积分")
        private Double Score;
        @ApiModelProperty(value = "积分时间")
        private String ScoreTime;
        @ApiModelProperty(value = "描述")
        private String Desc;
    }
}
