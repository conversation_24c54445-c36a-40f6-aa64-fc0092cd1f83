package com.jnby.mallasset.remote.dayuecheng;

import com.alibaba.fastjson.JSON;
import com.jnby.mallasset.config.retrofit.RetrofitConfig;
import com.jnby.mallasset.remote.dayuecheng.entity.BaseDycResp;
import com.jnby.mallasset.remote.dayuecheng.entity.DycMemberQueryRespEntity;
import com.jnby.mallasset.util.DateUtil;
import com.jnby.mallasset.util.Md5Util;
import retrofit2.Response;

import java.util.Date;
import java.util.Map;
import java.util.TreeMap;

public class DayuechengUtils {

    public static void main(String[] args) {

//        Map<String, Object> parameters = new TreeMap<>();
//        StringBuilder stringBuilder = new StringBuilder();
//        long timestamp = DateUtil.getCurrentTimeMillis(new Date());
//        String appid = "100814357303";
//        String method = "joycity.crm.member.getUnionMemberInfo";
//        String mobile = "15820584615";
//        String key = "1TxHWykT6YpYdKGEIu8vpQpId4OCPRLX";
//        parameters.put("app_id",appid);
//        parameters.put("method",method);
//        parameters.put("timestamp",timestamp);
//        parameters.put("mobile",mobile);
//        parameters.put("bindType","0");
//        for(Map.Entry<String,Object> entry : parameters.entrySet()){
//            String str = entry.getKey() + "=" + entry.getValue();
//            stringBuilder.append(str).append("&");
//        }
//        String json = stringBuilder.toString();
//        json = json + "key=" + key;
//        System.out.println("json==:"  + json);
//        String sign = Md5Util.getMd5Hash(json);
//        System.out.println("签名：" + sign);
//        try {
//            IDayuechengRemoteHttpApi api = RetrofitConfig.getMainApi("https://open.joycity.mobi", IDayuechengRemoteHttpApi.class);
//            Response<BaseDycResp<DycMemberQueryRespEntity>> ex = api.getUnionMemberInfo(appid,method,timestamp,sign,mobile,"0").execute();
//            BaseDycResp<DycMemberQueryRespEntity> resp = ex.body();
//            System.out.println("结果===" + JSON.toJSONString(resp));
//
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }

//        Map<String, Object> parameters = new TreeMap<>();
//        StringBuilder stringBuilder = new StringBuilder();
//        long timestamp = DateUtil.getCurrentTimeMillis(new Date());
//        String appid = "100814357303";
//        String method = "joycity.crm.member.createMemberByUnion";
//        String mobile = "17774011597";
//        String issueStore = "000001";
//        String key = "1TxHWykT6YpYdKGEIu8vpQpId4OCPRLX";
//        parameters.put("app_id",appid);
//        parameters.put("method",method);
//        parameters.put("timestamp",timestamp);
//        parameters.put("mobile",mobile);
//        parameters.put("issueStore","000001");
//        for(Map.Entry<String,Object> entry : parameters.entrySet()){
//            String str = entry.getKey() + "=" + entry.getValue();
//            stringBuilder.append(str).append("&");
//        }
//        String json = stringBuilder.toString();
//        json = json + "key=" + key;
//        System.out.println("json==:"  + json);
//        String sign = Md5Util.getMd5Hash(json);
//        System.out.println("签名：" + sign);
//        try {
//            IDayuechengRemoteHttpApi api = RetrofitConfig.getMainApi("https://open.joycity.mobi", IDayuechengRemoteHttpApi.class);
//            Response<BaseDycResp<DycMemberQueryRespEntity>> ex = api.registerMemberInfo(appid,method,timestamp,sign,mobile,issueStore).execute();
//            BaseDycResp<DycMemberQueryRespEntity> resp = ex.body();
//            System.out.println("结果===" + JSON.toJSONString(resp));
//
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
    }

}
