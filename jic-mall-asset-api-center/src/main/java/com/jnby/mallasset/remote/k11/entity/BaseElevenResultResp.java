package com.jnby.mallasset.remote.k11.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.k11.entity-BaseResultResp.java
 * 文件简介:
 *
 * <AUTHOR>
 * @date 2025/8/7 10:12
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class BaseElevenResultResp implements Serializable {
    @ApiModelProperty(value = "结果")
    private int code;
    @ApiModelProperty(value = "返回消息")
    private String msg;
    @ApiModelProperty("响应数据")
    private Object data;

}
