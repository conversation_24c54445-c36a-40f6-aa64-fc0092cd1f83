package com.jnby.mallasset.remote.yintai99.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TransHeaderEntity implements Serializable {
    @ApiModelProperty("交易日期yyyy-mm-dd")
    private String txDate;
    @ApiModelProperty("客戶端發生的日期及時間")
    private String ledgerDatetime;
    @ApiModelProperty("店鋪編號")
    private String storeCode;
    @ApiModelProperty("收款機號")
    private String tillId;
    @ApiModelProperty("銷售單號")
    private String docNo;
    @ApiModelProperty("被取消的原銷售單號")
    private String voidDocNo;
    @ApiModelProperty("預留字段")
    private String txAttrib;
}
