package com.jnby.mallasset.remote.mallcoo.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MemberCreateCardReqEntity implements Serializable {
    @ApiModelProperty(value = "手机号", required = true)
    @JSONField(ordinal = 1)
    private String mobile;
    @ApiModelProperty(value = "开卡平台来源", required = true)
    @JSONField(ordinal = 2)
    private int dataSource;
}
