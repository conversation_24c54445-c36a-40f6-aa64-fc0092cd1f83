<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.mallasset.module.mapper.box.CashierMallAssetConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.mallasset.module.model.CashierMallAssetConfig">
        <id column="ID" property="id" />
        <result column="IS_DELETE" property="isDelete" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="MALL_NAME" property="mallName" />
        <result column="PLATFORM_MALL_ID" property="platformMallId" />
        <result column="PLATFORM_APP_ID" property="platformAppId" />
        <result column="PLATFORM_PUBLIC_KEY" property="platformPublicKey" />
        <result column="PLATFORM_PRIVATE_KEY" property="platformPrivateKey" />
        <result column="POINTS_INCREMENT_TYPE" property="pointsIncrementType" />
        <result column="POINTS_CAN_USE" property="pointsCanUse" />
        <result column="POINTS_NAME" property="pointsName" />
        <result column="COUPON_CAN_USE" property="couponCanUse" />
        <result column="USE_RULE_CONTENT" property="useRuleContent" />
        <result column="HAS_USE_RULE" property="hasUseRule" />
        <result column="HAS_PRIVACY" property="hasPrivacy" />
        <result column="PRIVACY_CONTENT" property="privacyContent" />
        <result column="POINTS_DEDUCTION_SCALE" property="pointsDeductionScale" />
        <result column="POINTS_DEDUCTION_THRESHOLD" property="pointsDeductionThreshold" />
        <result column="POINTS_DEDUCTION_UPPER_LIMIT" property="pointsDeductionUpperLimit" />
        <result column="API_PLATFORM" property="apiPlatform" />
        <result column="POINTS_DEDUCTION_LADDER" property="pointsDeductionLadder" />
        <result column="HAS_PHONE_VERIFICATION" property="hasPhoneVerification" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, IS_DELETE, CREATE_TIME, UPDATE_TIME, MALL_NAME, PLATFORM_MALL_ID, PLATFORM_APP_ID, PLATFORM_PUBLIC_KEY, PLATFORM_PRIVATE_KEY,
          POINTS_INCREMENT_TYPE, POINTS_CAN_USE, POINTS_NAME, COUPON_CAN_USE, USE_RULE_CONTENT, HAS_USE_RULE, HAS_PRIVACY, PRIVACY_CONTENT,
          POINTS_DEDUCTION_SCALE, POINTS_DEDUCTION_THRESHOLD, POINTS_DEDUCTION_UPPER_LIMIT, API_PLATFORM, POINTS_DEDUCTION_LADDER,
          HAS_PHONE_VERIFICATION,
    </sql>

<!--    <select id="selectEntityByMallId" resultMap="BaseResultMap">-->
<!--        SELECT <include refid="Base_Column_List" />-->
<!--            FROM jnby.cashier_mall_asset_config where MALL_ID = #{mallId}-->
<!--    </select>-->
</mapper>