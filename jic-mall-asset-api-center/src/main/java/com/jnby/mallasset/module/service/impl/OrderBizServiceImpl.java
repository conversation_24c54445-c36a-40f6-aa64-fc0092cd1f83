package com.jnby.mallasset.module.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.dto.req.order.*;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.mapper.box.CashierMallStoreConfigMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.module.service.IOrderBizService;
import com.jnby.mallasset.remote.dingding.IDingDingRemoteHttpApi;
import com.jnby.mallasset.remote.dingding.entity.DingDingMsg;
import com.jnby.mallasset.remote.dingding.entity.DingDingResponse;
import com.jnby.mallasset.remote.huarun.entity.BaseHuaRunResp;
import com.jnby.mallasset.remote.huarun.entity.SynchronizationOrderRespEntity;
import com.jnby.mallasset.remote.mallcoo.IMallCooPosRemoteHttpApi;
import com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooPosResp;
import com.jnby.mallasset.sdk.config.YidigangApiConfig;
import com.jnby.mallasset.sdk.util.AesUtil;
import com.jnby.mallasset.sdk.util.Md5Util;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.OrderServiceFactory;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.apache.http.entity.StringEntity;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件名: com.jnby.mallasset.module.service.impl-MallCooBizServiceImpl.java
 * 文件简介:
 *
 * <AUTHOR>
 * @date 2024/7/16 20:08
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Service
@Slf4j
public class OrderBizServiceImpl implements IOrderBizService {
    @Resource
    private OrderServiceFactory orderServiceFactory;
    @Resource
    private CashierMallStoreConfigMapper cashierMallStoreConfigMapper;
    @Resource
    private CashierMallOrderLogMapper cashierMallOrderLogMapper;
    @Resource
    private YidigangApiConfig yidigangApiConfig;
    @Resource
    private IMallCooPosRemoteHttpApi mallCooPosRemoteHttpApi;
    @Resource
    private IDingDingRemoteHttpApi dingDingRemoteHttpApi;

    @Override
    public ResponseResult consumeAddScore(OrderConsumeReq orderConsumeReq) {
        CashierMallAssetStoreRef cashierMallAssetStoreRef = cashierMallStoreConfigMapper.selectInfoByStoreId(orderConsumeReq.getStoreId());
        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getEnumByCode(cashierMallAssetStoreRef.getApiPlatform());
        AbstractOrderService abstractOrderService = orderServiceFactory.router(platformTypeEnum,PlatformCategoryTypeEnum.ORDER);
        return abstractOrderService.pushOrder(orderConsumeReq,cashierMallAssetStoreRef);
    }

    @Override
    public ResponseResult consumeReturnScore(OrderRefundReq orderRefundReq) {
        CashierMallAssetStoreRef cashierMallAssetStoreRef = cashierMallStoreConfigMapper.selectInfoByStoreId(orderRefundReq.getStoreId());
        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getEnumByCode(cashierMallAssetStoreRef.getApiPlatform());
        AbstractOrderService abstractOrderService = orderServiceFactory.router(platformTypeEnum,PlatformCategoryTypeEnum.ORDER);
        return abstractOrderService.pushRefundOrder(orderRefundReq,cashierMallAssetStoreRef);
    }

    @Override
    public ResponseResult consumePrepareReturn(OrderRefundReq orderRefundReq) {
        CashierMallAssetStoreRef cashierMallAssetStoreRef = cashierMallStoreConfigMapper.selectInfoByStoreId(orderRefundReq.getStoreId());
        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getEnumByCode(cashierMallAssetStoreRef.getApiPlatform());
        AbstractOrderService abstractOrderService = orderServiceFactory.router(platformTypeEnum,PlatformCategoryTypeEnum.ORDER);
        return abstractOrderService.prepareRefundOrder(orderRefundReq,cashierMallAssetStoreRef);
    }

    @Override
    public ResponseResult shopPrepareReturn(OrderRefundReq orderRefundReq) {
        CashierMallAssetStoreRef cashierMallAssetStoreRef = cashierMallStoreConfigMapper.selectInfoByStoreId(orderRefundReq.getStoreId());
        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getEnumByCode(cashierMallAssetStoreRef.getApiPlatform());
        AbstractOrderService abstractOrderService = orderServiceFactory.router(platformTypeEnum,PlatformCategoryTypeEnum.ORDER);
        return abstractOrderService.shopRefundOrder(orderRefundReq,cashierMallAssetStoreRef);
    }

    @Override
    public String callback(BaseHuaRunResp<SynchronizationOrderRespEntity> req) {
        log.info("订单回调callback返回参数：{}", JSON.toJSONString(req));
        // 回调成功
        if(req.getResult() == 1 || req.getResult() == 104){
            SynchronizationOrderRespEntity synchronizationOrderRespEntity = req.getQuery();
            String exchangeId = synchronizationOrderRespEntity.getExchangeId();
            // 更新执行标识
            CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
            // 交易ID
            cashierMallOrderLog.setExchangeId(exchangeId);
            // 交易单号
            cashierMallOrderLog.setExchangeNo(synchronizationOrderRespEntity.getOrderNo());
            cashierMallOrderLog.setIsExecute("Y");
            cashierMallOrderLogMapper.updateInfoByExchangeId(cashierMallOrderLog);
        }
        return "Success";
    }

    @Override
    public OrderPosResult posYiDiGangOrder(OrderPosReq req) {
        OrderPosResult result = new OrderPosResult();
        log.info("颐堤港POS订单数据传输参数：{}", JSON.toJSONString(req));
        log.info("颐堤港POS上传订单数据配置参数：{}",  JSONObject.toJSONString(yidigangApiConfig));
        try{
            String appid = yidigangApiConfig.getAppid();
            String secret = yidigangApiConfig.getSecret();
            String version = yidigangApiConfig.getIv();
            String dateTime = DateUtil.timeStamp3Date(req.getConsumeTime(),null);

            OrderPosParam data = new OrderPosParam();
            data.setProjectid(yidigangApiConfig.getProjectId());
            data.setUnique_id(req.getOrdNum());
            data.setTime(dateTime);
            data.setSys_source_code(yidigangApiConfig.getSysSourceCode());
            data.setPos_number(yidigangApiConfig.getPosNumber());
            String storeId = req.getStoreId();
            if(storeId.equals("2DA00129")){
                data.setBrand_id("jnbyxcx");
            }else if(storeId.equals("5DA00141")){
                data.setBrand_id("lessxcx");
            }else if(storeId.equals("9DA00111")){
                data.setBrand_id("sxxcx");
            }
            data.setTransaction_time(dateTime);
            data.setPaid_amount(req.getOrdAmount());
            data.setOrder_number(req.getOrdNum());

            String body = JSONObject.toJSONString(data);
            log.info("POS上传订单数据传输参数：{}",  body);
            body = AesUtil.aesEncrypt(body, secret, version);
            log.info("AES加密后body：" + body);
            String sign = Md5Util.getMd5Hash(body + "&" + secret);
            log.info("MD5加密后签名：" + sign);
            Map<String, String> headers = new HashMap<>();
            headers.put("appId",appid);
            headers.put("sign",sign);
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
            Response<BaseMallCooPosResp> response = mallCooPosRemoteHttpApi.posOrder(headers,requestBody).execute();
            BaseMallCooPosResp baseMallCooPosResp = response.body();
            log.info("POS上传订单数据返回参数：{}", JSONObject.toJSONString(baseMallCooPosResp));
            if(baseMallCooPosResp.getCode() != 1){
                // 钉钉告警
                pushDingWarn(headers, body,JSONObject.toJSONString(data), baseMallCooPosResp);
            }
            result.setCode(baseMallCooPosResp.getCode());
            result.setMsg(baseMallCooPosResp.getMessage());
        }catch (Exception e){
            log.error("POS订单数据传输发送异常",e);
        }
        return result;
    }
    
    private void pushDingWarn(Map<String, String> headers, String body,String params, BaseMallCooPosResp baseMallCooPosResp) throws IOException {
        String msg = "数据推送异常：\n" + "功能：颐堤港POS订单数据" + "\n请求参数: \n" + JSON.toJSON(headers) + ",[加密body]：" + body + ",[参数]：" + params + "\n请求结果: \n" + JSON.toJSON(baseMallCooPosResp);
        DingDingMsg dingDingMsg = new DingDingMsg();
        Map<String,Object> param = new HashMap<>();
        param.put("content",msg);
        dingDingMsg.setText(param);
        dingDingMsg.setMsgtype("text");
        Response<DingDingResponse> execute = dingDingRemoteHttpApi.pushMsg(dingDingMsg).execute();
        log.info("推送消息返回结果：{}",execute.body());
    }
}
