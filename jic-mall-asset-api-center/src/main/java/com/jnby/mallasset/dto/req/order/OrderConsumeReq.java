package com.jnby.mallasset.dto.req.order;

import com.jnby.mallasset.api.dto.BaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("订单加积分请求入参")
public class OrderConsumeReq extends BaseReq {

    @ApiModelProperty(value = "订单编号",required = true)
    private String ordNum;
    @ApiModelProperty(value = "订单付款编号")
    private String ordPayNo;
    @ApiModelProperty(value = "会员手机号",required = true)
    private String memberTel;
    @ApiModelProperty(value = "会员姓名或昵称")
    private String memberName;
    @ApiModelProperty(value = "外部会员编码")
    private String outerMemberId;
    @ApiModelProperty(value = "订单实付金额",required = true)
    private BigDecimal ordAmount;
    @ApiModelProperty(value = "商品数量")
    private int totQty;
    @ApiModelProperty(value = "商品总价格")
    private BigDecimal totPriceList;
    @ApiModelProperty(value = "订单总金额，包括券")
    private BigDecimal totAmtActual;
    @ApiModelProperty(value = "订单完成时间",required = true)
    private Long ordFinishTime;
    @ApiModelProperty(value = "会员openID")
    private String ordOpenId;
    @ApiModelProperty(value = "回调地址")
    private String notifyUrl;
    @ApiModelProperty(value = "订单渠道：微商城、BOX")
    private String ordChannel;
    @ApiModelProperty(value = "订单商品列表")
    private List<ProductItem> productItemList;
    @ApiModelProperty(value = "订单付款列表")
    private List<PayItem> payItemList;
}
