package com.jnby.mallasset.remote.wanke.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class WanKeOrderReqEntity implements Serializable {
    @ApiModelProperty(value = "订单号(系统内唯一标识)")
    @JSONField(ordinal = 1)
    private String orderNo;
    @ApiModelProperty(value = "印享星商户编码")
    @JSONField(ordinal = 2)
    private String storeCode;
    @ApiModelProperty(value = "订单渠道 1：pos，2口碑，3点评，5购啊购")
    @JSONField(ordinal = 3)
    private String channel;
    @ApiModelProperty(value = "会员ID")
    @JSONField(ordinal = 4)
    private String memberId;
    @ApiModelProperty(value = "会员手机号")
    @JSONField(ordinal = 5)
    private String telephone;
    @ApiModelProperty(value = "总金额(单位：分)，非退款单必输")
    @JSONField(ordinal = 6)
    private String totalAmount;
    @ApiModelProperty(value = "交易金额(单位：分)，退款单为退款金额")
    @JSONField(ordinal = 7)
    private String payAmount;
    @ApiModelProperty(value = "支付信息")
    @JSONField(ordinal = 8)
    private List<Map> paymentInfo;
    @ApiModelProperty(value = "0：支付单，3：退款单")
    @JSONField(ordinal = 9)
    private String type;
    @ApiModelProperty(value = "交易时间")
    @JSONField(ordinal = 10)
    private String orderTime;
}
