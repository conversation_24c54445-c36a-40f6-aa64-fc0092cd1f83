package com.jnby.mallasset.strategy.category;

import com.jnby.mallasset.strategy.context.PointContext;
import com.jnby.mallasset.strategy.context.UserContext;

public interface IBasePointsService {
    /**
     * 获取总积分
     */
    Long getPoints(UserContext user);

    /**
     * 增加积分
     */
    Boolean usePoints(UserContext user, PointContext pointContext);

    /**
     * 减少积分
     */
    Boolean returnPoints(UserContext user, PointContext pointContext);
}
