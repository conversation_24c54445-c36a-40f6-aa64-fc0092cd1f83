package com.jnby.mallasset.remote.kaide.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BaseKaiDeResp<T> implements Serializable {
    @ApiModelProperty(value = "结果")
    private int errorCode;
    @ApiModelProperty(value = "返回消息")
    private String message;
    @ApiModelProperty("响应数据")
    private T body;

    public boolean isSuccess(){
        if (this.errorCode == 0) {
            return true;
        }
        return false;
    }
}
