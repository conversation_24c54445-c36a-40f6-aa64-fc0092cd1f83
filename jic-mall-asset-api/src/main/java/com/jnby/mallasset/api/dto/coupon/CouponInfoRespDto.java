package com.jnby.mallasset.api.dto.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("可用券列表回参")
@Data
public class CouponInfoRespDto implements Serializable {
    @ApiModelProperty("券模板号")
    private String couponTemplateNo;
    @ApiModelProperty("券号")
    private String couponNo;
    @ApiModelProperty("券标题(名称)")
    private String couponName;
    @ApiModelProperty("过期时间")
    private String overdueTime;
    @ApiModelProperty("减免金额(元)")
    private String reduceMoney;
    @ApiModelProperty("抵扣条件(消费满多少元可用),单位[元]")
    private String threshold;
}
