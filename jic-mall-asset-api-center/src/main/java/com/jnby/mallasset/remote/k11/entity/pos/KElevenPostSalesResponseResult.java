package com.jnby.mallasset.remote.k11.entity.pos;

import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Root;

@Data
@Root(name = "Result" )
public class KElevenPostSalesResponseResult {

    @Element(name = "Result" , required = false)
    private String result;
    @Element(name = "ErrorCode" , required = false)
    private int errorCode;
    @Element(name = "ErrorMessage" , required = false)
    private String errorMessage;
    @Element(name = "ResponseTime" , required = false)
    private String responseTime;
}
