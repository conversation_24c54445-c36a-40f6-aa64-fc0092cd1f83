package com.jnby.mallasset.sdk.util;

import com.alibaba.fastjson.JSON;
import com.jnby.mallasset.sdk.model.WhaleResponse;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

/**
 * <AUTHOR>
 */
public class PostUtil {

    public String doGet(String path) {

        HttpRequestBase request = new HttpGet(path);

        HttpResponse response = null;
        try {
            response = getHttpClient().execute(request);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        StringBuilder sb = new StringBuilder();
        try {
            String s;
            BufferedReader br = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
            while ((s = br.readLine()) != null) {
                sb.append(s);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return sb.toString();
    }


    /**
     * 执行请求
     *
     * @param path
     * @param categoryType
     * @param body
     * @param appId
     * @param secret
     * @param iv
     * @return
     */
    public WhaleResponse doPost(String path, int categoryType, String body, String appId, String secret, String iv) {

        //region 加密

        try {
            body = AesUtil.aesEncrypt(body, secret, iv);
            System.out.println("AES加密后body：" + body);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(JSON.toJSONString(e));
        }

        //endregion

        //region 签名

        String sign = Md5Util.getMd5Hash(body + "&" + secret);
        System.out.println("MD5加密后签名：" + sign);
        //endregion

        String url = (path.endsWith("/") ? path : path + "/") + categoryType;

        HttpEntityEnclosingRequestBase request = new HttpPost(url);
        request.addHeader("content-type", "application/json; charset=utf-8");
        request.addHeader("appId", appId);
        request.addHeader("sign", sign);
        try {
            request.setEntity(new StringEntity(body));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            System.out.println(JSON.toJSONString(e));
        }
        HttpResponse response = null;
        try {
            response = getHttpClient().execute(request);
        } catch (IOException e) {
            e.printStackTrace();
            System.out.println(JSON.toJSONString(e));
        }

        StringBuilder sb = new StringBuilder();
        try {
            BufferedReader br = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
            String s;
            while ((s = br.readLine()) != null) {
                sb.append(s);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        String result = sb.toString();
        return JSON.parseObject(result, WhaleResponse.class);
    }

    /**
     * build ssl HttpClient
     *
     * @return
     */
    private HttpClient getHttpClient() {
        SSLContextBuilder builder = new SSLContextBuilder();
        try {
            builder.loadTrustMaterial(null, new TrustStrategy() {
                @Override
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    return true;
                }
            });
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (KeyStoreException e) {
            e.printStackTrace();
        }
        SSLConnectionSocketFactory sslSF = null;
        try {
            sslSF = new SSLConnectionSocketFactory(builder.build(), NoopHostnameVerifier.INSTANCE);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        }
        return HttpClients.custom().setSSLSocketFactory(sslSF).build();
    }
}
