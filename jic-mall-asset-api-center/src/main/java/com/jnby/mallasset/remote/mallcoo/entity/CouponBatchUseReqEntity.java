package com.jnby.mallasset.remote.mallcoo.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@ApiModel("批量使用券入参")
@Data
public class CouponBatchUseReqEntity implements Serializable {

    @ApiModelProperty("核销方 2：商场 3：商家")
    @JSONField(ordinal = 1, name = "Verification")
    private String Verification;
    @ApiModelProperty("猫酷核销商户ID")
    @JSONField(ordinal = 2, name = "McShopID")
    private Long McShopID;
    @ApiModelProperty("是否全部成功")
    @JSONField(ordinal = 3, name = "IsAllSuccess")
    private Boolean IsAllSuccess;
    @ApiModelProperty("使用券列表")
    @JSONField(ordinal = 4, name = "UseInfoList")
    private List<UseInfoList> UseInfoList;
    @ApiModelProperty("外部订单编号")
    @JSONField(ordinal = 5, name = "OrderNo")
    private String OrderNo;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class UseInfoList implements Serializable {
        @ApiModelProperty("券码")
        @JSONField(ordinal = 1, name = "VCode")
        private String VCode;
    }
}
