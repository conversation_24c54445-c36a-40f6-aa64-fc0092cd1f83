package com.jnby.mallasset.remote.drc.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DrcMemberRegisterReqEntity.java
 * 文件简介: 订单消费请求
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class DrcOrderReqEntity implements Serializable {
    @ApiModelProperty(value = "商场ID", required = true)
    private String mall_id;
    @ApiModelProperty(value = "会员手机号", required = true)
    private String member_phone;
    @ApiModelProperty(value = "会员号", required = true)
    private String member_code;
    @ApiModelProperty(value = "店铺号", required = true)
    private String store_code;
    @ApiModelProperty(value = "销售金额(单位:元;支持小数)", required = true)
    private BigDecimal sales_amount;
    @ApiModelProperty(value = "销售单号", required = true)
    private String doc_no;
    @ApiModelProperty(value = "销售时间(yyyy-MM-dd HH:mm:ss)", required = true)
    private String sales_date_time;
    @ApiModelProperty(value = "原单号")
    private String oradoc_no;
    @ApiModelProperty(value = "支付方式:微信/支付宝/POS")
    private String payway;
    @ApiModelProperty(value = "数据来源:CRM", required = true)
    private String data_source;
    @ApiModelProperty(value = "唯一标识序号", required = true)
    private String uu_id;
    @ApiModelProperty(value = "操作人", required = true)
    private String operators;
}
