package com.jnby.mallasset.api.dto.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("可用券操作回参")
@Data
public class CouponOperationRespDto implements Serializable {
    @ApiModelProperty("券号")
    private String couponNo;
    @ApiModelProperty("追踪ID")
    private String traceId;
    @ApiModelProperty("成功状态")
    private Boolean success;
    @ApiModelProperty("失败信息")
    private String errorMsg;

}
