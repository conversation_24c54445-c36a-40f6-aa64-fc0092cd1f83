package com.jnby.mallasset.remote.haiancheng;


import com.jnby.mallasset.remote.haiancheng.entity.BaseHacResp;
import com.jnby.mallasset.remote.haiancheng.entity.HacMemberReqEntity;
import com.jnby.mallasset.remote.haiancheng.entity.HacMemberRespEntity;
import com.jnby.mallasset.remote.haiancheng.entity.HacOrderRespEntity;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.Map;

/**
 * 海岸城
 */
public interface IHacRemoteHttpApi {

    /**
     * 功能描述: 会员查询
     * 使用场景:
     *
     * @param headers
     * @param data
     * @return retrofit2.Call<com.jnby.mallasset.remote.haiancheng.entity.BaseHacResp<com.jnby.mallasset.remote.haiancheng.entity.HacMemberRespEntity>>
     * <AUTHOR>
     * @date 2025/1/13 9:56
     */
    @POST("/member/member/getByMobile")
    Call<BaseHacResp<HacMemberRespEntity>> queryMember(@HeaderMap Map<String, String> headers, @Body RequestBody data);

    /**
     * 功能描述: 会员注册
     * 使用场景:
     *
     * @param headers
     * @param data
     * @return retrofit2.Call<com.jnby.mallasset.remote.haiancheng.entity.BaseHacResp<com.jnby.mallasset.remote.haiancheng.entity.HacMemberRespEntity>>
     * <AUTHOR>
     * @date 2025/1/13 9:56
     */
    @POST("/member/member/registerForThirdParty")
    Call<BaseHacResp<HacMemberRespEntity>> registerForMember(@HeaderMap Map<String, String> headers, @Body RequestBody data);


    /**
     * 功能描述: 订单创建
     * 使用场景:
     *
     * @param headers
     * @param data
     * @return retrofit2.Call<com.jnby.mallasset.remote.haiancheng.entity.BaseHacResp<com.jnby.mallasset.remote.haiancheng.entity.HacMemberRespEntity>>
     * <AUTHOR>
     * @date 2025/1/13 9:56
     */
    @POST("/pos/thirdPartyStoreOrder/create")
    Call<BaseHacResp<HacOrderRespEntity>> orderCreate(@HeaderMap Map<String, String> headers, @Body RequestBody data);

    /**
     * 功能描述: 订单退款
     * 使用场景:
     *
     * @param headers
     * @param data
     * @return retrofit2.Call<com.jnby.mallasset.remote.haiancheng.entity.BaseHacResp<com.jnby.mallasset.remote.haiancheng.entity.HacMemberRespEntity>>
     * <AUTHOR>
     * @date 2025/1/13 9:56
     */
    @POST("/pos/thirdPartyStoreOrder/return")
    Call<BaseHacResp<HacOrderRespEntity>> orderRefund(@HeaderMap Map<String, String> headers, @Body RequestBody data);
}
