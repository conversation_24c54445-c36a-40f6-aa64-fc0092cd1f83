/*
 *      Copyright (c) 2018-2028, DreamLu All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: DreamLu 卢春梦 (<EMAIL>)
 */
package com.jnby.mallasset.util;

/**
 * char 常量池
 *
 * <AUTHOR>
 */
public interface CharPool {

	// @formatter:off
	char UPPER_A          = 'A';
	char LOWER_A          = 'a';
	char UPPER_Z          = 'Z';
	char LOWER_Z          = 'z';
	char DOT              = '.';
	char AT               = '@';
	char LEFT_BRACE       = '{';
	char RIGHT_BRACE      = '}';
	char LEFT_BRACKET     = '(';
	char RIGHT_BRACKET    = ')';
	char DASH             = '-';
	char PERCENT          = '%';
	char PIPE             = '|';
	char PLUS             = '+';
	char QUESTION_MARK    = '?';
	char EXCLAMATION_MARK = '!';
	char EQUALS           = '=';
	char AMPERSAND        = '&';
	char ASTERISK         = '*';
	char STAR             = ASTERISK;
	char BACK_SLASH       = '\\';
	char COLON            = ':';
	char COMMA            = ',';
	char DOLLAR           = '$';
	char SLASH            = '/';
	char HASH             = '#';
	char HAT              = '^';
	char LEFT_CHEV        = '<';
	char NEWLINE          = '\n';
	char N                = 'n';
	char Y                = 'y';
	char QUOTE            = '\"';
	char RETURN           = '\r';
	char TAB              = '\t';
	char RIGHT_CHEV       = '>';
	char SEMICOLON        = ';';
	char SINGLE_QUOTE     = '\'';
	char BACKTICK         = '`';
	char SPACE            = ' ';
	char TILDA            = '~';
	char LEFT_SQ_BRACKET  = '[';
	char RIGHT_SQ_BRACKET = ']';
	char UNDERSCORE       = '_';
	char ONE              = '1';
	char ZERO             = '0';
	// @formatter:on

}
