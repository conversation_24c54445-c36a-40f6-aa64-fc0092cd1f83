package com.jnby.mallasset.remote.kkmall;


import com.jnby.mallasset.remote.kkmall.entity.*;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.Map;

/**
 * 文件名: com.jnby.mallasset.remote.kkmall-IKkMallRemoteHttpApi.java
 * 文件简介: KKMALL平台
 *
 * <AUTHOR>
 * @date 2024/7/31 16:08
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface IKkMallRemoteHttpApi {
    /**
     * 功能描述: 获取用户token
     * 使用场景:
     *
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.kkmall.entity.BaseKkMallResp<com.jnby.mallasset.remote.kkmall.entity.UserTokenRespEntity>>
     * <AUTHOR>
     * @date 2024/7/31 17:31
     */
    @POST("/portal-api/op/sys/token")
    Call<BaseKkMallResp<UserTokenRespEntity>> userToken(@Body UserTokenReqEntity req);

    /**
     * 功能描述: 会员查询
     * 使用场景:
     *
     * @param headers
     * @param mobile
     * @return retrofit2.Call<com.jnby.mallasset.remote.kkmall.entity.BaseKkMallResp<com.jnby.mallasset.remote.kkmall.entity.KkMallMemberRespEntity>>
     * <AUTHOR>
     * @date 2024/7/31 17:19
     */
    @GET("/portal-api/op/member/query")
    Call<BaseKkMallResp<KkMallMemberRespEntity>> queryMember(@HeaderMap Map<String, String> headers, @Query("mobile") String mobile);

    /**
     * 功能描述: 会员注册
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.kkmall.entity.BaseKkMallResp<com.jnby.mallasset.remote.kkmall.entity.KkMallMemberRespEntity>>
     * <AUTHOR>
     * @date 2024/7/31 17:19
     */
    @POST("/portal-api/op/member/regist")
    Call<BaseKkMallResp<KkMallMemberRespEntity>> registMember(@HeaderMap Map<String, String> headers, @Body KkMallMemberReqEntity req);


    /**
     * 功能描述: 订单销售
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.kkmall.entity.BaseKkMallResp<com.jnby.mallasset.remote.kkmall.entity.KkMallOrderRespEntity>>
     * <AUTHOR>
     * @date 2024/8/1 16:13
     */
    @POST("/portal-api/op/sales/uploadSales")
    Call<BaseKkMallResp<KkMallOrderRespEntity>> orderSales(@HeaderMap Map<String, String> headers, @Body KkMallOrderReqEntity req);

    /**
     * 功能描述: 订单退货
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.kkmall.entity.BaseKkMallResp<com.jnby.mallasset.remote.kkmall.entity.KkMallOrderRespEntity>>
     * <AUTHOR>
     * @date 2024/8/1 16:13
     */
    @POST("/portal-api/op/sales/salesReturn")
    Call<BaseKkMallResp<KkMallOrderRefundRespEntity>> orderReturn(@HeaderMap Map<String, String> headers, @Body KkMallOrderRefundReqEntity req);

    /**
     * 功能描述: 订单退货校验
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.kkmall.entity.BaseKkMallResp<com.jnby.mallasset.remote.kkmall.entity.KkMallOrderRespEntity>>
     * <AUTHOR>
     * @date 2024/8/1 16:13
     */
    @POST("/portal-api/op/sales/salesReturnCheck")
    Call<BaseKkMallResp<KkMallOrderRefundRespEntity>> orderReturnCheck(@HeaderMap Map<String, String> headers, @Body KkMallOrderRefundReqEntity req);

    /**
     * 功能描述: 上传订单结算系统
     * 使用场景:
     *
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.kkmall.entity.BaseKkMallResp<com.jnby.mallasset.remote.kkmall.entity.KkMallOrderRespEntity>>
     * <AUTHOR>
     * @date 2024/8/1 16:13
     */
    @POST("http://kcdata.kingkeybanner.com:8185/SalesTrans/rest/salestransaction/salestranslitev61")
    Call<KkMallUploadOrderResp> uploadOrderInfo(@Body KkMallUploadOrderInfo req);
}
