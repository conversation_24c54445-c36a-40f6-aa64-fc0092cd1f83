package com.jnby.mallasset.module.model.bojun;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: CodeGenerator
 * @Date: 2024-08-20 17:59:31
 * @Description: 
 */
@TableName("C_STORE")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="CStore对象", description="")
public class CStore implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private Long id;
    @TableField("AD_CLIENT_ID")
    private Long adClientId;
    @TableField("AD_ORG_ID")
    private Long adOrgId;
    @TableField("ISACTIVE")
    private String isactive;
    @TableField("MODIFIERID")
    private Long modifierid;
    @TableField("CREATIONDATE")
    private Date creationdate;
    @TableField("MODIFIEDDATE")
    private Date modifieddate;
    @TableField("OWNERID")
    private Long ownerid;
    @TableField("NAME")
    private String name;
    @TableField("DESCRIPTION")
    private String description;
    @TableField("C_AREA_ID")
    private Long cAreaId;
    @TableField("LOCKCASH")
    private BigDecimal lockcash;
    @TableField("ADDRESS")
    private String address;
    @TableField("PHONE")
    private String phone;
    @TableField("FAX")
    private String fax;
    @TableField("CONTACTOR_ID")
    private Long contactorId;
    @TableField("MONTHFEE")
    private BigDecimal monthfee;
    @TableField("ISSTOP")
    private String isstop;
    @TableField("RENTBEGIN")
    private String rentbegin;
    @TableField("RENTEND")
    private String rentend;
    @TableField("PROPORTION")
    private String proportion;
    @TableField("EMPCNT")
    private String empcnt;
    @TableField("CHECKDATE")
    private Integer checkdate;
    @TableField("ISCENTER")
    private String iscenter;
    @TableField("ISRETAIL")
    private String isretail;
    @TableField("MOBIL")
    private String mobil;
    @TableField("SNAME")
    private String sname;
    @TableField("POSTCAL")
    private BigDecimal postcal;
    @TableField("CALCULATION")
    private String calculation;
    @TableField("C_CUSTOMER_ID")
    private Long cCustomerId;
    @TableField("C_CUSTOMERUP_ID")
    private Long cCustomerupId;
    @TableField("C_PRICEAREA_ID")
    private Long cPriceareaId;
    @TableField("ISFAIRORIG")
    private String isfairorig;
    @TableField("AREAMNG_ID")
    private Long areamngId;
    @TableField("LIMITQTY")
    private Long limitqty;
    @TableField("LIMITAMT")
    private BigDecimal limitamt;
    @TableField("LIMITMO")
    private String limitmo;
    @TableField("MARKDIS")
    private BigDecimal markdis;
    @TableField("DATEBLOCK")
    private Integer dateblock;
    @TableField("C_STORETYPE_JZ_ID")
    private Long cStoretypeJzId;
    @TableField("IMGURL1")
    private String imgurl1;
    @TableField("IMGURL2")
    private String imgurl2;
    @TableField("IMGURL3")
    private String imgurl3;
    @TableField("IMGURL4")
    private String imgurl4;
    @TableField("IMGURL5")
    private String imgurl5;
    @TableField("BIGAREAMNG_ID")
    private Long bigareamngId;
    @TableField("C_PROVINCE_ID")
    private Long cProvinceId;
    @TableField("C_CITY_ID")
    private Long cCityId;
    @TableField("STORESIGN")
    private BigDecimal storesign;
    @TableField("C_STORETYPE")
    private String cStoretype;
    @TableField("REMARK")
    private String remark;
    @TableField("CODE")
    private String code;
    @TableField("ISUFSTORE")
    private String isufstore;
    @TableField("C_STORE_ID")
    private Long cStoreId;
    @TableField("C_DEPARTMENT_ID")
    private Long cDepartmentId;
    @TableField("C_CLASSCODE_ID")
    private Long cClasscodeId;
    @TableField("UF_CODE")
    private String ufCode;
    @TableField("BILLDATE_FRIST")
    private Integer billdateFrist;
    @TableField("PRIORITY")
    private Long priority;
    @TableField("C_BLOCK_ID")
    private Long cBlockId;
    @TableField("ISFICTITIOUS")
    private String isfictitious;
    @TableField("SHOP_RECEIVE_TYPE")
    private Integer shopReceiveType;
    @TableField("POSPW")
    private String pospw;
    @TableField("ISBLOCK")
    private String isblock;
    @TableField("CLOPSTORETYPE")
    private String clopstoretype;
    @TableField("DISCOUNT")
    private BigDecimal discount;
    @TableField("CLOP_STORE")
    private String clopStore;
    @TableField("ISGIFT")
    private String isgift;
    @TableField("ISDISCOM")
    private String isdiscom;
    @TableField("C_DEPART_ID")
    private Long cDepartId;
    @TableField("IMP_MONTH")
    private Long impMonth;
    @TableField("IMP_TYPE1")
    private Long impType1;
    @TableField("IMP_TYPE2")
    private Long impType2;
    @TableField("IMP_TYPE3")
    private Long impType3;
    @TableField("TAXRATE")
    private BigDecimal taxrate;
    @TableField("C_STOREATTRIB1_ID")
    private Long cStoreattrib1Id;
    @TableField("C_STOREATTRIB2_ID")
    private Long cStoreattrib2Id;
    @TableField("C_STOREATTRIB3_ID")
    private Long cStoreattrib3Id;
    @TableField("C_STOREATTRIB4_ID")
    private Long cStoreattrib4Id;
    @TableField("C_STOREATTRIB5_ID")
    private Long cStoreattrib5Id;
    @TableField("C_STOREATTRIB6_ID")
    private Long cStoreattrib6Id;
    @TableField("C_STOREATTRIB7_ID")
    private Long cStoreattrib7Id;
    @TableField("C_STOREATTRIB8_ID")
    private Long cStoreattrib8Id;
    @TableField("C_STOREATTRIB9_ID")
    private Long cStoreattrib9Id;
    @TableField("C_STOREATTRIB10_ID")
    private Long cStoreattrib10Id;
    @TableField("C_STOREATTRIB11_ID")
    private Long cStoreattrib11Id;
    @TableField("C_STOREATTRIB12_ID")
    private Long cStoreattrib12Id;
    @TableField("C_STOREATTRIB13_ID")
    private Long cStoreattrib13Id;
    @TableField("C_STOREATTRIB14_ID")
    private Long cStoreattrib14Id;
    @TableField("C_STOREATTRIB15_ID")
    private Long cStoreattrib15Id;
    @TableField("C_STOREATTRIB16_ID")
    private Long cStoreattrib16Id;
    @TableField("C_STOREATTRIB17_ID")
    private Long cStoreattrib17Id;
    @TableField("C_STOREATTRIB18_ID")
    private Long cStoreattrib18Id;
    @TableField("C_STOREATTRIB19_ID")
    private Long cStoreattrib19Id;
    @TableField("C_STOREATTRIB20_ID")
    private Long cStoreattrib20Id;
    @TableField("ISNEGATIVE")
    private String isnegative;
    @TableField("TDEFDOWNTYPE_ID")
    private Long tdefdowntypeId;
    @TableField("C_QTYADDAREA_ID")
    private Long cQtyaddareaId;
    @TableField("C_CORP_ID")
    private Long cCorpId;
    @TableField("USBKEY")
    private String usbkey;
    @TableField("IFEBSTORE")
    private String ifebstore;
    @TableField("DATE_ENDACCOUNT")
    private Integer dateEndaccount;
    @TableField("ISSTOCK")
    private String isstock;
    @TableField("Y_STORE")
    private String yStore;
    @TableField("C_VIPTYPE_ID1")
    private String cViptypeId1;
    @TableField("IF_WMS")
    private String ifWms;
    @TableField("IFORDERSTORE")
    private String iforderstore;
    @TableField("ORDERLIMITDATE")
    private Long orderlimitdate;
    @TableField("WEBPOSLOGINURL")
    private String webposloginurl;
    @TableField("COMPTYPE")
    private Long comptype;
    @TableField("C_STORE_SQL")
    private String cStoreSql;
    @TableField("M_DIM1_ID")
    private Long mDim1Id;
    @TableField("STORETYPE")
    private String storetype;
    @TableField("RETCHKORG")
    private String retchkorg;
    @TableField("MARKET")
    private String market;
    @TableField("C_STOREGRADE_ID")
    private Long cStoregradeId;
    @TableField("C_STOREKIND_ID")
    private Long cStorekindId;
    @TableField("C_INTEGRALAREA_ID")
    private Long cIntegralareaId;
    @TableField("FRAMWORK_AREA_ID")
    private Long framworkAreaId;
    @TableField("C_ARCBRAND_ID")
    private Long cArcbrandId;
    @TableField("IS_RESTORE")
    private String isRestore;
    @TableField("IS_MARK")
    private String isMark;
    @TableField("IS_RET")
    private String isRet;
    @TableField("WEBPOS_OFFLINE")
    private String webposOffline;
    @TableField("LOWEST_DISCOUNT")
    private String lowestDiscount;
    @TableField("CHK_OVERDAYS")
    private Long chkOverdays;
    @TableField("C_MARKBALTYPE_ID")
    private Long cMarkbaltypeId;
    @TableField("ISVIPINTL")
    private String isvipintl;
    @TableField("ISVIPDIS")
    private String isvipdis;
    @TableField("ISONLYCARD")
    private String isonlycard;
    @TableField("C_PAYWAY_DEFAULT")
    private Long cPaywayDefault;
    @TableField("CREDITLIMIT")
    private Long creditlimit;
    @TableField("EB_CREDITRANK_ID")
    private Long ebCreditrankId;
    @TableField("EB_BONUSTYPE_ID")
    private Long ebBonustypeId;
    @TableField("IS_TAOBAO")
    private String isTaobao;
    @TableField("EB_SHIPTYPE_ID")
    private Long ebShiptypeId;
    @TableField("SHELFDEPTH")
    private Long shelfdepth;
    @TableField("IS_MORESALESREP")
    private String isMoresalesrep;
    @TableField("DIM1_FILTER")
    private String dim1Filter;
    @TableField("C_POSADDR_ID")
    private Long cPosaddrId;
    @TableField("POSPRINT")
    private String posprint;
    @TableField("IS_MODIFYPAYAMT")
    private String isModifypayamt;
    @TableField("OPENDATE")
    private Integer opendate;
    @TableField("LEASEPERIOD")
    private String leaseperiod;
    @TableField("ENDSALE")
    private String endsale;
    @TableField("USEMONTH")
    private String usemonth;
    @TableField("CONTRACT")
    private String contract;
    @TableField("ENDDATE")
    private Integer enddate;
    @TableField("RENCOST")
    private String rencost;
    @TableField("BEARCOMPANY_ID")
    private Long bearcompanyId;
    @TableField("CHARGEOF_ID")
    private Long chargeofId;
    @TableField("ALIPAY_KEY")
    private String alipayKey;
    @TableField("ALIPAY_PARTNERID")
    private String alipayPartnerid;
    @TableField("ALIPAY_SELL_MAIL")
    private String alipaySellMail;
    @TableField("IS_MARKETNO")
    private String isMarketno;
    @TableField("IS_MANUALINT")
    private String isManualint;
    @TableField("CONTACTOR")
    private String contactor;
    @TableField("BILLDATERANGE")
    private String billdaterange;
    @TableField("LONGITUDE")
    private BigDecimal longitude;
    @TableField("LATITUDE")
    private BigDecimal latitude;
    @TableField("IS_CREATEDATA")
    private String isCreatedata;
    @TableField("IS_TONC")
    private String isTonc;
    @TableField("IS_COMPARED")
    private String isCompared;
    @TableField("PREOPENDATE")
    private Integer preopendate;
    @TableField("C_STOREATTRIB21_ID")
    private Long cStoreattrib21Id;
    @TableField("IS_PROCLOSE")
    private String isProclose;
    @TableField("IS_CHECKALIAS")
    private String isCheckalias;
    @TableField("C_CONSUMEAREA_ID")
    private Long cConsumeareaId;
    @TableField("IS_COUNTER")
    private String isCounter;
    @TableField("IS_EXCSTORE")
    private String isExcstore;
    @TableField("ORGMODIFYPER")
    private String orgmodifyper;
    @TableField("C_DISTRICT_ID")
    private Long cDistrictId;
    @TableField("WECHAT_CUSTOMERID")
    private String wechatCustomerid;
    @TableField("SUB_MCH_ID")
    private String subMchId;
    @TableField("ISAIXIU")
    private String isaixiu;
    @TableField("IS_SMARTPAY")
    private String isSmartpay;
    @ApiModelProperty(value = "HR组织ID")
    @TableField("HR_GROUP_ID")
    private Long hrGroupId;
    @TableField("EB_STORAGE_TYPE")
    private Integer ebStorageType;
    @TableField("IS_JSTYPE")
    private String isJstype;
    @TableField("IS_BCLOUD_STORE")
    private String isBcloudStore;
    @TableField("RETAIL_RET_DAY")
    private Long retailRetDay;
    @TableField("Q_NVL")
    private String qNvl;
    @TableField("C_MALL_ID")
    private Long cMallId;
    @TableField("Q_PASSWORD")
    private String qPassword;
    @TableField("Q_SMS")
    private String qSms;
    @TableField("Q_CODE")
    private String qCode;
    @TableField("C_BIGAREA_ID")
    private Long cBigareaId;
    @TableField("IS_BANKBUY")
    private String isBankbuy;
    @TableField("MAINMEDIAADDRESS")
    private String mainmediaaddress;
    @TableField("CHANGE")
    private Long change;
    @TableField("CURRENCY")
    private String currency;
    @TableField("CURRENCY_SIGN")
    private String currencySign;
    @TableField("ISRETPAY")
    private String isretpay;
    @TableField("SF_CARDNO")
    private String sfCardno;
    @TableField("C_PAYWAY_FILTER")
    private String cPaywayFilter;
    @TableField("IS_ALLPAYWAY")
    private String isAllpayway;
    @TableField("IS_DISSTORE")
    private String isDisstore;
    @TableField("IS_UNIONSTORE")
    private String isUnionstore;
    @TableField("RETAIL_LOCATION")
    private String retailLocation;
    @TableField("IS_MORECURRENCY")
    private String isMorecurrency;
    @TableField("C_CURRENCY_ID")
    private Long cCurrencyId;
    @TableField("POS_AUTO_DIS")
    private String posAutoDis;
    @TableField("SUBSYSTEM_NAME")
    private String subsystemName;
    @TableField("MENU_LIST")
    private String menuList;
    @TableField("ISRET_LEVEL_ON")
    private String isretLevelOn;
    @TableField("ISPADPOS")
    private String ispadpos;
    @TableField("ISKEEP_PWD")
    private String iskeepPwd;
    @TableField("DATE_HOUR_OFFSET")
    private Integer dateHourOffset;
    @TableField("POS_SERIALNO")
    private String posSerialno;
    @TableField("IS_HPMART")
    private String isHpmart;
    @TableField("SHOPID")
    private String shopid;
    @TableField("SHOPCODE")
    private String shopcode;
    @TableField("PRODUCTCODE")
    private String productcode;
    @TableField("Q_CODE_POS")
    private String qCodePos;
    @TableField("GUESTMACHINECOM")
    private String guestmachinecom;
    @TableField("GUESTMACHINE")
    private Integer guestmachine;
    @TableField("ISMUSTENTERVIP")
    private String ismustentervip;
    @TableField("CHKDAY")
    private Integer chkday;
    @TableField("MALLCODE")
    private String mallcode;
    @TableField("COUNTERNUM")
    private String counternum;
    @TableField("EB_EXPRESS_ID")
    private Long ebExpressId;
    @TableField("SDTSTORE")
    private String sdtstore;
    @TableField("IS_BPAYSHOWVOUCHER")
    private String isBpayshowvoucher;
    @TableField("WECHAT_CUSTOMERIDNEW")
    private String wechatCustomeridnew;
    @TableField("C_COUNTRY_ID")
    private Long cCountryId;
    @TableField("IS_O2O")
    private String isO2o;
    @TableField("ALLOW_RECEIPT")
    private String allowReceipt;
    @TableField("IS_MODIFYAMT_REASON")
    private String isModifyamtReason;
    @TableField("BPOSEX")
    private String bposex;
    @TableField("ISSDT")
    private String issdt;
    @TableField("DXLX")
    private String dxlx;
    @TableField("C_UNIONSTORE_ID")
    private Long cUnionstoreId;
    @TableField("IS_AUTOIN")
    private String isAutoin;
    @TableField("DEFAULT_CHARGEPAYWAY")
    private Long defaultChargepayway;
    @TableField("DEFAULTLASTDATE")
    private String defaultlastdate;
    @TableField("INVOICE_TEMPLATE")
    private String invoiceTemplate;
    @TableField("IS_SHOWVISITPLAN")
    private String isShowvisitplan;
    @TableField("INVOICE_TAXNO")
    private String invoiceTaxno;
    @TableField("CHECKDATADOWN")
    private Integer checkdatadown;
    @TableField("PORDERLIMITDATE")
    private Long porderlimitdate;
    @TableField("IS_CHECKSKUONLINE")
    private String isCheckskuonline;
    @TableField("COMBINEAFTER")
    private String combineafter;
    @TableField("IS_REFRESHNETWORK")
    private String isRefreshnetwork;
    @TableField("IS_FORCEBPOS")
    private String isForcebpos;
    @TableField("DESCRIPTION1")
    private String description1;
    @TableField("IS_OXO")
    private String isOxo;
    @TableField("JIT_STORECODE")
    private String jitStorecode;
    @TableField("C_DISTR__YXJ_ID")
    private Long cDistrYxjId;
    @TableField("IS_FILTERVIPTYPE")
    private String isFilterviptype;
    @TableField("IS_VIP_CHECK")
    private String isVipCheck;
    @TableField("IS_BRITHDAYDIS")
    private String isBrithdaydis;
    @TableField("BROWSER_OPEN_MODE")
    private String browserOpenMode;
    @TableField("IS_INTEGRAL_BASE")
    private String isIntegralBase;
    @TableField("IS_ORDER")
    private String isOrder;
    @TableField("CAN_GRAB")
    private String canGrab;
    @TableField("IS_PERFORMANCE_SHARE")
    private String isPerformanceShare;
    @TableField("MORLTHENOTE")
    private String morlthenote;
    @TableField("ISMORLTHENOTEP")
    private String ismorlthenotep;
    @TableField("IS_CONTROL")
    private String isControl;
    @TableField("IS_PRINT_ELECTRONICINVOICE")
    private String isPrintElectronicinvoice;
    @TableField("OPENVIP_AMT")
    private BigDecimal openvipAmt;
    @TableField("MORDERLIMITDATE")
    private Long morderlimitdate;
    @TableField("PRINTTPLS4SGLPDT")
    private String printtpls4sglpdt;
    @TableField("INVOICE_PHONE")
    private String invoicePhone;
    @TableField("INVOICE_ACCOUNT")
    private String invoiceAccount;
    @TableField("INVOICE_BANK")
    private String invoiceBank;
    @TableField("INVOICE_ADDR")
    private String invoiceAddr;
    @TableField("INVOICE_COM")
    private String invoiceCom;
    @TableField("MALL_NAME")
    private String mallName;
    @TableField("MALL_NO")
    private String mallNo;
    @TableField("MALL_ADDRESS")
    private String mallAddress;
    @TableField("IS_STONO")
    private String isStono;
    @TableField("IS_WMSSTORE")
    private String isWmsstore;
    @TableField("WMS_STORECODE")
    private String wmsStorecode;
    @TableField("IS_TOWMS")
    private String isTowms;
    @TableField("IS_MARKET")
    private String isMarket;
    @TableField("C_REALSTORE_ID")
    private Long cRealstoreId;
    @TableField("DESCRIPTION01")
    private String description01;
    @TableField("DESCRIPTION02")
    private String description02;
    @TableField("DESCRIPTION03")
    private String description03;
    @TableField("DESCRIPTION04")
    private String description04;
    @TableField("DESCRIPTION05")
    private String description05;
    @TableField("MERCHANT_NUMBER")
    private String merchantNumber;
    @TableField("PAYMENT_KEY")
    private String paymentKey;
    @TableField("IS_DELIVERY")
    private String isDelivery;
    @TableField("DESKEY")
    private String deskey;
    @TableField("CARRYMODE")
    private String carrymode;
    @TableField("IS_SELECTPRINT")
    private String isSelectprint;
    @TableField("PRINTTEMPLATELIST")
    private String printtemplatelist;
    @TableField("IS_EXAMINATION")
    private String isExamination;
    @TableField("SHOUQIANBACODE")
    private String shouqianbacode;
    @TableField("WECHAT_CUSTOMERIDNEW2")
    private String wechatCustomeridnew2;
    @TableField("SHOUQIANBA_USE")
    private String shouqianbaUse;
    @TableField("O2OVOICE")
    private String o2ovoice;
    @TableField("PENDVOICE")
    private String pendvoice;
    @TableField("MERCHANT")
    private String merchant;
    @TableField("VIPPRINTTEMPLATE")
    private String vipprinttemplate;
    @TableField("JTK")
    private String jtk;
    @TableField("RECHARGE")
    private String recharge;
    @TableField("COMFIRM_BEFOREPAY")
    private String comfirmBeforepay;
    @TableField("MONITOR_URL")
    private String monitorUrl;
    @TableField("MANAGERNAV_OPEN_MODE")
    private String managernavOpenMode;
    @TableField("VOUCHER_STORE_TYPE")
    private String voucherStoreType;
    @TableField("ISMONENY")
    private String ismoneny;
    @TableField("MOBILEPAYSOLUTION")
    private String mobilepaysolution;
    @TableField("INTERNAL_PURCHASE_STORE")
    private String internalPurchaseStore;
    @TableField("PADPOS_TEMPLATE")
    private String padposTemplate;
    @TableField("ALLOW_CUSTOMER")
    private String allowCustomer;
    @TableField("BRANCHIDCARD")
    private String branchidcard;
    @TableField("POSIDCARD")
    private String posidcard;
    @TableField("ONLINEORDER")
    private String onlineorder;
    @TableField("VIP_ACTIVATE")
    private String vipActivate;
    @TableField("IS_WADE")
    private String isWade;
    @TableField("ISPRINTHOLDRETAIL")
    private String isprintholdretail;
    @TableField("MD5")
    private String md5;
    @TableField("MISPOSCARD")
    private String misposcard;
    @TableField("MISPOSTERMINAL")
    private String misposterminal;
    @TableField("MISPOSVISION")
    private String misposvision;
    @TableField("WECHAT_CUSTOMERIDNEW3")
    private String wechatCustomeridnew3;
    @TableField("WECHAT_CUSTOMERIDNEW4")
    private String wechatCustomeridnew4;
    @TableField("CCB_POSB_TERM_NO")
    private String ccbPosbTermNo;
    @TableField("USER_ID")
    private String userId;
    @TableField("LANDI_UNIONPAY")
    private String landiUnionpay;
    @TableField("WECHAT_CUSTOMERIDNEW5")
    private String wechatCustomeridnew5;
    @TableField("POSBTOC_POSB_TERM_NO")
    private String posbtocPosbTermNo;
    @TableField("ISMOBILEPAYS")
    private String ismobilepays;
    @TableField("CONSUMER_CARD_PAY_TYPE")
    private String consumerCardPayType;
    @TableField("PAYWEB_APPKEY")
    private String paywebAppkey;
    @TableField("MANUALLYENTER")
    private String manuallyenter;
    @TableField("IS_VIP")
    private String isVip;
    @TableField("C_STOREATTRIB22_ID")
    private Long cStoreattrib22Id;
    @TableField("IS_YUNDONG")
    private String isYundong;
    @TableField("IS_PENGMA")
    private String isPengma;
    @TableField("INVENTORY_YEAR")
    private String inventoryYear;
    @TableField("WAREHOUSE_RANG")
    private String warehouseRang;
    @TableField("CLOSEDATE")
    private Integer closedate;
    @TableField("IS_TINGYE")
    private String isTingye;
    @TableField("C_STOREATTRIB24_ID")
    private String cStoreattrib24Id;
    @TableField("C_STOREATTRIB25_ID")
    private String cStoreattrib25Id;
    @TableField("C_STOREATTRIB26_ID")
    private String cStoreattrib26Id;
    @TableField("C_STOREATTRIB27_ID")
    private String cStoreattrib27Id;
    @TableField("C_STOREATTRIB28_ID")
    private String cStoreattrib28Id;
    @TableField("C_STOREATTRIB29_ID")
    private String cStoreattrib29Id;
    @TableField("C_STOREATTRIB30_ID")
    private Integer cStoreattrib30Id;
    @TableField("C_STOREATTRIB31_ID")
    private String cStoreattrib31Id;
    @TableField("C_STOREATTRIB32_ID")
    private String cStoreattrib32Id;
    @TableField("C_STOREATTRIB33_ID")
    private String cStoreattrib33Id;
    @TableField("C_STOREATTRIB34_ID")
    private String cStoreattrib34Id;
    @TableField("C_STOREATTRIB35_ID")
    private String cStoreattrib35Id;
    @TableField("C_STOREATTRIB36_ID")
    private String cStoreattrib36Id;
    @TableField("C_STOREATTRIB37_ID")
    private String cStoreattrib37Id;
    @TableField("C_STOREATTRIB38_ID")
    private String cStoreattrib38Id;
    @TableField("C_STOREATTRIB39_ID")
    private String cStoreattrib39Id;
    @TableField("HEDIAN_PINPAI")
    private String hedianPinpai;
    @TableField("MARKET_NAME")
    private String marketName;
    @TableField("YEAR_RENT")
    private String yearRent;
    @TableField("DEC_OPENDATE")
    private Integer decOpendate;
    @TableField("TAX_DIS")
    private String taxDis;
    @TableField("YEAR_AMT_BOT")
    private String yearAmtBot;
    @TableField("CONTRACT_STATUS")
    private String contractStatus;
    @TableField("CLOSE_RESON")
    private String closeReson;
    @TableField("DISPLAY")
    private String display;
    @TableField("STORE_TYPE")
    private String storeType;
    @TableField("IS_BUS_LICENSE")
    private String isBusLicense;
    @TableField("C_STOREATTRIB23_ID")
    private Long cStoreattrib23Id;
    @TableField("AREAMNG_NEW")
    private String areamngNew;
    @TableField("BIGAREAMNG_NEW")
    private String bigareamngNew;
    @TableField("TAX_NATURE")
    private String taxNature;
    @TableField("GRADE")
    private String grade;
    @TableField("IS_YTONO")
    private String isYtono;
    @TableField("DEFAULT01")
    private String default01;
    @TableField("DEFAULT02")
    private String default02;
    @TableField("DEFAULT04")
    private String default04;
    @TableField("DEFAULT05")
    private String default05;
    @TableField("DEFAULT06")
    private String default06;
    @TableField("DEFAULT15")
    private String default15;
    @TableField("DEFAULT03")
    private BigDecimal default03;
    @TableField("DEFAULT07")
    private String default07;
    @TableField("DEFAULT08")
    private String default08;
    @TableField("DEFAULT09")
    private String default09;
    @TableField("DEFAULT10")
    private String default10;
    @TableField("DEFAULT11")
    private String default11;
    @TableField("DEFAULT13")
    private String default13;
    @TableField("DEFAULT14")
    private String default14;
    @TableField("DEFAULT12")
    private String default12;
    @TableField("OP_DEVICE_ID")
    private String opDeviceId;
    @TableField("ISINVOICE")
    private String isinvoice;
    @TableField("RETURNPRICE")
    private String returnprice;
    @TableField("ZX_CUSTOMERID")
    private String zxCustomerid;
    @TableField("INVOICEURL")
    private String invoiceurl;
    @TableField("SBSNUM")
    private String sbsnum;
    @TableField("ISUSERRFID")
    private String isuserrfid;
    @TableField("SUBORDERZT")
    private String suborderzt;
    @TableField("CAN_GRAB_NOCAN")
    private String canGrabNocan;
    @TableField("IS_UNIQUE")
    private String isUnique;
    @TableField("IS_CHECKSKUECRM")
    private String isCheckskuecrm;
    @TableField("IS_TORFID")
    private String isTorfid;
    @TableField("USE_POS")
    private String usePos;
    @ApiModelProperty(value = "BPOS零售流水码是否从仓库发出")
    @TableField("ISUNIREC")
    private String isunirec;
    @ApiModelProperty(value = "京东城市仓")
    @TableField("C_STORE_JD_ID")
    private Long cStoreJdId;
    @ApiModelProperty(value = "备注")
    @TableField("REMARK_JD")
    private String remarkJd;
    @ApiModelProperty(value = "物流公司")
    @TableField("EB_EXPRESS_JD_ID")
    private Long ebExpressJdId;
    @TableField("USEPOSITION")
    private String useposition;
    @TableField("POSITIONMD")
    private String positionmd;
    @TableField("FH_BRAND")
    private String fhBrand;
    @TableField("INVOICE_COMPANY")
    private String invoiceCompany;
    @TableField("WECHAT_CUSTOMERIDNEW5_2")
    private String wechatCustomeridnew52;
    @TableField("POSBTOC_POSB_TERM_NO_2")
    private String posbtocPosbTermNo2;
    @TableField("DATUM_NUMBER")
    private String datumNumber;
    @TableField("VOU_INPUT_TYPE")
    private String vouInputType;
    @TableField("INIT_INPUT_TYPE")
    private String initInputType;
    @TableField("STORE_CLERK")
    private String storeClerk;
    @TableField("STORE_SALEACCOUNT")
    private String storeSaleaccount;
    @TableField("STORE_SALETAXNUM")
    private String storeSaletaxnum;
    @TableField("OLD_CODE")
    private String oldCode;
    @TableField("SMALL_ROUTINE")
    private String smallRoutine;
    @TableField("CHECK_CODE")
    private String checkCode;
    @TableField("OLD_XSQY")
    private String oldXsqy;
    @TableField("STORE_NAME")
    private String storeName;
    @TableField("FLOOR_PLAN")
    private String floorPlan;
    @TableField("SEATING_IMG")
    private String seatingImg;
    @TableField("TOSOLVED")
    private String tosolved;
    @TableField("IS_JPC")
    private String isJpc;
    @TableField("ZT_MONTHCODE")
    private String ztMonthcode;
    @TableField("C_STOREGS_ID")
    private Long cStoregsId;
    @TableField("IS_TM")
    private String isTm;
    @TableField("TRANIN_STORE_ID")
    private Long traninStoreId;
    @TableField("IS_CSC")
    private String isCsc;
    @TableField("C_STOREATTRIB40_ID")
    private Long cStoreattrib40Id;
    @TableField("C_STOREATTRIB41_ID")
    private Long cStoreattrib41Id;
    @TableField("IS_KG")
    private String isKg;
    @TableField("IS_YW")
    private String isYw;
    @TableField("BEGIN_NEWZX")
    private Integer beginNewzx;
    @TableField("END_NEWZX")
    private Integer endNewzx;
    @TableField("REMARK1")
    private String remark1;
    @TableField("REMARK2")
    private String remark2;
    @TableField("REMARK3")
    private String remark3;
    @TableField("REMARK4")
    private String remark4;
    @TableField("REMARK5")
    private String remark5;
    @TableField("REMARK6")
    private String remark6;
    @TableField("IS_KCSHOP")
    private String isKcshop;
    @TableField("KCSHOP_CODE")
    private String kcshopCode;
    @TableField("BIGGEST_DISCOUNT")
    private BigDecimal biggestDiscount;


    public Long getId() {
        return id;
    }

    public CStore setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getAdClientId() {
        return adClientId;
    }

    public CStore setAdClientId(Long adClientId) {
        this.adClientId = adClientId;
        return this;
    }

    public Long getAdOrgId() {
        return adOrgId;
    }

    public CStore setAdOrgId(Long adOrgId) {
        this.adOrgId = adOrgId;
        return this;
    }

    public String getIsactive() {
        return isactive;
    }

    public CStore setIsactive(String isactive) {
        this.isactive = isactive;
        return this;
    }

    public Long getModifierid() {
        return modifierid;
    }

    public CStore setModifierid(Long modifierid) {
        this.modifierid = modifierid;
        return this;
    }

    public Date getCreationdate() {
        return creationdate;
    }

    public CStore setCreationdate(Date creationdate) {
        this.creationdate = creationdate;
        return this;
    }

    public Date getModifieddate() {
        return modifieddate;
    }

    public CStore setModifieddate(Date modifieddate) {
        this.modifieddate = modifieddate;
        return this;
    }

    public Long getOwnerid() {
        return ownerid;
    }

    public CStore setOwnerid(Long ownerid) {
        this.ownerid = ownerid;
        return this;
    }

    public String getName() {
        return name;
    }

    public CStore setName(String name) {
        this.name = name;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public CStore setDescription(String description) {
        this.description = description;
        return this;
    }

    public Long getcAreaId() {
        return cAreaId;
    }

    public CStore setcAreaId(Long cAreaId) {
        this.cAreaId = cAreaId;
        return this;
    }

    public BigDecimal getLockcash() {
        return lockcash;
    }

    public CStore setLockcash(BigDecimal lockcash) {
        this.lockcash = lockcash;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public CStore setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getPhone() {
        return phone;
    }

    public CStore setPhone(String phone) {
        this.phone = phone;
        return this;
    }

    public String getFax() {
        return fax;
    }

    public CStore setFax(String fax) {
        this.fax = fax;
        return this;
    }

    public Long getContactorId() {
        return contactorId;
    }

    public CStore setContactorId(Long contactorId) {
        this.contactorId = contactorId;
        return this;
    }

    public BigDecimal getMonthfee() {
        return monthfee;
    }

    public CStore setMonthfee(BigDecimal monthfee) {
        this.monthfee = monthfee;
        return this;
    }

    public String getIsstop() {
        return isstop;
    }

    public CStore setIsstop(String isstop) {
        this.isstop = isstop;
        return this;
    }

    public String getRentbegin() {
        return rentbegin;
    }

    public CStore setRentbegin(String rentbegin) {
        this.rentbegin = rentbegin;
        return this;
    }

    public String getRentend() {
        return rentend;
    }

    public CStore setRentend(String rentend) {
        this.rentend = rentend;
        return this;
    }

    public String getProportion() {
        return proportion;
    }

    public CStore setProportion(String proportion) {
        this.proportion = proportion;
        return this;
    }

    public String getEmpcnt() {
        return empcnt;
    }

    public CStore setEmpcnt(String empcnt) {
        this.empcnt = empcnt;
        return this;
    }

    public Integer getCheckdate() {
        return checkdate;
    }

    public CStore setCheckdate(Integer checkdate) {
        this.checkdate = checkdate;
        return this;
    }

    public String getIscenter() {
        return iscenter;
    }

    public CStore setIscenter(String iscenter) {
        this.iscenter = iscenter;
        return this;
    }

    public String getIsretail() {
        return isretail;
    }

    public CStore setIsretail(String isretail) {
        this.isretail = isretail;
        return this;
    }

    public String getMobil() {
        return mobil;
    }

    public CStore setMobil(String mobil) {
        this.mobil = mobil;
        return this;
    }

    public String getSname() {
        return sname;
    }

    public CStore setSname(String sname) {
        this.sname = sname;
        return this;
    }

    public BigDecimal getPostcal() {
        return postcal;
    }

    public CStore setPostcal(BigDecimal postcal) {
        this.postcal = postcal;
        return this;
    }

    public String getCalculation() {
        return calculation;
    }

    public CStore setCalculation(String calculation) {
        this.calculation = calculation;
        return this;
    }

    public Long getcCustomerId() {
        return cCustomerId;
    }

    public CStore setcCustomerId(Long cCustomerId) {
        this.cCustomerId = cCustomerId;
        return this;
    }

    public Long getcCustomerupId() {
        return cCustomerupId;
    }

    public CStore setcCustomerupId(Long cCustomerupId) {
        this.cCustomerupId = cCustomerupId;
        return this;
    }

    public Long getcPriceareaId() {
        return cPriceareaId;
    }

    public CStore setcPriceareaId(Long cPriceareaId) {
        this.cPriceareaId = cPriceareaId;
        return this;
    }

    public String getIsfairorig() {
        return isfairorig;
    }

    public CStore setIsfairorig(String isfairorig) {
        this.isfairorig = isfairorig;
        return this;
    }

    public Long getAreamngId() {
        return areamngId;
    }

    public CStore setAreamngId(Long areamngId) {
        this.areamngId = areamngId;
        return this;
    }

    public Long getLimitqty() {
        return limitqty;
    }

    public CStore setLimitqty(Long limitqty) {
        this.limitqty = limitqty;
        return this;
    }

    public BigDecimal getLimitamt() {
        return limitamt;
    }

    public CStore setLimitamt(BigDecimal limitamt) {
        this.limitamt = limitamt;
        return this;
    }

    public String getLimitmo() {
        return limitmo;
    }

    public CStore setLimitmo(String limitmo) {
        this.limitmo = limitmo;
        return this;
    }

    public BigDecimal getMarkdis() {
        return markdis;
    }

    public CStore setMarkdis(BigDecimal markdis) {
        this.markdis = markdis;
        return this;
    }

    public Integer getDateblock() {
        return dateblock;
    }

    public CStore setDateblock(Integer dateblock) {
        this.dateblock = dateblock;
        return this;
    }

    public Long getcStoretypeJzId() {
        return cStoretypeJzId;
    }

    public CStore setcStoretypeJzId(Long cStoretypeJzId) {
        this.cStoretypeJzId = cStoretypeJzId;
        return this;
    }

    public String getImgurl1() {
        return imgurl1;
    }

    public CStore setImgurl1(String imgurl1) {
        this.imgurl1 = imgurl1;
        return this;
    }

    public String getImgurl2() {
        return imgurl2;
    }

    public CStore setImgurl2(String imgurl2) {
        this.imgurl2 = imgurl2;
        return this;
    }

    public String getImgurl3() {
        return imgurl3;
    }

    public CStore setImgurl3(String imgurl3) {
        this.imgurl3 = imgurl3;
        return this;
    }

    public String getImgurl4() {
        return imgurl4;
    }

    public CStore setImgurl4(String imgurl4) {
        this.imgurl4 = imgurl4;
        return this;
    }

    public String getImgurl5() {
        return imgurl5;
    }

    public CStore setImgurl5(String imgurl5) {
        this.imgurl5 = imgurl5;
        return this;
    }

    public Long getBigareamngId() {
        return bigareamngId;
    }

    public CStore setBigareamngId(Long bigareamngId) {
        this.bigareamngId = bigareamngId;
        return this;
    }

    public Long getcProvinceId() {
        return cProvinceId;
    }

    public CStore setcProvinceId(Long cProvinceId) {
        this.cProvinceId = cProvinceId;
        return this;
    }

    public Long getcCityId() {
        return cCityId;
    }

    public CStore setcCityId(Long cCityId) {
        this.cCityId = cCityId;
        return this;
    }

    public BigDecimal getStoresign() {
        return storesign;
    }

    public CStore setStoresign(BigDecimal storesign) {
        this.storesign = storesign;
        return this;
    }

    public String getcStoretype() {
        return cStoretype;
    }

    public CStore setcStoretype(String cStoretype) {
        this.cStoretype = cStoretype;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public CStore setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public String getCode() {
        return code;
    }

    public CStore setCode(String code) {
        this.code = code;
        return this;
    }

    public String getIsufstore() {
        return isufstore;
    }

    public CStore setIsufstore(String isufstore) {
        this.isufstore = isufstore;
        return this;
    }

    public Long getcStoreId() {
        return cStoreId;
    }

    public CStore setcStoreId(Long cStoreId) {
        this.cStoreId = cStoreId;
        return this;
    }

    public Long getcDepartmentId() {
        return cDepartmentId;
    }

    public CStore setcDepartmentId(Long cDepartmentId) {
        this.cDepartmentId = cDepartmentId;
        return this;
    }

    public Long getcClasscodeId() {
        return cClasscodeId;
    }

    public CStore setcClasscodeId(Long cClasscodeId) {
        this.cClasscodeId = cClasscodeId;
        return this;
    }

    public String getUfCode() {
        return ufCode;
    }

    public CStore setUfCode(String ufCode) {
        this.ufCode = ufCode;
        return this;
    }

    public Integer getBilldateFrist() {
        return billdateFrist;
    }

    public CStore setBilldateFrist(Integer billdateFrist) {
        this.billdateFrist = billdateFrist;
        return this;
    }

    public Long getPriority() {
        return priority;
    }

    public CStore setPriority(Long priority) {
        this.priority = priority;
        return this;
    }

    public Long getcBlockId() {
        return cBlockId;
    }

    public CStore setcBlockId(Long cBlockId) {
        this.cBlockId = cBlockId;
        return this;
    }

    public String getIsfictitious() {
        return isfictitious;
    }

    public CStore setIsfictitious(String isfictitious) {
        this.isfictitious = isfictitious;
        return this;
    }

    public Integer getShopReceiveType() {
        return shopReceiveType;
    }

    public CStore setShopReceiveType(Integer shopReceiveType) {
        this.shopReceiveType = shopReceiveType;
        return this;
    }

    public String getPospw() {
        return pospw;
    }

    public CStore setPospw(String pospw) {
        this.pospw = pospw;
        return this;
    }

    public String getIsblock() {
        return isblock;
    }

    public CStore setIsblock(String isblock) {
        this.isblock = isblock;
        return this;
    }

    public String getClopstoretype() {
        return clopstoretype;
    }

    public CStore setClopstoretype(String clopstoretype) {
        this.clopstoretype = clopstoretype;
        return this;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public CStore setDiscount(BigDecimal discount) {
        this.discount = discount;
        return this;
    }

    public String getClopStore() {
        return clopStore;
    }

    public CStore setClopStore(String clopStore) {
        this.clopStore = clopStore;
        return this;
    }

    public String getIsgift() {
        return isgift;
    }

    public CStore setIsgift(String isgift) {
        this.isgift = isgift;
        return this;
    }

    public String getIsdiscom() {
        return isdiscom;
    }

    public CStore setIsdiscom(String isdiscom) {
        this.isdiscom = isdiscom;
        return this;
    }

    public Long getcDepartId() {
        return cDepartId;
    }

    public CStore setcDepartId(Long cDepartId) {
        this.cDepartId = cDepartId;
        return this;
    }

    public Long getImpMonth() {
        return impMonth;
    }

    public CStore setImpMonth(Long impMonth) {
        this.impMonth = impMonth;
        return this;
    }

    public Long getImpType1() {
        return impType1;
    }

    public CStore setImpType1(Long impType1) {
        this.impType1 = impType1;
        return this;
    }

    public Long getImpType2() {
        return impType2;
    }

    public CStore setImpType2(Long impType2) {
        this.impType2 = impType2;
        return this;
    }

    public Long getImpType3() {
        return impType3;
    }

    public CStore setImpType3(Long impType3) {
        this.impType3 = impType3;
        return this;
    }

    public BigDecimal getTaxrate() {
        return taxrate;
    }

    public CStore setTaxrate(BigDecimal taxrate) {
        this.taxrate = taxrate;
        return this;
    }

    public Long getcStoreattrib1Id() {
        return cStoreattrib1Id;
    }

    public CStore setcStoreattrib1Id(Long cStoreattrib1Id) {
        this.cStoreattrib1Id = cStoreattrib1Id;
        return this;
    }

    public Long getcStoreattrib2Id() {
        return cStoreattrib2Id;
    }

    public CStore setcStoreattrib2Id(Long cStoreattrib2Id) {
        this.cStoreattrib2Id = cStoreattrib2Id;
        return this;
    }

    public Long getcStoreattrib3Id() {
        return cStoreattrib3Id;
    }

    public CStore setcStoreattrib3Id(Long cStoreattrib3Id) {
        this.cStoreattrib3Id = cStoreattrib3Id;
        return this;
    }

    public Long getcStoreattrib4Id() {
        return cStoreattrib4Id;
    }

    public CStore setcStoreattrib4Id(Long cStoreattrib4Id) {
        this.cStoreattrib4Id = cStoreattrib4Id;
        return this;
    }

    public Long getcStoreattrib5Id() {
        return cStoreattrib5Id;
    }

    public CStore setcStoreattrib5Id(Long cStoreattrib5Id) {
        this.cStoreattrib5Id = cStoreattrib5Id;
        return this;
    }

    public Long getcStoreattrib6Id() {
        return cStoreattrib6Id;
    }

    public CStore setcStoreattrib6Id(Long cStoreattrib6Id) {
        this.cStoreattrib6Id = cStoreattrib6Id;
        return this;
    }

    public Long getcStoreattrib7Id() {
        return cStoreattrib7Id;
    }

    public CStore setcStoreattrib7Id(Long cStoreattrib7Id) {
        this.cStoreattrib7Id = cStoreattrib7Id;
        return this;
    }

    public Long getcStoreattrib8Id() {
        return cStoreattrib8Id;
    }

    public CStore setcStoreattrib8Id(Long cStoreattrib8Id) {
        this.cStoreattrib8Id = cStoreattrib8Id;
        return this;
    }

    public Long getcStoreattrib9Id() {
        return cStoreattrib9Id;
    }

    public CStore setcStoreattrib9Id(Long cStoreattrib9Id) {
        this.cStoreattrib9Id = cStoreattrib9Id;
        return this;
    }

    public Long getcStoreattrib10Id() {
        return cStoreattrib10Id;
    }

    public CStore setcStoreattrib10Id(Long cStoreattrib10Id) {
        this.cStoreattrib10Id = cStoreattrib10Id;
        return this;
    }

    public Long getcStoreattrib11Id() {
        return cStoreattrib11Id;
    }

    public CStore setcStoreattrib11Id(Long cStoreattrib11Id) {
        this.cStoreattrib11Id = cStoreattrib11Id;
        return this;
    }

    public Long getcStoreattrib12Id() {
        return cStoreattrib12Id;
    }

    public CStore setcStoreattrib12Id(Long cStoreattrib12Id) {
        this.cStoreattrib12Id = cStoreattrib12Id;
        return this;
    }

    public Long getcStoreattrib13Id() {
        return cStoreattrib13Id;
    }

    public CStore setcStoreattrib13Id(Long cStoreattrib13Id) {
        this.cStoreattrib13Id = cStoreattrib13Id;
        return this;
    }

    public Long getcStoreattrib14Id() {
        return cStoreattrib14Id;
    }

    public CStore setcStoreattrib14Id(Long cStoreattrib14Id) {
        this.cStoreattrib14Id = cStoreattrib14Id;
        return this;
    }

    public Long getcStoreattrib15Id() {
        return cStoreattrib15Id;
    }

    public CStore setcStoreattrib15Id(Long cStoreattrib15Id) {
        this.cStoreattrib15Id = cStoreattrib15Id;
        return this;
    }

    public Long getcStoreattrib16Id() {
        return cStoreattrib16Id;
    }

    public CStore setcStoreattrib16Id(Long cStoreattrib16Id) {
        this.cStoreattrib16Id = cStoreattrib16Id;
        return this;
    }

    public Long getcStoreattrib17Id() {
        return cStoreattrib17Id;
    }

    public CStore setcStoreattrib17Id(Long cStoreattrib17Id) {
        this.cStoreattrib17Id = cStoreattrib17Id;
        return this;
    }

    public Long getcStoreattrib18Id() {
        return cStoreattrib18Id;
    }

    public CStore setcStoreattrib18Id(Long cStoreattrib18Id) {
        this.cStoreattrib18Id = cStoreattrib18Id;
        return this;
    }

    public Long getcStoreattrib19Id() {
        return cStoreattrib19Id;
    }

    public CStore setcStoreattrib19Id(Long cStoreattrib19Id) {
        this.cStoreattrib19Id = cStoreattrib19Id;
        return this;
    }

    public Long getcStoreattrib20Id() {
        return cStoreattrib20Id;
    }

    public CStore setcStoreattrib20Id(Long cStoreattrib20Id) {
        this.cStoreattrib20Id = cStoreattrib20Id;
        return this;
    }

    public String getIsnegative() {
        return isnegative;
    }

    public CStore setIsnegative(String isnegative) {
        this.isnegative = isnegative;
        return this;
    }

    public Long getTdefdowntypeId() {
        return tdefdowntypeId;
    }

    public CStore setTdefdowntypeId(Long tdefdowntypeId) {
        this.tdefdowntypeId = tdefdowntypeId;
        return this;
    }

    public Long getcQtyaddareaId() {
        return cQtyaddareaId;
    }

    public CStore setcQtyaddareaId(Long cQtyaddareaId) {
        this.cQtyaddareaId = cQtyaddareaId;
        return this;
    }

    public Long getcCorpId() {
        return cCorpId;
    }

    public CStore setcCorpId(Long cCorpId) {
        this.cCorpId = cCorpId;
        return this;
    }

    public String getUsbkey() {
        return usbkey;
    }

    public CStore setUsbkey(String usbkey) {
        this.usbkey = usbkey;
        return this;
    }

    public String getIfebstore() {
        return ifebstore;
    }

    public CStore setIfebstore(String ifebstore) {
        this.ifebstore = ifebstore;
        return this;
    }

    public Integer getDateEndaccount() {
        return dateEndaccount;
    }

    public CStore setDateEndaccount(Integer dateEndaccount) {
        this.dateEndaccount = dateEndaccount;
        return this;
    }

    public String getIsstock() {
        return isstock;
    }

    public CStore setIsstock(String isstock) {
        this.isstock = isstock;
        return this;
    }

    public String getyStore() {
        return yStore;
    }

    public CStore setyStore(String yStore) {
        this.yStore = yStore;
        return this;
    }

    public String getcViptypeId1() {
        return cViptypeId1;
    }

    public CStore setcViptypeId1(String cViptypeId1) {
        this.cViptypeId1 = cViptypeId1;
        return this;
    }

    public String getIfWms() {
        return ifWms;
    }

    public CStore setIfWms(String ifWms) {
        this.ifWms = ifWms;
        return this;
    }

    public String getIforderstore() {
        return iforderstore;
    }

    public CStore setIforderstore(String iforderstore) {
        this.iforderstore = iforderstore;
        return this;
    }

    public Long getOrderlimitdate() {
        return orderlimitdate;
    }

    public CStore setOrderlimitdate(Long orderlimitdate) {
        this.orderlimitdate = orderlimitdate;
        return this;
    }

    public String getWebposloginurl() {
        return webposloginurl;
    }

    public CStore setWebposloginurl(String webposloginurl) {
        this.webposloginurl = webposloginurl;
        return this;
    }

    public Long getComptype() {
        return comptype;
    }

    public CStore setComptype(Long comptype) {
        this.comptype = comptype;
        return this;
    }

    public String getcStoreSql() {
        return cStoreSql;
    }

    public CStore setcStoreSql(String cStoreSql) {
        this.cStoreSql = cStoreSql;
        return this;
    }

    public Long getmDim1Id() {
        return mDim1Id;
    }

    public CStore setmDim1Id(Long mDim1Id) {
        this.mDim1Id = mDim1Id;
        return this;
    }

    public String getStoretype() {
        return storetype;
    }

    public CStore setStoretype(String storetype) {
        this.storetype = storetype;
        return this;
    }

    public String getRetchkorg() {
        return retchkorg;
    }

    public CStore setRetchkorg(String retchkorg) {
        this.retchkorg = retchkorg;
        return this;
    }

    public String getMarket() {
        return market;
    }

    public CStore setMarket(String market) {
        this.market = market;
        return this;
    }

    public Long getcStoregradeId() {
        return cStoregradeId;
    }

    public CStore setcStoregradeId(Long cStoregradeId) {
        this.cStoregradeId = cStoregradeId;
        return this;
    }

    public Long getcStorekindId() {
        return cStorekindId;
    }

    public CStore setcStorekindId(Long cStorekindId) {
        this.cStorekindId = cStorekindId;
        return this;
    }

    public Long getcIntegralareaId() {
        return cIntegralareaId;
    }

    public CStore setcIntegralareaId(Long cIntegralareaId) {
        this.cIntegralareaId = cIntegralareaId;
        return this;
    }

    public Long getFramworkAreaId() {
        return framworkAreaId;
    }

    public CStore setFramworkAreaId(Long framworkAreaId) {
        this.framworkAreaId = framworkAreaId;
        return this;
    }

    public Long getcArcbrandId() {
        return cArcbrandId;
    }

    public CStore setcArcbrandId(Long cArcbrandId) {
        this.cArcbrandId = cArcbrandId;
        return this;
    }

    public String getIsRestore() {
        return isRestore;
    }

    public CStore setIsRestore(String isRestore) {
        this.isRestore = isRestore;
        return this;
    }

    public String getIsMark() {
        return isMark;
    }

    public CStore setIsMark(String isMark) {
        this.isMark = isMark;
        return this;
    }

    public String getIsRet() {
        return isRet;
    }

    public CStore setIsRet(String isRet) {
        this.isRet = isRet;
        return this;
    }

    public String getWebposOffline() {
        return webposOffline;
    }

    public CStore setWebposOffline(String webposOffline) {
        this.webposOffline = webposOffline;
        return this;
    }

    public String getLowestDiscount() {
        return lowestDiscount;
    }

    public CStore setLowestDiscount(String lowestDiscount) {
        this.lowestDiscount = lowestDiscount;
        return this;
    }

    public Long getChkOverdays() {
        return chkOverdays;
    }

    public CStore setChkOverdays(Long chkOverdays) {
        this.chkOverdays = chkOverdays;
        return this;
    }

    public Long getcMarkbaltypeId() {
        return cMarkbaltypeId;
    }

    public CStore setcMarkbaltypeId(Long cMarkbaltypeId) {
        this.cMarkbaltypeId = cMarkbaltypeId;
        return this;
    }

    public String getIsvipintl() {
        return isvipintl;
    }

    public CStore setIsvipintl(String isvipintl) {
        this.isvipintl = isvipintl;
        return this;
    }

    public String getIsvipdis() {
        return isvipdis;
    }

    public CStore setIsvipdis(String isvipdis) {
        this.isvipdis = isvipdis;
        return this;
    }

    public String getIsonlycard() {
        return isonlycard;
    }

    public CStore setIsonlycard(String isonlycard) {
        this.isonlycard = isonlycard;
        return this;
    }

    public Long getcPaywayDefault() {
        return cPaywayDefault;
    }

    public CStore setcPaywayDefault(Long cPaywayDefault) {
        this.cPaywayDefault = cPaywayDefault;
        return this;
    }

    public Long getCreditlimit() {
        return creditlimit;
    }

    public CStore setCreditlimit(Long creditlimit) {
        this.creditlimit = creditlimit;
        return this;
    }

    public Long getEbCreditrankId() {
        return ebCreditrankId;
    }

    public CStore setEbCreditrankId(Long ebCreditrankId) {
        this.ebCreditrankId = ebCreditrankId;
        return this;
    }

    public Long getEbBonustypeId() {
        return ebBonustypeId;
    }

    public CStore setEbBonustypeId(Long ebBonustypeId) {
        this.ebBonustypeId = ebBonustypeId;
        return this;
    }

    public String getIsTaobao() {
        return isTaobao;
    }

    public CStore setIsTaobao(String isTaobao) {
        this.isTaobao = isTaobao;
        return this;
    }

    public Long getEbShiptypeId() {
        return ebShiptypeId;
    }

    public CStore setEbShiptypeId(Long ebShiptypeId) {
        this.ebShiptypeId = ebShiptypeId;
        return this;
    }

    public Long getShelfdepth() {
        return shelfdepth;
    }

    public CStore setShelfdepth(Long shelfdepth) {
        this.shelfdepth = shelfdepth;
        return this;
    }

    public String getIsMoresalesrep() {
        return isMoresalesrep;
    }

    public CStore setIsMoresalesrep(String isMoresalesrep) {
        this.isMoresalesrep = isMoresalesrep;
        return this;
    }

    public String getDim1Filter() {
        return dim1Filter;
    }

    public CStore setDim1Filter(String dim1Filter) {
        this.dim1Filter = dim1Filter;
        return this;
    }

    public Long getcPosaddrId() {
        return cPosaddrId;
    }

    public CStore setcPosaddrId(Long cPosaddrId) {
        this.cPosaddrId = cPosaddrId;
        return this;
    }

    public String getPosprint() {
        return posprint;
    }

    public CStore setPosprint(String posprint) {
        this.posprint = posprint;
        return this;
    }

    public String getIsModifypayamt() {
        return isModifypayamt;
    }

    public CStore setIsModifypayamt(String isModifypayamt) {
        this.isModifypayamt = isModifypayamt;
        return this;
    }

    public Integer getOpendate() {
        return opendate;
    }

    public CStore setOpendate(Integer opendate) {
        this.opendate = opendate;
        return this;
    }

    public String getLeaseperiod() {
        return leaseperiod;
    }

    public CStore setLeaseperiod(String leaseperiod) {
        this.leaseperiod = leaseperiod;
        return this;
    }

    public String getEndsale() {
        return endsale;
    }

    public CStore setEndsale(String endsale) {
        this.endsale = endsale;
        return this;
    }

    public String getUsemonth() {
        return usemonth;
    }

    public CStore setUsemonth(String usemonth) {
        this.usemonth = usemonth;
        return this;
    }

    public String getContract() {
        return contract;
    }

    public CStore setContract(String contract) {
        this.contract = contract;
        return this;
    }

    public Integer getEnddate() {
        return enddate;
    }

    public CStore setEnddate(Integer enddate) {
        this.enddate = enddate;
        return this;
    }

    public String getRencost() {
        return rencost;
    }

    public CStore setRencost(String rencost) {
        this.rencost = rencost;
        return this;
    }

    public Long getBearcompanyId() {
        return bearcompanyId;
    }

    public CStore setBearcompanyId(Long bearcompanyId) {
        this.bearcompanyId = bearcompanyId;
        return this;
    }

    public Long getChargeofId() {
        return chargeofId;
    }

    public CStore setChargeofId(Long chargeofId) {
        this.chargeofId = chargeofId;
        return this;
    }

    public String getAlipayKey() {
        return alipayKey;
    }

    public CStore setAlipayKey(String alipayKey) {
        this.alipayKey = alipayKey;
        return this;
    }

    public String getAlipayPartnerid() {
        return alipayPartnerid;
    }

    public CStore setAlipayPartnerid(String alipayPartnerid) {
        this.alipayPartnerid = alipayPartnerid;
        return this;
    }

    public String getAlipaySellMail() {
        return alipaySellMail;
    }

    public CStore setAlipaySellMail(String alipaySellMail) {
        this.alipaySellMail = alipaySellMail;
        return this;
    }

    public String getIsMarketno() {
        return isMarketno;
    }

    public CStore setIsMarketno(String isMarketno) {
        this.isMarketno = isMarketno;
        return this;
    }

    public String getIsManualint() {
        return isManualint;
    }

    public CStore setIsManualint(String isManualint) {
        this.isManualint = isManualint;
        return this;
    }

    public String getContactor() {
        return contactor;
    }

    public CStore setContactor(String contactor) {
        this.contactor = contactor;
        return this;
    }

    public String getBilldaterange() {
        return billdaterange;
    }

    public CStore setBilldaterange(String billdaterange) {
        this.billdaterange = billdaterange;
        return this;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public CStore setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
        return this;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public CStore setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
        return this;
    }

    public String getIsCreatedata() {
        return isCreatedata;
    }

    public CStore setIsCreatedata(String isCreatedata) {
        this.isCreatedata = isCreatedata;
        return this;
    }

    public String getIsTonc() {
        return isTonc;
    }

    public CStore setIsTonc(String isTonc) {
        this.isTonc = isTonc;
        return this;
    }

    public String getIsCompared() {
        return isCompared;
    }

    public CStore setIsCompared(String isCompared) {
        this.isCompared = isCompared;
        return this;
    }

    public Integer getPreopendate() {
        return preopendate;
    }

    public CStore setPreopendate(Integer preopendate) {
        this.preopendate = preopendate;
        return this;
    }

    public Long getcStoreattrib21Id() {
        return cStoreattrib21Id;
    }

    public CStore setcStoreattrib21Id(Long cStoreattrib21Id) {
        this.cStoreattrib21Id = cStoreattrib21Id;
        return this;
    }

    public String getIsProclose() {
        return isProclose;
    }

    public CStore setIsProclose(String isProclose) {
        this.isProclose = isProclose;
        return this;
    }

    public String getIsCheckalias() {
        return isCheckalias;
    }

    public CStore setIsCheckalias(String isCheckalias) {
        this.isCheckalias = isCheckalias;
        return this;
    }

    public Long getcConsumeareaId() {
        return cConsumeareaId;
    }

    public CStore setcConsumeareaId(Long cConsumeareaId) {
        this.cConsumeareaId = cConsumeareaId;
        return this;
    }

    public String getIsCounter() {
        return isCounter;
    }

    public CStore setIsCounter(String isCounter) {
        this.isCounter = isCounter;
        return this;
    }

    public String getIsExcstore() {
        return isExcstore;
    }

    public CStore setIsExcstore(String isExcstore) {
        this.isExcstore = isExcstore;
        return this;
    }

    public String getOrgmodifyper() {
        return orgmodifyper;
    }

    public CStore setOrgmodifyper(String orgmodifyper) {
        this.orgmodifyper = orgmodifyper;
        return this;
    }

    public Long getcDistrictId() {
        return cDistrictId;
    }

    public CStore setcDistrictId(Long cDistrictId) {
        this.cDistrictId = cDistrictId;
        return this;
    }

    public String getWechatCustomerid() {
        return wechatCustomerid;
    }

    public CStore setWechatCustomerid(String wechatCustomerid) {
        this.wechatCustomerid = wechatCustomerid;
        return this;
    }

    public String getSubMchId() {
        return subMchId;
    }

    public CStore setSubMchId(String subMchId) {
        this.subMchId = subMchId;
        return this;
    }

    public String getIsaixiu() {
        return isaixiu;
    }

    public CStore setIsaixiu(String isaixiu) {
        this.isaixiu = isaixiu;
        return this;
    }

    public String getIsSmartpay() {
        return isSmartpay;
    }

    public CStore setIsSmartpay(String isSmartpay) {
        this.isSmartpay = isSmartpay;
        return this;
    }

    public Long getHrGroupId() {
        return hrGroupId;
    }

    public CStore setHrGroupId(Long hrGroupId) {
        this.hrGroupId = hrGroupId;
        return this;
    }

    public Integer getEbStorageType() {
        return ebStorageType;
    }

    public CStore setEbStorageType(Integer ebStorageType) {
        this.ebStorageType = ebStorageType;
        return this;
    }

    public String getIsJstype() {
        return isJstype;
    }

    public CStore setIsJstype(String isJstype) {
        this.isJstype = isJstype;
        return this;
    }

    public String getIsBcloudStore() {
        return isBcloudStore;
    }

    public CStore setIsBcloudStore(String isBcloudStore) {
        this.isBcloudStore = isBcloudStore;
        return this;
    }

    public Long getRetailRetDay() {
        return retailRetDay;
    }

    public CStore setRetailRetDay(Long retailRetDay) {
        this.retailRetDay = retailRetDay;
        return this;
    }

    public String getqNvl() {
        return qNvl;
    }

    public CStore setqNvl(String qNvl) {
        this.qNvl = qNvl;
        return this;
    }

    public Long getcMallId() {
        return cMallId;
    }

    public CStore setcMallId(Long cMallId) {
        this.cMallId = cMallId;
        return this;
    }

    public String getqPassword() {
        return qPassword;
    }

    public CStore setqPassword(String qPassword) {
        this.qPassword = qPassword;
        return this;
    }

    public String getqSms() {
        return qSms;
    }

    public CStore setqSms(String qSms) {
        this.qSms = qSms;
        return this;
    }

    public String getqCode() {
        return qCode;
    }

    public CStore setqCode(String qCode) {
        this.qCode = qCode;
        return this;
    }

    public Long getcBigareaId() {
        return cBigareaId;
    }

    public CStore setcBigareaId(Long cBigareaId) {
        this.cBigareaId = cBigareaId;
        return this;
    }

    public String getIsBankbuy() {
        return isBankbuy;
    }

    public CStore setIsBankbuy(String isBankbuy) {
        this.isBankbuy = isBankbuy;
        return this;
    }

    public String getMainmediaaddress() {
        return mainmediaaddress;
    }

    public CStore setMainmediaaddress(String mainmediaaddress) {
        this.mainmediaaddress = mainmediaaddress;
        return this;
    }

    public Long getChange() {
        return change;
    }

    public CStore setChange(Long change) {
        this.change = change;
        return this;
    }

    public String getCurrency() {
        return currency;
    }

    public CStore setCurrency(String currency) {
        this.currency = currency;
        return this;
    }

    public String getCurrencySign() {
        return currencySign;
    }

    public CStore setCurrencySign(String currencySign) {
        this.currencySign = currencySign;
        return this;
    }

    public String getIsretpay() {
        return isretpay;
    }

    public CStore setIsretpay(String isretpay) {
        this.isretpay = isretpay;
        return this;
    }

    public String getSfCardno() {
        return sfCardno;
    }

    public CStore setSfCardno(String sfCardno) {
        this.sfCardno = sfCardno;
        return this;
    }

    public String getcPaywayFilter() {
        return cPaywayFilter;
    }

    public CStore setcPaywayFilter(String cPaywayFilter) {
        this.cPaywayFilter = cPaywayFilter;
        return this;
    }

    public String getIsAllpayway() {
        return isAllpayway;
    }

    public CStore setIsAllpayway(String isAllpayway) {
        this.isAllpayway = isAllpayway;
        return this;
    }

    public String getIsDisstore() {
        return isDisstore;
    }

    public CStore setIsDisstore(String isDisstore) {
        this.isDisstore = isDisstore;
        return this;
    }

    public String getIsUnionstore() {
        return isUnionstore;
    }

    public CStore setIsUnionstore(String isUnionstore) {
        this.isUnionstore = isUnionstore;
        return this;
    }

    public String getRetailLocation() {
        return retailLocation;
    }

    public CStore setRetailLocation(String retailLocation) {
        this.retailLocation = retailLocation;
        return this;
    }

    public String getIsMorecurrency() {
        return isMorecurrency;
    }

    public CStore setIsMorecurrency(String isMorecurrency) {
        this.isMorecurrency = isMorecurrency;
        return this;
    }

    public Long getcCurrencyId() {
        return cCurrencyId;
    }

    public CStore setcCurrencyId(Long cCurrencyId) {
        this.cCurrencyId = cCurrencyId;
        return this;
    }

    public String getPosAutoDis() {
        return posAutoDis;
    }

    public CStore setPosAutoDis(String posAutoDis) {
        this.posAutoDis = posAutoDis;
        return this;
    }

    public String getSubsystemName() {
        return subsystemName;
    }

    public CStore setSubsystemName(String subsystemName) {
        this.subsystemName = subsystemName;
        return this;
    }

    public String getMenuList() {
        return menuList;
    }

    public CStore setMenuList(String menuList) {
        this.menuList = menuList;
        return this;
    }

    public String getIsretLevelOn() {
        return isretLevelOn;
    }

    public CStore setIsretLevelOn(String isretLevelOn) {
        this.isretLevelOn = isretLevelOn;
        return this;
    }

    public String getIspadpos() {
        return ispadpos;
    }

    public CStore setIspadpos(String ispadpos) {
        this.ispadpos = ispadpos;
        return this;
    }

    public String getIskeepPwd() {
        return iskeepPwd;
    }

    public CStore setIskeepPwd(String iskeepPwd) {
        this.iskeepPwd = iskeepPwd;
        return this;
    }

    public Integer getDateHourOffset() {
        return dateHourOffset;
    }

    public CStore setDateHourOffset(Integer dateHourOffset) {
        this.dateHourOffset = dateHourOffset;
        return this;
    }

    public String getPosSerialno() {
        return posSerialno;
    }

    public CStore setPosSerialno(String posSerialno) {
        this.posSerialno = posSerialno;
        return this;
    }

    public String getIsHpmart() {
        return isHpmart;
    }

    public CStore setIsHpmart(String isHpmart) {
        this.isHpmart = isHpmart;
        return this;
    }

    public String getShopid() {
        return shopid;
    }

    public CStore setShopid(String shopid) {
        this.shopid = shopid;
        return this;
    }

    public String getShopcode() {
        return shopcode;
    }

    public CStore setShopcode(String shopcode) {
        this.shopcode = shopcode;
        return this;
    }

    public String getProductcode() {
        return productcode;
    }

    public CStore setProductcode(String productcode) {
        this.productcode = productcode;
        return this;
    }

    public String getqCodePos() {
        return qCodePos;
    }

    public CStore setqCodePos(String qCodePos) {
        this.qCodePos = qCodePos;
        return this;
    }

    public String getGuestmachinecom() {
        return guestmachinecom;
    }

    public CStore setGuestmachinecom(String guestmachinecom) {
        this.guestmachinecom = guestmachinecom;
        return this;
    }

    public Integer getGuestmachine() {
        return guestmachine;
    }

    public CStore setGuestmachine(Integer guestmachine) {
        this.guestmachine = guestmachine;
        return this;
    }

    public String getIsmustentervip() {
        return ismustentervip;
    }

    public CStore setIsmustentervip(String ismustentervip) {
        this.ismustentervip = ismustentervip;
        return this;
    }

    public Integer getChkday() {
        return chkday;
    }

    public CStore setChkday(Integer chkday) {
        this.chkday = chkday;
        return this;
    }

    public String getMallcode() {
        return mallcode;
    }

    public CStore setMallcode(String mallcode) {
        this.mallcode = mallcode;
        return this;
    }

    public String getCounternum() {
        return counternum;
    }

    public CStore setCounternum(String counternum) {
        this.counternum = counternum;
        return this;
    }

    public Long getEbExpressId() {
        return ebExpressId;
    }

    public CStore setEbExpressId(Long ebExpressId) {
        this.ebExpressId = ebExpressId;
        return this;
    }

    public String getSdtstore() {
        return sdtstore;
    }

    public CStore setSdtstore(String sdtstore) {
        this.sdtstore = sdtstore;
        return this;
    }

    public String getIsBpayshowvoucher() {
        return isBpayshowvoucher;
    }

    public CStore setIsBpayshowvoucher(String isBpayshowvoucher) {
        this.isBpayshowvoucher = isBpayshowvoucher;
        return this;
    }

    public String getWechatCustomeridnew() {
        return wechatCustomeridnew;
    }

    public CStore setWechatCustomeridnew(String wechatCustomeridnew) {
        this.wechatCustomeridnew = wechatCustomeridnew;
        return this;
    }

    public Long getcCountryId() {
        return cCountryId;
    }

    public CStore setcCountryId(Long cCountryId) {
        this.cCountryId = cCountryId;
        return this;
    }

    public String getIsO2o() {
        return isO2o;
    }

    public CStore setIsO2o(String isO2o) {
        this.isO2o = isO2o;
        return this;
    }

    public String getAllowReceipt() {
        return allowReceipt;
    }

    public CStore setAllowReceipt(String allowReceipt) {
        this.allowReceipt = allowReceipt;
        return this;
    }

    public String getIsModifyamtReason() {
        return isModifyamtReason;
    }

    public CStore setIsModifyamtReason(String isModifyamtReason) {
        this.isModifyamtReason = isModifyamtReason;
        return this;
    }

    public String getBposex() {
        return bposex;
    }

    public CStore setBposex(String bposex) {
        this.bposex = bposex;
        return this;
    }

    public String getIssdt() {
        return issdt;
    }

    public CStore setIssdt(String issdt) {
        this.issdt = issdt;
        return this;
    }

    public String getDxlx() {
        return dxlx;
    }

    public CStore setDxlx(String dxlx) {
        this.dxlx = dxlx;
        return this;
    }

    public Long getcUnionstoreId() {
        return cUnionstoreId;
    }

    public CStore setcUnionstoreId(Long cUnionstoreId) {
        this.cUnionstoreId = cUnionstoreId;
        return this;
    }

    public String getIsAutoin() {
        return isAutoin;
    }

    public CStore setIsAutoin(String isAutoin) {
        this.isAutoin = isAutoin;
        return this;
    }

    public Long getDefaultChargepayway() {
        return defaultChargepayway;
    }

    public CStore setDefaultChargepayway(Long defaultChargepayway) {
        this.defaultChargepayway = defaultChargepayway;
        return this;
    }

    public String getDefaultlastdate() {
        return defaultlastdate;
    }

    public CStore setDefaultlastdate(String defaultlastdate) {
        this.defaultlastdate = defaultlastdate;
        return this;
    }

    public String getInvoiceTemplate() {
        return invoiceTemplate;
    }

    public CStore setInvoiceTemplate(String invoiceTemplate) {
        this.invoiceTemplate = invoiceTemplate;
        return this;
    }

    public String getIsShowvisitplan() {
        return isShowvisitplan;
    }

    public CStore setIsShowvisitplan(String isShowvisitplan) {
        this.isShowvisitplan = isShowvisitplan;
        return this;
    }

    public String getInvoiceTaxno() {
        return invoiceTaxno;
    }

    public CStore setInvoiceTaxno(String invoiceTaxno) {
        this.invoiceTaxno = invoiceTaxno;
        return this;
    }

    public Integer getCheckdatadown() {
        return checkdatadown;
    }

    public CStore setCheckdatadown(Integer checkdatadown) {
        this.checkdatadown = checkdatadown;
        return this;
    }

    public Long getPorderlimitdate() {
        return porderlimitdate;
    }

    public CStore setPorderlimitdate(Long porderlimitdate) {
        this.porderlimitdate = porderlimitdate;
        return this;
    }

    public String getIsCheckskuonline() {
        return isCheckskuonline;
    }

    public CStore setIsCheckskuonline(String isCheckskuonline) {
        this.isCheckskuonline = isCheckskuonline;
        return this;
    }

    public String getCombineafter() {
        return combineafter;
    }

    public CStore setCombineafter(String combineafter) {
        this.combineafter = combineafter;
        return this;
    }

    public String getIsRefreshnetwork() {
        return isRefreshnetwork;
    }

    public CStore setIsRefreshnetwork(String isRefreshnetwork) {
        this.isRefreshnetwork = isRefreshnetwork;
        return this;
    }

    public String getIsForcebpos() {
        return isForcebpos;
    }

    public CStore setIsForcebpos(String isForcebpos) {
        this.isForcebpos = isForcebpos;
        return this;
    }

    public String getDescription1() {
        return description1;
    }

    public CStore setDescription1(String description1) {
        this.description1 = description1;
        return this;
    }

    public String getIsOxo() {
        return isOxo;
    }

    public CStore setIsOxo(String isOxo) {
        this.isOxo = isOxo;
        return this;
    }

    public String getJitStorecode() {
        return jitStorecode;
    }

    public CStore setJitStorecode(String jitStorecode) {
        this.jitStorecode = jitStorecode;
        return this;
    }

    public Long getcDistrYxjId() {
        return cDistrYxjId;
    }

    public CStore setcDistrYxjId(Long cDistrYxjId) {
        this.cDistrYxjId = cDistrYxjId;
        return this;
    }

    public String getIsFilterviptype() {
        return isFilterviptype;
    }

    public CStore setIsFilterviptype(String isFilterviptype) {
        this.isFilterviptype = isFilterviptype;
        return this;
    }

    public String getIsVipCheck() {
        return isVipCheck;
    }

    public CStore setIsVipCheck(String isVipCheck) {
        this.isVipCheck = isVipCheck;
        return this;
    }

    public String getIsBrithdaydis() {
        return isBrithdaydis;
    }

    public CStore setIsBrithdaydis(String isBrithdaydis) {
        this.isBrithdaydis = isBrithdaydis;
        return this;
    }

    public String getBrowserOpenMode() {
        return browserOpenMode;
    }

    public CStore setBrowserOpenMode(String browserOpenMode) {
        this.browserOpenMode = browserOpenMode;
        return this;
    }

    public String getIsIntegralBase() {
        return isIntegralBase;
    }

    public CStore setIsIntegralBase(String isIntegralBase) {
        this.isIntegralBase = isIntegralBase;
        return this;
    }

    public String getIsOrder() {
        return isOrder;
    }

    public CStore setIsOrder(String isOrder) {
        this.isOrder = isOrder;
        return this;
    }

    public String getCanGrab() {
        return canGrab;
    }

    public CStore setCanGrab(String canGrab) {
        this.canGrab = canGrab;
        return this;
    }

    public String getIsPerformanceShare() {
        return isPerformanceShare;
    }

    public CStore setIsPerformanceShare(String isPerformanceShare) {
        this.isPerformanceShare = isPerformanceShare;
        return this;
    }

    public String getMorlthenote() {
        return morlthenote;
    }

    public CStore setMorlthenote(String morlthenote) {
        this.morlthenote = morlthenote;
        return this;
    }

    public String getIsmorlthenotep() {
        return ismorlthenotep;
    }

    public CStore setIsmorlthenotep(String ismorlthenotep) {
        this.ismorlthenotep = ismorlthenotep;
        return this;
    }

    public String getIsControl() {
        return isControl;
    }

    public CStore setIsControl(String isControl) {
        this.isControl = isControl;
        return this;
    }

    public String getIsPrintElectronicinvoice() {
        return isPrintElectronicinvoice;
    }

    public CStore setIsPrintElectronicinvoice(String isPrintElectronicinvoice) {
        this.isPrintElectronicinvoice = isPrintElectronicinvoice;
        return this;
    }

    public BigDecimal getOpenvipAmt() {
        return openvipAmt;
    }

    public CStore setOpenvipAmt(BigDecimal openvipAmt) {
        this.openvipAmt = openvipAmt;
        return this;
    }

    public Long getMorderlimitdate() {
        return morderlimitdate;
    }

    public CStore setMorderlimitdate(Long morderlimitdate) {
        this.morderlimitdate = morderlimitdate;
        return this;
    }

    public String getPrinttpls4sglpdt() {
        return printtpls4sglpdt;
    }

    public CStore setPrinttpls4sglpdt(String printtpls4sglpdt) {
        this.printtpls4sglpdt = printtpls4sglpdt;
        return this;
    }

    public String getInvoicePhone() {
        return invoicePhone;
    }

    public CStore setInvoicePhone(String invoicePhone) {
        this.invoicePhone = invoicePhone;
        return this;
    }

    public String getInvoiceAccount() {
        return invoiceAccount;
    }

    public CStore setInvoiceAccount(String invoiceAccount) {
        this.invoiceAccount = invoiceAccount;
        return this;
    }

    public String getInvoiceBank() {
        return invoiceBank;
    }

    public CStore setInvoiceBank(String invoiceBank) {
        this.invoiceBank = invoiceBank;
        return this;
    }

    public String getInvoiceAddr() {
        return invoiceAddr;
    }

    public CStore setInvoiceAddr(String invoiceAddr) {
        this.invoiceAddr = invoiceAddr;
        return this;
    }

    public String getInvoiceCom() {
        return invoiceCom;
    }

    public CStore setInvoiceCom(String invoiceCom) {
        this.invoiceCom = invoiceCom;
        return this;
    }

    public String getMallName() {
        return mallName;
    }

    public CStore setMallName(String mallName) {
        this.mallName = mallName;
        return this;
    }

    public String getMallNo() {
        return mallNo;
    }

    public CStore setMallNo(String mallNo) {
        this.mallNo = mallNo;
        return this;
    }

    public String getMallAddress() {
        return mallAddress;
    }

    public CStore setMallAddress(String mallAddress) {
        this.mallAddress = mallAddress;
        return this;
    }

    public String getIsStono() {
        return isStono;
    }

    public CStore setIsStono(String isStono) {
        this.isStono = isStono;
        return this;
    }

    public String getIsWmsstore() {
        return isWmsstore;
    }

    public CStore setIsWmsstore(String isWmsstore) {
        this.isWmsstore = isWmsstore;
        return this;
    }

    public String getWmsStorecode() {
        return wmsStorecode;
    }

    public CStore setWmsStorecode(String wmsStorecode) {
        this.wmsStorecode = wmsStorecode;
        return this;
    }

    public String getIsTowms() {
        return isTowms;
    }

    public CStore setIsTowms(String isTowms) {
        this.isTowms = isTowms;
        return this;
    }

    public String getIsMarket() {
        return isMarket;
    }

    public CStore setIsMarket(String isMarket) {
        this.isMarket = isMarket;
        return this;
    }

    public Long getcRealstoreId() {
        return cRealstoreId;
    }

    public CStore setcRealstoreId(Long cRealstoreId) {
        this.cRealstoreId = cRealstoreId;
        return this;
    }

    public String getDescription01() {
        return description01;
    }

    public CStore setDescription01(String description01) {
        this.description01 = description01;
        return this;
    }

    public String getDescription02() {
        return description02;
    }

    public CStore setDescription02(String description02) {
        this.description02 = description02;
        return this;
    }

    public String getDescription03() {
        return description03;
    }

    public CStore setDescription03(String description03) {
        this.description03 = description03;
        return this;
    }

    public String getDescription04() {
        return description04;
    }

    public CStore setDescription04(String description04) {
        this.description04 = description04;
        return this;
    }

    public String getDescription05() {
        return description05;
    }

    public CStore setDescription05(String description05) {
        this.description05 = description05;
        return this;
    }

    public String getMerchantNumber() {
        return merchantNumber;
    }

    public CStore setMerchantNumber(String merchantNumber) {
        this.merchantNumber = merchantNumber;
        return this;
    }

    public String getPaymentKey() {
        return paymentKey;
    }

    public CStore setPaymentKey(String paymentKey) {
        this.paymentKey = paymentKey;
        return this;
    }

    public String getIsDelivery() {
        return isDelivery;
    }

    public CStore setIsDelivery(String isDelivery) {
        this.isDelivery = isDelivery;
        return this;
    }

    public String getDeskey() {
        return deskey;
    }

    public CStore setDeskey(String deskey) {
        this.deskey = deskey;
        return this;
    }

    public String getCarrymode() {
        return carrymode;
    }

    public CStore setCarrymode(String carrymode) {
        this.carrymode = carrymode;
        return this;
    }

    public String getIsSelectprint() {
        return isSelectprint;
    }

    public CStore setIsSelectprint(String isSelectprint) {
        this.isSelectprint = isSelectprint;
        return this;
    }

    public String getPrinttemplatelist() {
        return printtemplatelist;
    }

    public CStore setPrinttemplatelist(String printtemplatelist) {
        this.printtemplatelist = printtemplatelist;
        return this;
    }

    public String getIsExamination() {
        return isExamination;
    }

    public CStore setIsExamination(String isExamination) {
        this.isExamination = isExamination;
        return this;
    }

    public String getShouqianbacode() {
        return shouqianbacode;
    }

    public CStore setShouqianbacode(String shouqianbacode) {
        this.shouqianbacode = shouqianbacode;
        return this;
    }

    public String getWechatCustomeridnew2() {
        return wechatCustomeridnew2;
    }

    public CStore setWechatCustomeridnew2(String wechatCustomeridnew2) {
        this.wechatCustomeridnew2 = wechatCustomeridnew2;
        return this;
    }

    public String getShouqianbaUse() {
        return shouqianbaUse;
    }

    public CStore setShouqianbaUse(String shouqianbaUse) {
        this.shouqianbaUse = shouqianbaUse;
        return this;
    }

    public String geto2ovoice() {
        return o2ovoice;
    }

    public CStore seto2ovoice(String o2ovoice) {
        this.o2ovoice = o2ovoice;
        return this;
    }

    public String getPendvoice() {
        return pendvoice;
    }

    public CStore setPendvoice(String pendvoice) {
        this.pendvoice = pendvoice;
        return this;
    }

    public String getMerchant() {
        return merchant;
    }

    public CStore setMerchant(String merchant) {
        this.merchant = merchant;
        return this;
    }

    public String getVipprinttemplate() {
        return vipprinttemplate;
    }

    public CStore setVipprinttemplate(String vipprinttemplate) {
        this.vipprinttemplate = vipprinttemplate;
        return this;
    }

    public String getJtk() {
        return jtk;
    }

    public CStore setJtk(String jtk) {
        this.jtk = jtk;
        return this;
    }

    public String getRecharge() {
        return recharge;
    }

    public CStore setRecharge(String recharge) {
        this.recharge = recharge;
        return this;
    }

    public String getComfirmBeforepay() {
        return comfirmBeforepay;
    }

    public CStore setComfirmBeforepay(String comfirmBeforepay) {
        this.comfirmBeforepay = comfirmBeforepay;
        return this;
    }

    public String getMonitorUrl() {
        return monitorUrl;
    }

    public CStore setMonitorUrl(String monitorUrl) {
        this.monitorUrl = monitorUrl;
        return this;
    }

    public String getManagernavOpenMode() {
        return managernavOpenMode;
    }

    public CStore setManagernavOpenMode(String managernavOpenMode) {
        this.managernavOpenMode = managernavOpenMode;
        return this;
    }

    public String getVoucherStoreType() {
        return voucherStoreType;
    }

    public CStore setVoucherStoreType(String voucherStoreType) {
        this.voucherStoreType = voucherStoreType;
        return this;
    }

    public String getIsmoneny() {
        return ismoneny;
    }

    public CStore setIsmoneny(String ismoneny) {
        this.ismoneny = ismoneny;
        return this;
    }

    public String getMobilepaysolution() {
        return mobilepaysolution;
    }

    public CStore setMobilepaysolution(String mobilepaysolution) {
        this.mobilepaysolution = mobilepaysolution;
        return this;
    }

    public String getInternalPurchaseStore() {
        return internalPurchaseStore;
    }

    public CStore setInternalPurchaseStore(String internalPurchaseStore) {
        this.internalPurchaseStore = internalPurchaseStore;
        return this;
    }

    public String getPadposTemplate() {
        return padposTemplate;
    }

    public CStore setPadposTemplate(String padposTemplate) {
        this.padposTemplate = padposTemplate;
        return this;
    }

    public String getAllowCustomer() {
        return allowCustomer;
    }

    public CStore setAllowCustomer(String allowCustomer) {
        this.allowCustomer = allowCustomer;
        return this;
    }

    public String getBranchidcard() {
        return branchidcard;
    }

    public CStore setBranchidcard(String branchidcard) {
        this.branchidcard = branchidcard;
        return this;
    }

    public String getPosidcard() {
        return posidcard;
    }

    public CStore setPosidcard(String posidcard) {
        this.posidcard = posidcard;
        return this;
    }

    public String getOnlineorder() {
        return onlineorder;
    }

    public CStore setOnlineorder(String onlineorder) {
        this.onlineorder = onlineorder;
        return this;
    }

    public String getVipActivate() {
        return vipActivate;
    }

    public CStore setVipActivate(String vipActivate) {
        this.vipActivate = vipActivate;
        return this;
    }

    public String getIsWade() {
        return isWade;
    }

    public CStore setIsWade(String isWade) {
        this.isWade = isWade;
        return this;
    }

    public String getIsprintholdretail() {
        return isprintholdretail;
    }

    public CStore setIsprintholdretail(String isprintholdretail) {
        this.isprintholdretail = isprintholdretail;
        return this;
    }

    public String getMd5() {
        return md5;
    }

    public CStore setMd5(String md5) {
        this.md5 = md5;
        return this;
    }

    public String getMisposcard() {
        return misposcard;
    }

    public CStore setMisposcard(String misposcard) {
        this.misposcard = misposcard;
        return this;
    }

    public String getMisposterminal() {
        return misposterminal;
    }

    public CStore setMisposterminal(String misposterminal) {
        this.misposterminal = misposterminal;
        return this;
    }

    public String getMisposvision() {
        return misposvision;
    }

    public CStore setMisposvision(String misposvision) {
        this.misposvision = misposvision;
        return this;
    }

    public String getWechatCustomeridnew3() {
        return wechatCustomeridnew3;
    }

    public CStore setWechatCustomeridnew3(String wechatCustomeridnew3) {
        this.wechatCustomeridnew3 = wechatCustomeridnew3;
        return this;
    }

    public String getWechatCustomeridnew4() {
        return wechatCustomeridnew4;
    }

    public CStore setWechatCustomeridnew4(String wechatCustomeridnew4) {
        this.wechatCustomeridnew4 = wechatCustomeridnew4;
        return this;
    }

    public String getCcbPosbTermNo() {
        return ccbPosbTermNo;
    }

    public CStore setCcbPosbTermNo(String ccbPosbTermNo) {
        this.ccbPosbTermNo = ccbPosbTermNo;
        return this;
    }

    public String getUserId() {
        return userId;
    }

    public CStore setUserId(String userId) {
        this.userId = userId;
        return this;
    }

    public String getLandiUnionpay() {
        return landiUnionpay;
    }

    public CStore setLandiUnionpay(String landiUnionpay) {
        this.landiUnionpay = landiUnionpay;
        return this;
    }

    public String getWechatCustomeridnew5() {
        return wechatCustomeridnew5;
    }

    public CStore setWechatCustomeridnew5(String wechatCustomeridnew5) {
        this.wechatCustomeridnew5 = wechatCustomeridnew5;
        return this;
    }

    public String getPosbtocPosbTermNo() {
        return posbtocPosbTermNo;
    }

    public CStore setPosbtocPosbTermNo(String posbtocPosbTermNo) {
        this.posbtocPosbTermNo = posbtocPosbTermNo;
        return this;
    }

    public String getIsmobilepays() {
        return ismobilepays;
    }

    public CStore setIsmobilepays(String ismobilepays) {
        this.ismobilepays = ismobilepays;
        return this;
    }

    public String getConsumerCardPayType() {
        return consumerCardPayType;
    }

    public CStore setConsumerCardPayType(String consumerCardPayType) {
        this.consumerCardPayType = consumerCardPayType;
        return this;
    }

    public String getPaywebAppkey() {
        return paywebAppkey;
    }

    public CStore setPaywebAppkey(String paywebAppkey) {
        this.paywebAppkey = paywebAppkey;
        return this;
    }

    public String getManuallyenter() {
        return manuallyenter;
    }

    public CStore setManuallyenter(String manuallyenter) {
        this.manuallyenter = manuallyenter;
        return this;
    }

    public String getIsVip() {
        return isVip;
    }

    public CStore setIsVip(String isVip) {
        this.isVip = isVip;
        return this;
    }

    public Long getcStoreattrib22Id() {
        return cStoreattrib22Id;
    }

    public CStore setcStoreattrib22Id(Long cStoreattrib22Id) {
        this.cStoreattrib22Id = cStoreattrib22Id;
        return this;
    }

    public String getIsYundong() {
        return isYundong;
    }

    public CStore setIsYundong(String isYundong) {
        this.isYundong = isYundong;
        return this;
    }

    public String getIsPengma() {
        return isPengma;
    }

    public CStore setIsPengma(String isPengma) {
        this.isPengma = isPengma;
        return this;
    }

    public String getInventoryYear() {
        return inventoryYear;
    }

    public CStore setInventoryYear(String inventoryYear) {
        this.inventoryYear = inventoryYear;
        return this;
    }

    public String getWarehouseRang() {
        return warehouseRang;
    }

    public CStore setWarehouseRang(String warehouseRang) {
        this.warehouseRang = warehouseRang;
        return this;
    }

    public Integer getClosedate() {
        return closedate;
    }

    public CStore setClosedate(Integer closedate) {
        this.closedate = closedate;
        return this;
    }

    public String getIsTingye() {
        return isTingye;
    }

    public CStore setIsTingye(String isTingye) {
        this.isTingye = isTingye;
        return this;
    }

    public String getcStoreattrib24Id() {
        return cStoreattrib24Id;
    }

    public CStore setcStoreattrib24Id(String cStoreattrib24Id) {
        this.cStoreattrib24Id = cStoreattrib24Id;
        return this;
    }

    public String getcStoreattrib25Id() {
        return cStoreattrib25Id;
    }

    public CStore setcStoreattrib25Id(String cStoreattrib25Id) {
        this.cStoreattrib25Id = cStoreattrib25Id;
        return this;
    }

    public String getcStoreattrib26Id() {
        return cStoreattrib26Id;
    }

    public CStore setcStoreattrib26Id(String cStoreattrib26Id) {
        this.cStoreattrib26Id = cStoreattrib26Id;
        return this;
    }

    public String getcStoreattrib27Id() {
        return cStoreattrib27Id;
    }

    public CStore setcStoreattrib27Id(String cStoreattrib27Id) {
        this.cStoreattrib27Id = cStoreattrib27Id;
        return this;
    }

    public String getcStoreattrib28Id() {
        return cStoreattrib28Id;
    }

    public CStore setcStoreattrib28Id(String cStoreattrib28Id) {
        this.cStoreattrib28Id = cStoreattrib28Id;
        return this;
    }

    public String getcStoreattrib29Id() {
        return cStoreattrib29Id;
    }

    public CStore setcStoreattrib29Id(String cStoreattrib29Id) {
        this.cStoreattrib29Id = cStoreattrib29Id;
        return this;
    }

    public Integer getcStoreattrib30Id() {
        return cStoreattrib30Id;
    }

    public CStore setcStoreattrib30Id(Integer cStoreattrib30Id) {
        this.cStoreattrib30Id = cStoreattrib30Id;
        return this;
    }

    public String getcStoreattrib31Id() {
        return cStoreattrib31Id;
    }

    public CStore setcStoreattrib31Id(String cStoreattrib31Id) {
        this.cStoreattrib31Id = cStoreattrib31Id;
        return this;
    }

    public String getcStoreattrib32Id() {
        return cStoreattrib32Id;
    }

    public CStore setcStoreattrib32Id(String cStoreattrib32Id) {
        this.cStoreattrib32Id = cStoreattrib32Id;
        return this;
    }

    public String getcStoreattrib33Id() {
        return cStoreattrib33Id;
    }

    public CStore setcStoreattrib33Id(String cStoreattrib33Id) {
        this.cStoreattrib33Id = cStoreattrib33Id;
        return this;
    }

    public String getcStoreattrib34Id() {
        return cStoreattrib34Id;
    }

    public CStore setcStoreattrib34Id(String cStoreattrib34Id) {
        this.cStoreattrib34Id = cStoreattrib34Id;
        return this;
    }

    public String getcStoreattrib35Id() {
        return cStoreattrib35Id;
    }

    public CStore setcStoreattrib35Id(String cStoreattrib35Id) {
        this.cStoreattrib35Id = cStoreattrib35Id;
        return this;
    }

    public String getcStoreattrib36Id() {
        return cStoreattrib36Id;
    }

    public CStore setcStoreattrib36Id(String cStoreattrib36Id) {
        this.cStoreattrib36Id = cStoreattrib36Id;
        return this;
    }

    public String getcStoreattrib37Id() {
        return cStoreattrib37Id;
    }

    public CStore setcStoreattrib37Id(String cStoreattrib37Id) {
        this.cStoreattrib37Id = cStoreattrib37Id;
        return this;
    }

    public String getcStoreattrib38Id() {
        return cStoreattrib38Id;
    }

    public CStore setcStoreattrib38Id(String cStoreattrib38Id) {
        this.cStoreattrib38Id = cStoreattrib38Id;
        return this;
    }

    public String getcStoreattrib39Id() {
        return cStoreattrib39Id;
    }

    public CStore setcStoreattrib39Id(String cStoreattrib39Id) {
        this.cStoreattrib39Id = cStoreattrib39Id;
        return this;
    }

    public String getHedianPinpai() {
        return hedianPinpai;
    }

    public CStore setHedianPinpai(String hedianPinpai) {
        this.hedianPinpai = hedianPinpai;
        return this;
    }

    public String getMarketName() {
        return marketName;
    }

    public CStore setMarketName(String marketName) {
        this.marketName = marketName;
        return this;
    }

    public String getYearRent() {
        return yearRent;
    }

    public CStore setYearRent(String yearRent) {
        this.yearRent = yearRent;
        return this;
    }

    public Integer getDecOpendate() {
        return decOpendate;
    }

    public CStore setDecOpendate(Integer decOpendate) {
        this.decOpendate = decOpendate;
        return this;
    }

    public String getTaxDis() {
        return taxDis;
    }

    public CStore setTaxDis(String taxDis) {
        this.taxDis = taxDis;
        return this;
    }

    public String getYearAmtBot() {
        return yearAmtBot;
    }

    public CStore setYearAmtBot(String yearAmtBot) {
        this.yearAmtBot = yearAmtBot;
        return this;
    }

    public String getContractStatus() {
        return contractStatus;
    }

    public CStore setContractStatus(String contractStatus) {
        this.contractStatus = contractStatus;
        return this;
    }

    public String getCloseReson() {
        return closeReson;
    }

    public CStore setCloseReson(String closeReson) {
        this.closeReson = closeReson;
        return this;
    }

    public String getDisplay() {
        return display;
    }

    public CStore setDisplay(String display) {
        this.display = display;
        return this;
    }

    public String getStoreType() {
        return storeType;
    }

    public CStore setStoreType(String storeType) {
        this.storeType = storeType;
        return this;
    }

    public String getIsBusLicense() {
        return isBusLicense;
    }

    public CStore setIsBusLicense(String isBusLicense) {
        this.isBusLicense = isBusLicense;
        return this;
    }

    public Long getcStoreattrib23Id() {
        return cStoreattrib23Id;
    }

    public CStore setcStoreattrib23Id(Long cStoreattrib23Id) {
        this.cStoreattrib23Id = cStoreattrib23Id;
        return this;
    }

    public String getAreamngNew() {
        return areamngNew;
    }

    public CStore setAreamngNew(String areamngNew) {
        this.areamngNew = areamngNew;
        return this;
    }

    public String getBigareamngNew() {
        return bigareamngNew;
    }

    public CStore setBigareamngNew(String bigareamngNew) {
        this.bigareamngNew = bigareamngNew;
        return this;
    }

    public String getTaxNature() {
        return taxNature;
    }

    public CStore setTaxNature(String taxNature) {
        this.taxNature = taxNature;
        return this;
    }

    public String getGrade() {
        return grade;
    }

    public CStore setGrade(String grade) {
        this.grade = grade;
        return this;
    }

    public String getIsYtono() {
        return isYtono;
    }

    public CStore setIsYtono(String isYtono) {
        this.isYtono = isYtono;
        return this;
    }

    public String getDefault01() {
        return default01;
    }

    public CStore setDefault01(String default01) {
        this.default01 = default01;
        return this;
    }

    public String getDefault02() {
        return default02;
    }

    public CStore setDefault02(String default02) {
        this.default02 = default02;
        return this;
    }

    public String getDefault04() {
        return default04;
    }

    public CStore setDefault04(String default04) {
        this.default04 = default04;
        return this;
    }

    public String getDefault05() {
        return default05;
    }

    public CStore setDefault05(String default05) {
        this.default05 = default05;
        return this;
    }

    public String getDefault06() {
        return default06;
    }

    public CStore setDefault06(String default06) {
        this.default06 = default06;
        return this;
    }

    public String getDefault15() {
        return default15;
    }

    public CStore setDefault15(String default15) {
        this.default15 = default15;
        return this;
    }

    public BigDecimal getDefault03() {
        return default03;
    }

    public CStore setDefault03(BigDecimal default03) {
        this.default03 = default03;
        return this;
    }

    public String getDefault07() {
        return default07;
    }

    public CStore setDefault07(String default07) {
        this.default07 = default07;
        return this;
    }

    public String getDefault08() {
        return default08;
    }

    public CStore setDefault08(String default08) {
        this.default08 = default08;
        return this;
    }

    public String getDefault09() {
        return default09;
    }

    public CStore setDefault09(String default09) {
        this.default09 = default09;
        return this;
    }

    public String getDefault10() {
        return default10;
    }

    public CStore setDefault10(String default10) {
        this.default10 = default10;
        return this;
    }

    public String getDefault11() {
        return default11;
    }

    public CStore setDefault11(String default11) {
        this.default11 = default11;
        return this;
    }

    public String getDefault13() {
        return default13;
    }

    public CStore setDefault13(String default13) {
        this.default13 = default13;
        return this;
    }

    public String getDefault14() {
        return default14;
    }

    public CStore setDefault14(String default14) {
        this.default14 = default14;
        return this;
    }

    public String getDefault12() {
        return default12;
    }

    public CStore setDefault12(String default12) {
        this.default12 = default12;
        return this;
    }

    public String getOpDeviceId() {
        return opDeviceId;
    }

    public CStore setOpDeviceId(String opDeviceId) {
        this.opDeviceId = opDeviceId;
        return this;
    }

    public String getIsinvoice() {
        return isinvoice;
    }

    public CStore setIsinvoice(String isinvoice) {
        this.isinvoice = isinvoice;
        return this;
    }

    public String getReturnprice() {
        return returnprice;
    }

    public CStore setReturnprice(String returnprice) {
        this.returnprice = returnprice;
        return this;
    }

    public String getZxCustomerid() {
        return zxCustomerid;
    }

    public CStore setZxCustomerid(String zxCustomerid) {
        this.zxCustomerid = zxCustomerid;
        return this;
    }

    public String getInvoiceurl() {
        return invoiceurl;
    }

    public CStore setInvoiceurl(String invoiceurl) {
        this.invoiceurl = invoiceurl;
        return this;
    }

    public String getSbsnum() {
        return sbsnum;
    }

    public CStore setSbsnum(String sbsnum) {
        this.sbsnum = sbsnum;
        return this;
    }

    public String getIsuserrfid() {
        return isuserrfid;
    }

    public CStore setIsuserrfid(String isuserrfid) {
        this.isuserrfid = isuserrfid;
        return this;
    }

    public String getSuborderzt() {
        return suborderzt;
    }

    public CStore setSuborderzt(String suborderzt) {
        this.suborderzt = suborderzt;
        return this;
    }

    public String getCanGrabNocan() {
        return canGrabNocan;
    }

    public CStore setCanGrabNocan(String canGrabNocan) {
        this.canGrabNocan = canGrabNocan;
        return this;
    }

    public String getIsUnique() {
        return isUnique;
    }

    public CStore setIsUnique(String isUnique) {
        this.isUnique = isUnique;
        return this;
    }

    public String getIsCheckskuecrm() {
        return isCheckskuecrm;
    }

    public CStore setIsCheckskuecrm(String isCheckskuecrm) {
        this.isCheckskuecrm = isCheckskuecrm;
        return this;
    }

    public String getIsTorfid() {
        return isTorfid;
    }

    public CStore setIsTorfid(String isTorfid) {
        this.isTorfid = isTorfid;
        return this;
    }

    public String getUsePos() {
        return usePos;
    }

    public CStore setUsePos(String usePos) {
        this.usePos = usePos;
        return this;
    }

    public String getIsunirec() {
        return isunirec;
    }

    public CStore setIsunirec(String isunirec) {
        this.isunirec = isunirec;
        return this;
    }

    public Long getcStoreJdId() {
        return cStoreJdId;
    }

    public CStore setcStoreJdId(Long cStoreJdId) {
        this.cStoreJdId = cStoreJdId;
        return this;
    }

    public String getRemarkJd() {
        return remarkJd;
    }

    public CStore setRemarkJd(String remarkJd) {
        this.remarkJd = remarkJd;
        return this;
    }

    public Long getEbExpressJdId() {
        return ebExpressJdId;
    }

    public CStore setEbExpressJdId(Long ebExpressJdId) {
        this.ebExpressJdId = ebExpressJdId;
        return this;
    }

    public String getUseposition() {
        return useposition;
    }

    public CStore setUseposition(String useposition) {
        this.useposition = useposition;
        return this;
    }

    public String getPositionmd() {
        return positionmd;
    }

    public CStore setPositionmd(String positionmd) {
        this.positionmd = positionmd;
        return this;
    }

    public String getFhBrand() {
        return fhBrand;
    }

    public CStore setFhBrand(String fhBrand) {
        this.fhBrand = fhBrand;
        return this;
    }

    public String getInvoiceCompany() {
        return invoiceCompany;
    }

    public CStore setInvoiceCompany(String invoiceCompany) {
        this.invoiceCompany = invoiceCompany;
        return this;
    }

    public String getWechatCustomeridnew52() {
        return wechatCustomeridnew52;
    }

    public CStore setWechatCustomeridnew52(String wechatCustomeridnew52) {
        this.wechatCustomeridnew52 = wechatCustomeridnew52;
        return this;
    }

    public String getPosbtocPosbTermNo2() {
        return posbtocPosbTermNo2;
    }

    public CStore setPosbtocPosbTermNo2(String posbtocPosbTermNo2) {
        this.posbtocPosbTermNo2 = posbtocPosbTermNo2;
        return this;
    }

    public String getDatumNumber() {
        return datumNumber;
    }

    public CStore setDatumNumber(String datumNumber) {
        this.datumNumber = datumNumber;
        return this;
    }

    public String getVouInputType() {
        return vouInputType;
    }

    public CStore setVouInputType(String vouInputType) {
        this.vouInputType = vouInputType;
        return this;
    }

    public String getInitInputType() {
        return initInputType;
    }

    public CStore setInitInputType(String initInputType) {
        this.initInputType = initInputType;
        return this;
    }

    public String getStoreClerk() {
        return storeClerk;
    }

    public CStore setStoreClerk(String storeClerk) {
        this.storeClerk = storeClerk;
        return this;
    }

    public String getStoreSaleaccount() {
        return storeSaleaccount;
    }

    public CStore setStoreSaleaccount(String storeSaleaccount) {
        this.storeSaleaccount = storeSaleaccount;
        return this;
    }

    public String getStoreSaletaxnum() {
        return storeSaletaxnum;
    }

    public CStore setStoreSaletaxnum(String storeSaletaxnum) {
        this.storeSaletaxnum = storeSaletaxnum;
        return this;
    }

    public String getOldCode() {
        return oldCode;
    }

    public CStore setOldCode(String oldCode) {
        this.oldCode = oldCode;
        return this;
    }

    public String getSmallRoutine() {
        return smallRoutine;
    }

    public CStore setSmallRoutine(String smallRoutine) {
        this.smallRoutine = smallRoutine;
        return this;
    }

    public String getCheckCode() {
        return checkCode;
    }

    public CStore setCheckCode(String checkCode) {
        this.checkCode = checkCode;
        return this;
    }

    public String getOldXsqy() {
        return oldXsqy;
    }

    public CStore setOldXsqy(String oldXsqy) {
        this.oldXsqy = oldXsqy;
        return this;
    }

    public String getStoreName() {
        return storeName;
    }

    public CStore setStoreName(String storeName) {
        this.storeName = storeName;
        return this;
    }

    public String getFloorPlan() {
        return floorPlan;
    }

    public CStore setFloorPlan(String floorPlan) {
        this.floorPlan = floorPlan;
        return this;
    }

    public String getSeatingImg() {
        return seatingImg;
    }

    public CStore setSeatingImg(String seatingImg) {
        this.seatingImg = seatingImg;
        return this;
    }

    public String getTosolved() {
        return tosolved;
    }

    public CStore setTosolved(String tosolved) {
        this.tosolved = tosolved;
        return this;
    }

    public String getIsJpc() {
        return isJpc;
    }

    public CStore setIsJpc(String isJpc) {
        this.isJpc = isJpc;
        return this;
    }

    public String getZtMonthcode() {
        return ztMonthcode;
    }

    public CStore setZtMonthcode(String ztMonthcode) {
        this.ztMonthcode = ztMonthcode;
        return this;
    }

    public Long getcStoregsId() {
        return cStoregsId;
    }

    public CStore setcStoregsId(Long cStoregsId) {
        this.cStoregsId = cStoregsId;
        return this;
    }

    public String getIsTm() {
        return isTm;
    }

    public CStore setIsTm(String isTm) {
        this.isTm = isTm;
        return this;
    }

    public Long getTraninStoreId() {
        return traninStoreId;
    }

    public CStore setTraninStoreId(Long traninStoreId) {
        this.traninStoreId = traninStoreId;
        return this;
    }

    public String getIsCsc() {
        return isCsc;
    }

    public CStore setIsCsc(String isCsc) {
        this.isCsc = isCsc;
        return this;
    }

    public Long getcStoreattrib40Id() {
        return cStoreattrib40Id;
    }

    public CStore setcStoreattrib40Id(Long cStoreattrib40Id) {
        this.cStoreattrib40Id = cStoreattrib40Id;
        return this;
    }

    public Long getcStoreattrib41Id() {
        return cStoreattrib41Id;
    }

    public CStore setcStoreattrib41Id(Long cStoreattrib41Id) {
        this.cStoreattrib41Id = cStoreattrib41Id;
        return this;
    }

    public String getIsKg() {
        return isKg;
    }

    public CStore setIsKg(String isKg) {
        this.isKg = isKg;
        return this;
    }

    public String getIsYw() {
        return isYw;
    }

    public CStore setIsYw(String isYw) {
        this.isYw = isYw;
        return this;
    }

    public Integer getBeginNewzx() {
        return beginNewzx;
    }

    public CStore setBeginNewzx(Integer beginNewzx) {
        this.beginNewzx = beginNewzx;
        return this;
    }

    public Integer getEndNewzx() {
        return endNewzx;
    }

    public CStore setEndNewzx(Integer endNewzx) {
        this.endNewzx = endNewzx;
        return this;
    }

    public String getRemark1() {
        return remark1;
    }

    public CStore setRemark1(String remark1) {
        this.remark1 = remark1;
        return this;
    }

    public String getRemark2() {
        return remark2;
    }

    public CStore setRemark2(String remark2) {
        this.remark2 = remark2;
        return this;
    }

    public String getRemark3() {
        return remark3;
    }

    public CStore setRemark3(String remark3) {
        this.remark3 = remark3;
        return this;
    }

    public String getRemark4() {
        return remark4;
    }

    public CStore setRemark4(String remark4) {
        this.remark4 = remark4;
        return this;
    }

    public String getRemark5() {
        return remark5;
    }

    public CStore setRemark5(String remark5) {
        this.remark5 = remark5;
        return this;
    }

    public String getRemark6() {
        return remark6;
    }

    public CStore setRemark6(String remark6) {
        this.remark6 = remark6;
        return this;
    }

    public String getIsKcshop() {
        return isKcshop;
    }

    public CStore setIsKcshop(String isKcshop) {
        this.isKcshop = isKcshop;
        return this;
    }

    public String getKcshopCode() {
        return kcshopCode;
    }

    public CStore setKcshopCode(String kcshopCode) {
        this.kcshopCode = kcshopCode;
        return this;
    }

    public BigDecimal getBiggestDiscount() {
        return biggestDiscount;
    }

    public CStore setBiggestDiscount(BigDecimal biggestDiscount) {
        this.biggestDiscount = biggestDiscount;
        return this;
    }

    @Override
    public String toString() {
        return "CStoreModel{" +
            "id=" + id +
            ", adClientId=" + adClientId +
            ", adOrgId=" + adOrgId +
            ", isactive=" + isactive +
            ", modifierid=" + modifierid +
            ", creationdate=" + creationdate +
            ", modifieddate=" + modifieddate +
            ", ownerid=" + ownerid +
            ", name=" + name +
            ", description=" + description +
            ", cAreaId=" + cAreaId +
            ", lockcash=" + lockcash +
            ", address=" + address +
            ", phone=" + phone +
            ", fax=" + fax +
            ", contactorId=" + contactorId +
            ", monthfee=" + monthfee +
            ", isstop=" + isstop +
            ", rentbegin=" + rentbegin +
            ", rentend=" + rentend +
            ", proportion=" + proportion +
            ", empcnt=" + empcnt +
            ", checkdate=" + checkdate +
            ", iscenter=" + iscenter +
            ", isretail=" + isretail +
            ", mobil=" + mobil +
            ", sname=" + sname +
            ", postcal=" + postcal +
            ", calculation=" + calculation +
            ", cCustomerId=" + cCustomerId +
            ", cCustomerupId=" + cCustomerupId +
            ", cPriceareaId=" + cPriceareaId +
            ", isfairorig=" + isfairorig +
            ", areamngId=" + areamngId +
            ", limitqty=" + limitqty +
            ", limitamt=" + limitamt +
            ", limitmo=" + limitmo +
            ", markdis=" + markdis +
            ", dateblock=" + dateblock +
            ", cStoretypeJzId=" + cStoretypeJzId +
            ", imgurl1=" + imgurl1 +
            ", imgurl2=" + imgurl2 +
            ", imgurl3=" + imgurl3 +
            ", imgurl4=" + imgurl4 +
            ", imgurl5=" + imgurl5 +
            ", bigareamngId=" + bigareamngId +
            ", cProvinceId=" + cProvinceId +
            ", cCityId=" + cCityId +
            ", storesign=" + storesign +
            ", cStoretype=" + cStoretype +
            ", remark=" + remark +
            ", code=" + code +
            ", isufstore=" + isufstore +
            ", cStoreId=" + cStoreId +
            ", cDepartmentId=" + cDepartmentId +
            ", cClasscodeId=" + cClasscodeId +
            ", ufCode=" + ufCode +
            ", billdateFrist=" + billdateFrist +
            ", priority=" + priority +
            ", cBlockId=" + cBlockId +
            ", isfictitious=" + isfictitious +
            ", shopReceiveType=" + shopReceiveType +
            ", pospw=" + pospw +
            ", isblock=" + isblock +
            ", clopstoretype=" + clopstoretype +
            ", discount=" + discount +
            ", clopStore=" + clopStore +
            ", isgift=" + isgift +
            ", isdiscom=" + isdiscom +
            ", cDepartId=" + cDepartId +
            ", impMonth=" + impMonth +
            ", impType1=" + impType1 +
            ", impType2=" + impType2 +
            ", impType3=" + impType3 +
            ", taxrate=" + taxrate +
            ", cStoreattrib1Id=" + cStoreattrib1Id +
            ", cStoreattrib2Id=" + cStoreattrib2Id +
            ", cStoreattrib3Id=" + cStoreattrib3Id +
            ", cStoreattrib4Id=" + cStoreattrib4Id +
            ", cStoreattrib5Id=" + cStoreattrib5Id +
            ", cStoreattrib6Id=" + cStoreattrib6Id +
            ", cStoreattrib7Id=" + cStoreattrib7Id +
            ", cStoreattrib8Id=" + cStoreattrib8Id +
            ", cStoreattrib9Id=" + cStoreattrib9Id +
            ", cStoreattrib10Id=" + cStoreattrib10Id +
            ", cStoreattrib11Id=" + cStoreattrib11Id +
            ", cStoreattrib12Id=" + cStoreattrib12Id +
            ", cStoreattrib13Id=" + cStoreattrib13Id +
            ", cStoreattrib14Id=" + cStoreattrib14Id +
            ", cStoreattrib15Id=" + cStoreattrib15Id +
            ", cStoreattrib16Id=" + cStoreattrib16Id +
            ", cStoreattrib17Id=" + cStoreattrib17Id +
            ", cStoreattrib18Id=" + cStoreattrib18Id +
            ", cStoreattrib19Id=" + cStoreattrib19Id +
            ", cStoreattrib20Id=" + cStoreattrib20Id +
            ", isnegative=" + isnegative +
            ", tdefdowntypeId=" + tdefdowntypeId +
            ", cQtyaddareaId=" + cQtyaddareaId +
            ", cCorpId=" + cCorpId +
            ", usbkey=" + usbkey +
            ", ifebstore=" + ifebstore +
            ", dateEndaccount=" + dateEndaccount +
            ", isstock=" + isstock +
            ", yStore=" + yStore +
            ", cViptypeId1=" + cViptypeId1 +
            ", ifWms=" + ifWms +
            ", iforderstore=" + iforderstore +
            ", orderlimitdate=" + orderlimitdate +
            ", webposloginurl=" + webposloginurl +
            ", comptype=" + comptype +
            ", cStoreSql=" + cStoreSql +
            ", mDim1Id=" + mDim1Id +
            ", storetype=" + storetype +
            ", retchkorg=" + retchkorg +
            ", market=" + market +
            ", cStoregradeId=" + cStoregradeId +
            ", cStorekindId=" + cStorekindId +
            ", cIntegralareaId=" + cIntegralareaId +
            ", framworkAreaId=" + framworkAreaId +
            ", cArcbrandId=" + cArcbrandId +
            ", isRestore=" + isRestore +
            ", isMark=" + isMark +
            ", isRet=" + isRet +
            ", webposOffline=" + webposOffline +
            ", lowestDiscount=" + lowestDiscount +
            ", chkOverdays=" + chkOverdays +
            ", cMarkbaltypeId=" + cMarkbaltypeId +
            ", isvipintl=" + isvipintl +
            ", isvipdis=" + isvipdis +
            ", isonlycard=" + isonlycard +
            ", cPaywayDefault=" + cPaywayDefault +
            ", creditlimit=" + creditlimit +
            ", ebCreditrankId=" + ebCreditrankId +
            ", ebBonustypeId=" + ebBonustypeId +
            ", isTaobao=" + isTaobao +
            ", ebShiptypeId=" + ebShiptypeId +
            ", shelfdepth=" + shelfdepth +
            ", isMoresalesrep=" + isMoresalesrep +
            ", dim1Filter=" + dim1Filter +
            ", cPosaddrId=" + cPosaddrId +
            ", posprint=" + posprint +
            ", isModifypayamt=" + isModifypayamt +
            ", opendate=" + opendate +
            ", leaseperiod=" + leaseperiod +
            ", endsale=" + endsale +
            ", usemonth=" + usemonth +
            ", contract=" + contract +
            ", enddate=" + enddate +
            ", rencost=" + rencost +
            ", bearcompanyId=" + bearcompanyId +
            ", chargeofId=" + chargeofId +
            ", alipayKey=" + alipayKey +
            ", alipayPartnerid=" + alipayPartnerid +
            ", alipaySellMail=" + alipaySellMail +
            ", isMarketno=" + isMarketno +
            ", isManualint=" + isManualint +
            ", contactor=" + contactor +
            ", billdaterange=" + billdaterange +
            ", longitude=" + longitude +
            ", latitude=" + latitude +
            ", isCreatedata=" + isCreatedata +
            ", isTonc=" + isTonc +
            ", isCompared=" + isCompared +
            ", preopendate=" + preopendate +
            ", cStoreattrib21Id=" + cStoreattrib21Id +
            ", isProclose=" + isProclose +
            ", isCheckalias=" + isCheckalias +
            ", cConsumeareaId=" + cConsumeareaId +
            ", isCounter=" + isCounter +
            ", isExcstore=" + isExcstore +
            ", orgmodifyper=" + orgmodifyper +
            ", cDistrictId=" + cDistrictId +
            ", wechatCustomerid=" + wechatCustomerid +
            ", subMchId=" + subMchId +
            ", isaixiu=" + isaixiu +
            ", isSmartpay=" + isSmartpay +
            ", hrGroupId=" + hrGroupId +
            ", ebStorageType=" + ebStorageType +
            ", isJstype=" + isJstype +
            ", isBcloudStore=" + isBcloudStore +
            ", retailRetDay=" + retailRetDay +
            ", qNvl=" + qNvl +
            ", cMallId=" + cMallId +
            ", qPassword=" + qPassword +
            ", qSms=" + qSms +
            ", qCode=" + qCode +
            ", cBigareaId=" + cBigareaId +
            ", isBankbuy=" + isBankbuy +
            ", mainmediaaddress=" + mainmediaaddress +
            ", change=" + change +
            ", currency=" + currency +
            ", currencySign=" + currencySign +
            ", isretpay=" + isretpay +
            ", sfCardno=" + sfCardno +
            ", cPaywayFilter=" + cPaywayFilter +
            ", isAllpayway=" + isAllpayway +
            ", isDisstore=" + isDisstore +
            ", isUnionstore=" + isUnionstore +
            ", retailLocation=" + retailLocation +
            ", isMorecurrency=" + isMorecurrency +
            ", cCurrencyId=" + cCurrencyId +
            ", posAutoDis=" + posAutoDis +
            ", subsystemName=" + subsystemName +
            ", menuList=" + menuList +
            ", isretLevelOn=" + isretLevelOn +
            ", ispadpos=" + ispadpos +
            ", iskeepPwd=" + iskeepPwd +
            ", dateHourOffset=" + dateHourOffset +
            ", posSerialno=" + posSerialno +
            ", isHpmart=" + isHpmart +
            ", shopid=" + shopid +
            ", shopcode=" + shopcode +
            ", productcode=" + productcode +
            ", qCodePos=" + qCodePos +
            ", guestmachinecom=" + guestmachinecom +
            ", guestmachine=" + guestmachine +
            ", ismustentervip=" + ismustentervip +
            ", chkday=" + chkday +
            ", mallcode=" + mallcode +
            ", counternum=" + counternum +
            ", ebExpressId=" + ebExpressId +
            ", sdtstore=" + sdtstore +
            ", isBpayshowvoucher=" + isBpayshowvoucher +
            ", wechatCustomeridnew=" + wechatCustomeridnew +
            ", cCountryId=" + cCountryId +
            ", isO2o=" + isO2o +
            ", allowReceipt=" + allowReceipt +
            ", isModifyamtReason=" + isModifyamtReason +
            ", bposex=" + bposex +
            ", issdt=" + issdt +
            ", dxlx=" + dxlx +
            ", cUnionstoreId=" + cUnionstoreId +
            ", isAutoin=" + isAutoin +
            ", defaultChargepayway=" + defaultChargepayway +
            ", defaultlastdate=" + defaultlastdate +
            ", invoiceTemplate=" + invoiceTemplate +
            ", isShowvisitplan=" + isShowvisitplan +
            ", invoiceTaxno=" + invoiceTaxno +
            ", checkdatadown=" + checkdatadown +
            ", porderlimitdate=" + porderlimitdate +
            ", isCheckskuonline=" + isCheckskuonline +
            ", combineafter=" + combineafter +
            ", isRefreshnetwork=" + isRefreshnetwork +
            ", isForcebpos=" + isForcebpos +
            ", description1=" + description1 +
            ", isOxo=" + isOxo +
            ", jitStorecode=" + jitStorecode +
            ", cDistrYxjId=" + cDistrYxjId +
            ", isFilterviptype=" + isFilterviptype +
            ", isVipCheck=" + isVipCheck +
            ", isBrithdaydis=" + isBrithdaydis +
            ", browserOpenMode=" + browserOpenMode +
            ", isIntegralBase=" + isIntegralBase +
            ", isOrder=" + isOrder +
            ", canGrab=" + canGrab +
            ", isPerformanceShare=" + isPerformanceShare +
            ", morlthenote=" + morlthenote +
            ", ismorlthenotep=" + ismorlthenotep +
            ", isControl=" + isControl +
            ", isPrintElectronicinvoice=" + isPrintElectronicinvoice +
            ", openvipAmt=" + openvipAmt +
            ", morderlimitdate=" + morderlimitdate +
            ", printtpls4sglpdt=" + printtpls4sglpdt +
            ", invoicePhone=" + invoicePhone +
            ", invoiceAccount=" + invoiceAccount +
            ", invoiceBank=" + invoiceBank +
            ", invoiceAddr=" + invoiceAddr +
            ", invoiceCom=" + invoiceCom +
            ", mallName=" + mallName +
            ", mallNo=" + mallNo +
            ", mallAddress=" + mallAddress +
            ", isStono=" + isStono +
            ", isWmsstore=" + isWmsstore +
            ", wmsStorecode=" + wmsStorecode +
            ", isTowms=" + isTowms +
            ", isMarket=" + isMarket +
            ", cRealstoreId=" + cRealstoreId +
            ", description01=" + description01 +
            ", description02=" + description02 +
            ", description03=" + description03 +
            ", description04=" + description04 +
            ", description05=" + description05 +
            ", merchantNumber=" + merchantNumber +
            ", paymentKey=" + paymentKey +
            ", isDelivery=" + isDelivery +
            ", deskey=" + deskey +
            ", carrymode=" + carrymode +
            ", isSelectprint=" + isSelectprint +
            ", printtemplatelist=" + printtemplatelist +
            ", isExamination=" + isExamination +
            ", shouqianbacode=" + shouqianbacode +
            ", wechatCustomeridnew2=" + wechatCustomeridnew2 +
            ", shouqianbaUse=" + shouqianbaUse +
            ", o2ovoice=" + o2ovoice +
            ", pendvoice=" + pendvoice +
            ", merchant=" + merchant +
            ", vipprinttemplate=" + vipprinttemplate +
            ", jtk=" + jtk +
            ", recharge=" + recharge +
            ", comfirmBeforepay=" + comfirmBeforepay +
            ", monitorUrl=" + monitorUrl +
            ", managernavOpenMode=" + managernavOpenMode +
            ", voucherStoreType=" + voucherStoreType +
            ", ismoneny=" + ismoneny +
            ", mobilepaysolution=" + mobilepaysolution +
            ", internalPurchaseStore=" + internalPurchaseStore +
            ", padposTemplate=" + padposTemplate +
            ", allowCustomer=" + allowCustomer +
            ", branchidcard=" + branchidcard +
            ", posidcard=" + posidcard +
            ", onlineorder=" + onlineorder +
            ", vipActivate=" + vipActivate +
            ", isWade=" + isWade +
            ", isprintholdretail=" + isprintholdretail +
            ", md5=" + md5 +
            ", misposcard=" + misposcard +
            ", misposterminal=" + misposterminal +
            ", misposvision=" + misposvision +
            ", wechatCustomeridnew3=" + wechatCustomeridnew3 +
            ", wechatCustomeridnew4=" + wechatCustomeridnew4 +
            ", ccbPosbTermNo=" + ccbPosbTermNo +
            ", userId=" + userId +
            ", landiUnionpay=" + landiUnionpay +
            ", wechatCustomeridnew5=" + wechatCustomeridnew5 +
            ", posbtocPosbTermNo=" + posbtocPosbTermNo +
            ", ismobilepays=" + ismobilepays +
            ", consumerCardPayType=" + consumerCardPayType +
            ", paywebAppkey=" + paywebAppkey +
            ", manuallyenter=" + manuallyenter +
            ", isVip=" + isVip +
            ", cStoreattrib22Id=" + cStoreattrib22Id +
            ", isYundong=" + isYundong +
            ", isPengma=" + isPengma +
            ", inventoryYear=" + inventoryYear +
            ", warehouseRang=" + warehouseRang +
            ", closedate=" + closedate +
            ", isTingye=" + isTingye +
            ", cStoreattrib24Id=" + cStoreattrib24Id +
            ", cStoreattrib25Id=" + cStoreattrib25Id +
            ", cStoreattrib26Id=" + cStoreattrib26Id +
            ", cStoreattrib27Id=" + cStoreattrib27Id +
            ", cStoreattrib28Id=" + cStoreattrib28Id +
            ", cStoreattrib29Id=" + cStoreattrib29Id +
            ", cStoreattrib30Id=" + cStoreattrib30Id +
            ", cStoreattrib31Id=" + cStoreattrib31Id +
            ", cStoreattrib32Id=" + cStoreattrib32Id +
            ", cStoreattrib33Id=" + cStoreattrib33Id +
            ", cStoreattrib34Id=" + cStoreattrib34Id +
            ", cStoreattrib35Id=" + cStoreattrib35Id +
            ", cStoreattrib36Id=" + cStoreattrib36Id +
            ", cStoreattrib37Id=" + cStoreattrib37Id +
            ", cStoreattrib38Id=" + cStoreattrib38Id +
            ", cStoreattrib39Id=" + cStoreattrib39Id +
            ", hedianPinpai=" + hedianPinpai +
            ", marketName=" + marketName +
            ", yearRent=" + yearRent +
            ", decOpendate=" + decOpendate +
            ", taxDis=" + taxDis +
            ", yearAmtBot=" + yearAmtBot +
            ", contractStatus=" + contractStatus +
            ", closeReson=" + closeReson +
            ", display=" + display +
            ", storeType=" + storeType +
            ", isBusLicense=" + isBusLicense +
            ", cStoreattrib23Id=" + cStoreattrib23Id +
            ", areamngNew=" + areamngNew +
            ", bigareamngNew=" + bigareamngNew +
            ", taxNature=" + taxNature +
            ", grade=" + grade +
            ", isYtono=" + isYtono +
            ", default01=" + default01 +
            ", default02=" + default02 +
            ", default04=" + default04 +
            ", default05=" + default05 +
            ", default06=" + default06 +
            ", default15=" + default15 +
            ", default03=" + default03 +
            ", default07=" + default07 +
            ", default08=" + default08 +
            ", default09=" + default09 +
            ", default10=" + default10 +
            ", default11=" + default11 +
            ", default13=" + default13 +
            ", default14=" + default14 +
            ", default12=" + default12 +
            ", opDeviceId=" + opDeviceId +
            ", isinvoice=" + isinvoice +
            ", returnprice=" + returnprice +
            ", zxCustomerid=" + zxCustomerid +
            ", invoiceurl=" + invoiceurl +
            ", sbsnum=" + sbsnum +
            ", isuserrfid=" + isuserrfid +
            ", suborderzt=" + suborderzt +
            ", canGrabNocan=" + canGrabNocan +
            ", isUnique=" + isUnique +
            ", isCheckskuecrm=" + isCheckskuecrm +
            ", isTorfid=" + isTorfid +
            ", usePos=" + usePos +
            ", isunirec=" + isunirec +
            ", cStoreJdId=" + cStoreJdId +
            ", remarkJd=" + remarkJd +
            ", ebExpressJdId=" + ebExpressJdId +
            ", useposition=" + useposition +
            ", positionmd=" + positionmd +
            ", fhBrand=" + fhBrand +
            ", invoiceCompany=" + invoiceCompany +
            ", wechatCustomeridnew52=" + wechatCustomeridnew52 +
            ", posbtocPosbTermNo2=" + posbtocPosbTermNo2 +
            ", datumNumber=" + datumNumber +
            ", vouInputType=" + vouInputType +
            ", initInputType=" + initInputType +
            ", storeClerk=" + storeClerk +
            ", storeSaleaccount=" + storeSaleaccount +
            ", storeSaletaxnum=" + storeSaletaxnum +
            ", oldCode=" + oldCode +
            ", smallRoutine=" + smallRoutine +
            ", checkCode=" + checkCode +
            ", oldXsqy=" + oldXsqy +
            ", storeName=" + storeName +
            ", floorPlan=" + floorPlan +
            ", seatingImg=" + seatingImg +
            ", tosolved=" + tosolved +
            ", isJpc=" + isJpc +
            ", ztMonthcode=" + ztMonthcode +
            ", cStoregsId=" + cStoregsId +
            ", isTm=" + isTm +
            ", traninStoreId=" + traninStoreId +
            ", isCsc=" + isCsc +
            ", cStoreattrib40Id=" + cStoreattrib40Id +
            ", cStoreattrib41Id=" + cStoreattrib41Id +
            ", isKg=" + isKg +
            ", isYw=" + isYw +
            ", beginNewzx=" + beginNewzx +
            ", endNewzx=" + endNewzx +
            ", remark1=" + remark1 +
            ", remark2=" + remark2 +
            ", remark3=" + remark3 +
            ", remark4=" + remark4 +
            ", remark5=" + remark5 +
            ", remark6=" + remark6 +
            ", isKcshop=" + isKcshop +
            ", kcshopCode=" + kcshopCode +
            ", biggestDiscount=" + biggestDiscount +
            "}";
    }
}
