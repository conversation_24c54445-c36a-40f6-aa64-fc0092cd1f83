package com.jnby.mallasset.module.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-07-15 13:35:45
 * @Description: 商场资产规则配置
 */
@Data
@TableName("CASHIER_MALL_MEMBER_LOG")
@ApiModel(value="CashierMallMemberLog对象", description="会员流水表")
public class CashierMallMemberLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID")
    private Long id;
    @ApiModelProperty(value = "0未删除、1已删除")
    @TableField("IS_DELETE")
    private Integer isDelete;
    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "商场ID")
    @TableField("MALL_ID")
    private String mallId;
    @ApiModelProperty(value = "平台商场门店ID")
    @TableField("MALL_STORE_ID")
    private String mallStoreId;
    @ApiModelProperty(value = "伯俊门店ID")
    @TableField("BJ_STORE_ID")
    private String bjStoreId;
    @ApiModelProperty("商场平台：1-猫酷；2-银泰；3-华润")
    @TableField("MALL_PLATFORM")
    private Integer mallPlatform;
    @ApiModelProperty("手机号")
    @TableField("MOBILE")
    private String mobile;
    @ApiModelProperty("开卡用户ID")
    @TableField("OPEN_USER_ID")
    private String openUserId;
    @ApiModelProperty("是否执行成功：Y-是；N-否")
    @TableField("IS_EXECUTE")
    private String isExecute;
    @ApiModelProperty("是否已选择隐私政策：1-是；0-否")
    @TableField("HAS_CHOSE_PRIVACY")
    private Boolean hasChosePrivacy;
    @TableField(exist = false)
    private int count;
}
