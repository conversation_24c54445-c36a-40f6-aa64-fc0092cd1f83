package com.jnby.mallasset.dto.req.order;

import com.jnby.mallasset.api.dto.BaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("售后退积分请求入参")
public class OrderRefundReq extends BaseReq {

    @ApiModelProperty(value = "订单编号",required = true)
    private String ordNum;
    @ApiModelProperty(value = "订单付款编号")
    private String ordPayNo;
    @ApiModelProperty(value = "退款单号")
    private String refundNo;
    @ApiModelProperty(value = "会员手机号")
    private String memberTel;
    @ApiModelProperty(value = "外部会员编码")
    private String outerMemberId;
    @ApiModelProperty(value = "原订单实付金额")
    private BigDecimal ordAmount;
    @ApiModelProperty(value = "业务交易id")
    private String exchangeId;
    @ApiModelProperty(value = "业务交易单号")
    private String exchangeNo;
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;
    @ApiModelProperty(value = "退款原因")
    private String ordReason;
    @ApiModelProperty(value = "原订单完成时间")
    private Long ordFinishTime;
    @ApiModelProperty(value = "退单时间")
    private Long refundTime;
    @ApiModelProperty(value = "会员openID")
    private String ordOpenId;
    @ApiModelProperty(value = "回调地址")
    private String notifyUrl;
    @ApiModelProperty(value = "订单商品列表")
    private List<ProductItem> productItemList;
    @ApiModelProperty(value = "订单付款列表")
    private List<PayItem> payItemList;
}
