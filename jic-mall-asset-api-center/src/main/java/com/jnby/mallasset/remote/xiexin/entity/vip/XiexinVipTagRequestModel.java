package com.jnby.mallasset.remote.xiexin.entity.vip;

import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

@Data
@Root(name = "GetVipLableList", strict = false)
@Namespace(reference = "http://tempuri.org/")
public class XiexinVipTagRequestModel {

    @Element(name = "jlbh" , required = false)
    private int jlbh;

    @Element(name = "vipid" , required = false)
    private int vipid;

}
