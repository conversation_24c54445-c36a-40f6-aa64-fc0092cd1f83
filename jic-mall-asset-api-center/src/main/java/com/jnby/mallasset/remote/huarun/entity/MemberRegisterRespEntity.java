package com.jnby.mallasset.remote.huarun.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MemberRegisterRespEntity implements Serializable {
    @ApiModelProperty(value = "会员ID")
    private String memberId;
    @ApiModelProperty(value = "会员标识：0-新会员；1-老会员")
    private int memberFlag;
}
