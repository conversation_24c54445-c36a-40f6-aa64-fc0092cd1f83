package com.jnby.mallasset.remote.mallcoo.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Builder
@ApiModel("可用券列表回参")
@Data
public class CouponCanUseListRespEntity implements Serializable {

    @ApiModelProperty("可用券列表")
    private List<CouponInfo> CouponInfoList;

    @ApiModelProperty("当前查询条件下的数据总数量")
    private Integer Count;

    @Data
    @ApiModel("券详情")
    public static class CouponInfo implements Serializable {

        @ApiModelProperty("券ID")
        private Long CouponID;
        @ApiModelProperty("券标题(名称)")
        private String CouponName;
        @ApiModelProperty("券模板编号")
        private String CouponRuleNo;
        @ApiModelProperty("券副标题")
        private String CouponSubtitle;
        @ApiModelProperty("卡券类型:1-代金券 2-折扣券 3-兑换券 4-优惠券 5-停车券 6-团购券")
        private Integer CouponType;
        @ApiModelProperty("券码")
        private String VCode;
        @ApiModelProperty("券简介")
        private String CouponDesc;
        @ApiModelProperty("启用时间")
        private String EnableTime;
        @ApiModelProperty("过期时间")
        private String OverdueTime;
        @ApiModelProperty("每周可用时段集合(存储：1 [代表周一]，2 [代表周二]…)")
        private List<Integer> PartTimeList;
        //        @ApiModelProperty("每日可用时段")
//        private List<DailyPartTime> DPTList;
        @ApiModelProperty("减免金额(元)【代金券专用】")
        private Double ReduceMoney;
        @ApiModelProperty("抵扣条件(消费满多少元可用。如不写则默认：消费满任意金额可用)[元]【代金券专用】")
        private Double Deductible;
        @ApiModelProperty("折扣额度【折扣券专用】（1 ~ 9.9） [1代表一折]")
        private Double DiscountAmount;
        @ApiModelProperty("兑换说明【兑换券专用】")
        private String ExchangeInfo;
        @ApiModelProperty("优惠说明【优惠券专用】")
        private String PromotionInfo;
    }

}
