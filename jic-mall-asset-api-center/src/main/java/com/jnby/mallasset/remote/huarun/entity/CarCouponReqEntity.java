package com.jnby.mallasset.remote.huarun.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class CarCouponReqEntity {
    @ApiModelProperty("需要发放的会员手机号")
    @JSONField(ordinal = 1)
    private String memberTel;
    @ApiModelProperty("卡券类型，默认传1,代表私有车厂停车抵值券，2代表一点万象停车券")
    @JSONField(ordinal = 2)
    private int voucherType;
    @ApiModelProperty("停车券模板")
    @JSONField(ordinal = 3)
    private String voucherTemplate;
    @ApiModelProperty("卡券规则信息")
    @JSONField(ordinal = 4)
    private CarVoucherRule voucherRule;
}
