package com.jnby.mallasset.remote.huarun.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BaseHuaRunResp<T> implements Serializable {
    @ApiModelProperty(value = "结果")
    private int result;
    @ApiModelProperty(value = "返回消息")
    private String msg;
    @ApiModelProperty(value = "返回时间")
    private String returnTime;
    @ApiModelProperty("响应数据")
    private T query;

    public boolean isSuccess(){
        if (this.result == 1) {
            return true;
        }
        return false;
    }
}
