package com.jnby.mallasset.dto.req.order;

import com.alibaba.fastjson.annotation.JSONField;
import com.jnby.mallasset.api.dto.BaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("订单POS数据实体")
public class OrderPosParam {

    @ApiModelProperty(value = "猫酷项目ID")
    @JSONField(ordinal = 1)
    private String projectid;

    @ApiModelProperty(value = "事件唯一标识符")
    @JSONField(ordinal = 2)
    private String unique_id;

    @ApiModelProperty(value = "事件发生时间")
    @JSONField(ordinal = 3)
    private String time;

    @ApiModelProperty(value = "数据来源")
    @JSONField(ordinal = 4)
    private String sys_source_code;

    @ApiModelProperty(value = "POS机号")
    @JSONField(ordinal = 5)
    private String pos_number;

    @ApiModelProperty(value = "商铺ID")
    @JSONField(ordinal = 6)
    private String brand_id;

    @ApiModelProperty(value = "交易时间")
    @JSONField(ordinal = 7)
    private String transaction_time;

    @ApiModelProperty(value = "支付金额")
    @JSONField(ordinal = 8)
    private BigDecimal paid_amount;

    @ApiModelProperty(value = "流水号")
    @JSONField(ordinal = 9)
    private String order_number;
}
