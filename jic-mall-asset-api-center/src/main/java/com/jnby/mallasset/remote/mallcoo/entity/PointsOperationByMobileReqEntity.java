package com.jnby.mallasset.remote.mallcoo.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Builder
@ApiModel("扣减积分入参")
@Data
public class PointsOperationByMobileReqEntity implements Serializable {
    @ApiModelProperty("用户手机号")
    @JSONField(ordinal=1, name = "Mobile")
    private String Mobile;

    @ApiModelProperty("积分")
    @JSONField(ordinal=2, name = "Score")
    private Integer Score;

    @ApiModelProperty("积分事件")
    @JSONField(ordinal=3, name = "ScoreEvent")
    private Integer ScoreEvent;

    @ApiModelProperty("交易流水号")
    @JSONField(ordinal=4, name = "TransID")
    private String TransID;

    @ApiModelProperty("积分变动原因（注意：中文需要进行UTF-8 (UrlEncode)编码）")
    @JSONField(ordinal=5, name = "Reason")
    private String Reason;
}
