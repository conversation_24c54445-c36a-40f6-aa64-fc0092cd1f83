package com.jnby.mallasset.strategy.platform.kkmall;

import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import com.jnby.mallasset.constant.MallRedisKeyConstant;
import com.jnby.mallasset.module.mapper.box.CashierMallStoreConfigMapper;
import com.jnby.mallasset.module.model.CashierMallStoreConfig;
import com.jnby.mallasset.remote.huarun.IHuaRunRemoteHttpApi;
import com.jnby.mallasset.remote.huarun.entity.BaseHuaRunResp;
import com.jnby.mallasset.remote.huarun.entity.UserLoginReqEntity;
import com.jnby.mallasset.remote.huarun.entity.UserLoginRespEntity;
import com.jnby.mallasset.remote.kkmall.IKkMallRemoteHttpApi;
import com.jnby.mallasset.remote.kkmall.entity.BaseKkMallResp;
import com.jnby.mallasset.remote.kkmall.entity.UserTokenReqEntity;
import com.jnby.mallasset.remote.kkmall.entity.UserTokenRespEntity;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;

/**
 * 华润用户登录token
 */
@Service
@AllArgsConstructor
@Slf4j
public class KkMallTokenService {

    private IKkMallRemoteHttpApi kkMallRemoteHttpApi;
    private RedisPoolUtil redisPoolUtil;
    private CashierMallStoreConfigMapper cashierMallStoreConfigMapper;

    public String getUserToken(String storeId) throws IOException {
        String tokenKey = MallRedisKeyConstant.User.TOKEN_KEY_KKMALL + storeId;
        String token = RedisTemplateUtil.get(redisPoolUtil,tokenKey);
        if(token != null){
            return token;
        }
        UserTokenReqEntity req = new UserTokenReqEntity();
        // 根据storeId查询用户名、密码
        CashierMallStoreConfig config = cashierMallStoreConfigMapper.selectByStoreId(storeId);
        req.setUsername(config.getPlatformPublicKey());
        req.setPassword(config.getPlatformPrivateKey());
        Response<BaseKkMallResp<UserTokenRespEntity>> execute = kkMallRemoteHttpApi.userToken(req).execute();
        BaseKkMallResp<UserTokenRespEntity> body = execute.body();
        if(body.isSuccess() && body.getCode() == 0){
            token = body.getResult().getToken();
            RedisTemplateUtil.setex(redisPoolUtil, tokenKey, token, MallRedisKeyConstant.Expire.TWO_HOUR);
        }
        return token;
    }
}
