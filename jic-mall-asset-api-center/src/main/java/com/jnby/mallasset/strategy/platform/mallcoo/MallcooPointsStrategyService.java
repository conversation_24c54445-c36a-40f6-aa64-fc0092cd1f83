package com.jnby.mallasset.strategy.platform.mallcoo;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.jnby.mallasset.config.exception.MallException;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.remote.mallcoo.IMallCooRemoteHttpApi;
import com.jnby.mallasset.remote.mallcoo.MallCooUtils;
import com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooResp;
import com.jnby.mallasset.remote.mallcoo.entity.PointsOperationByMobileReqEntity;
import com.jnby.mallasset.strategy.category.AbstractPointsService;
import com.jnby.mallasset.strategy.context.PointContext;
import com.jnby.mallasset.strategy.context.UserContext;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * 猫酷平台积分策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.MALL_COO, category = PlatformCategoryTypeEnum.POINTS)
@AllArgsConstructor
@Slf4j
public class MallcooPointsStrategyService extends AbstractPointsService {
    @Autowired
    private IMallCooRemoteHttpApi mallCooRemoteHttpApi;


    @Override
    public void checkUser(UserContext user) {
        Preconditions.checkArgument(Objects.nonNull(user), "必要信息不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(user.getPhone()), "消费者不能为空");
        Preconditions.checkArgument(Objects.nonNull(user.getStoreConfig()), "门店配置不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(user.getStoreConfig().getPlatformAppId()), "商家应用ID不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(user.getStoreConfig().getPlatformPublicKey()), "商家公钥不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(user.getStoreConfig().getPlatformPrivateKey()), "商家私钥不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(user.getStoreConfig().getPlatformMallStoreId()), "商家门店ID不能为空");
        // 该是否能使用积分资产
        if (Boolean.FALSE.equals(user.getMallConfig().getPointsCanUse())) {
            throw new MallException(SystemErrorEnum.POINTS_CONFIG_CAN_NOT_USE_ERROR);
        }
    }

    @Override
    public void preCheckUsePoints(UserContext user, PointContext req) {
        Preconditions.checkArgument(Objects.nonNull(req), "必要请求信息不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getTraceId()), "事务ID不能为空");
        Preconditions.checkArgument(Objects.nonNull(req.getPoints()), "操作积分不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getRemark()), "备注不能为空");
    }

    @Override
    public void doUsePoints(UserContext user, PointContext req) {
        String appId = user.getStoreConfig().getPlatformAppId();
        String platformPublicKey = user.getStoreConfig().getPlatformPublicKey();
        String platformPrivateKey = user.getStoreConfig().getPlatformPrivateKey();
        String platformMallStoreId = user.getStoreConfig().getPlatformMallStoreId();
        try {
            PointsOperationByMobileReqEntity reqEntity = PointsOperationByMobileReqEntity.builder().Mobile(user.getPhone()).TransID(req.getTraceId()).Reason(req.getRemark()).Score(req.getPoints()).ScoreEvent(2000).build();
            Map<String, String> headers = MallCooUtils.getHeaders(appId, platformPublicKey, platformPrivateKey, JSON.toJSONString(reqEntity));
            log.info("猫酷积分使用调用远程接口入参，headers:[{}]--------------req:[{}]", JSON.toJSONString(headers), JSON.toJSONString(reqEntity));
            Response<BaseMallCooResp> execute = mallCooRemoteHttpApi.subtractPointsByMobile(headers, reqEntity).execute();
            boolean successful = execute.isSuccessful();
            if (!successful) {
                throw new MallException(SystemErrorEnum.POINTS_USE_REQUEST_ERROR);
            }
            BaseMallCooResp body = execute.body();
            log.info("猫酷积分使用调用远程接口响应：{}", JSON.toJSONString(body));
            if (!body.isSuccess()) {
                throw new MallException(SystemErrorEnum.POINTS_USE_FAIL_ERROR);
            }
        } catch (IOException e) {
            log.error("猫酷积分使用超时", e);
            throw new MallException(SystemErrorEnum.POINTS_USE_TIMEOUT_ERROR);
        } catch (MallException e) {
            log.error("猫酷积分使用已知异常", e);
            throw e;
        } catch (Exception e) {
            log.error("猫酷积分使用异常", e);
            throw new MallException(SystemErrorEnum.POINTS_USE_UNKNOWN_ERROR);
        }
    }

    @Override
    public void preCheckReturnPoints(UserContext user, PointContext req) {

    }

    @Override
    public void doReturnPoints(UserContext user, PointContext req) {
        String appId = user.getStoreConfig().getPlatformAppId();
        String platformPublicKey = user.getStoreConfig().getPlatformPublicKey();
        String platformPrivateKey = user.getStoreConfig().getPlatformPrivateKey();
        String platformMallStoreId = user.getStoreConfig().getPlatformMallStoreId();
        try {
            PointsOperationByMobileReqEntity reqEntity = PointsOperationByMobileReqEntity.builder().Mobile(user.getPhone()).TransID(req.getTraceId()).Reason(req.getRemark()).Score(req.getPoints()).ScoreEvent(2000).build();
            Map<String, String> headers = MallCooUtils.getHeaders(appId, platformPublicKey, platformPrivateKey, JSON.toJSONString(reqEntity));
            log.info("猫酷积分返还调用远程接口入参，headers:[{}]--------------req:[{}]", JSON.toJSONString(headers), JSON.toJSONString(reqEntity));
            Response<BaseMallCooResp> execute = mallCooRemoteHttpApi.plusPointsByMobile(headers, reqEntity).execute();
            boolean successful = execute.isSuccessful();
            if (!successful) {
                throw new MallException(SystemErrorEnum.POINTS_RETURN_REQUEST_ERROR);
            }
            BaseMallCooResp body = execute.body();
            log.info("猫酷积分返还调用远程接口响应：{}", JSON.toJSONString(body));
            if (!body.isSuccess()) {
                throw new MallException(SystemErrorEnum.POINTS_RETURN_FAIL_ERROR);
            }
        } catch (IOException e) {
            log.error("猫酷积分返还超时", e);
            throw new MallException(SystemErrorEnum.POINTS_RETURN_TIMEOUT_ERROR);
        } catch (MallException e) {
            log.error("猫酷积分返还已知异常", e);
            throw e;
        } catch (Exception e) {
            log.error("猫酷积分返还异常", e);
            throw new MallException(SystemErrorEnum.POINTS_RETURN_UNKNOWN_ERROR);
        }
    }
}
