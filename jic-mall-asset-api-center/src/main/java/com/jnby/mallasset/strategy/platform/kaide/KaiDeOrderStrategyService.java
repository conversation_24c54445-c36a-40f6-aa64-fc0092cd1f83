package com.jnby.mallasset.strategy.platform.kaide;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.dto.req.order.PayItem;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.remote.dingding.IDingDingRemoteHttpApi;
import com.jnby.mallasset.remote.dingding.entity.DingDingMsg;
import com.jnby.mallasset.remote.dingding.entity.DingDingResponse;
import com.jnby.mallasset.remote.kaide.IKaiDeRemoteHttpApi;
import com.jnby.mallasset.remote.kaide.constant.FieldConstant;
import com.jnby.mallasset.remote.kaide.entity.*;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 凯德订单平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.KAI_DE, category = PlatformCategoryTypeEnum.ORDER)
@Slf4j
@AllArgsConstructor
public class KaiDeOrderStrategyService extends AbstractOrderService {

    private CashierMallOrderLogMapper cashierMallOrderLogMapper;
    private IKaiDeRemoteHttpApi kaiDeRemoteHttpApi;
    private KaiDeMemberStrategyService kaiDeMemberStrategyService;
    private IDingDingRemoteHttpApi dingDingRemoteHttpApi;

    @Override
    public ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("凯德订单同步输入参数：{}", JSON.toJSONString(orderConsumeReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderConsumeReq, cashierMallAssetStoreRef);

            // 判断会员是否凯德会员
            ResponseResult result = checkNewMemberWithOrder(orderConsumeReq, cashierMallAssetStoreRef, cashierMallOrderLog);
            if (result.getCode() != 0) {
                log.info("凯德订单同步校验该手机号{}非凯德会员",orderConsumeReq.getMemberTel());
                return result;
            }
            orderConsumeReq.setOuterMemberId(String.valueOf(result.getData()));
            Map<String, String> header = new HashMap<>();
            header.put(FieldConstant.HEADER_SUBSCRIPTION_KEY,cashierMallAssetStoreRef.getPlatformPublicKey());
            header.put(FieldConstant.HEADER_TOKEN,cashierMallAssetStoreRef.getPlatformPrivateKey());
            KaiDeOrderReqEntity req = initKaiDeOrderReqEntity(orderConsumeReq,cashierMallAssetStoreRef);
            log.info("凯德订单同步调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseKaiDeResp<KaiDeOrderRespEntity>> execute = kaiDeRemoteHttpApi.orderRegister(header,req).execute();
            BaseKaiDeResp<KaiDeOrderRespEntity> body = execute.body();
            if(body != null && body.getErrorCode() == 0){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setMsg(body.getMessage());
                KaiDeOrderRespEntity orderRespEntity = body.getBody();
                BaseOrderResponseEntity baseOrderResponse = new BaseOrderResponseEntity();
                baseOrderResponse.setOrdNum(orderConsumeReq.getOrdNum());
                baseOrderResponse.setExchangeId(orderRespEntity.getIdentifier());
                baseOrderResponse.setExchangeNo(orderRespEntity.getIdentifier());
                commonResponse.setData(baseOrderResponse);
                // 更新返回的参数
                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLog.setUpdateExchangeId(orderRespEntity.getIdentifier());
                cashierMallOrderLog.setUpdateExchangeNo(orderRespEntity.getIdentifier());
                cashierMallOrderLog.setRemark(ResultCodeEnum.SUCCESS.getMsg());
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            }else {
                if(execute.errorBody() != null){
                    String errorBody = execute.errorBody().string();
                    BaseKaiDeResp baseKaiDeResp = JSONObject.parseObject(errorBody,BaseKaiDeResp.class);
                    commonResponse.setCode(baseKaiDeResp.getErrorCode());
                    commonResponse.setMsg(baseKaiDeResp.getMessage());
                    commonResponse.setData(baseKaiDeResp.getBody());
                    cashierMallOrderLog.setRemark(baseKaiDeResp.getMessage());
                    cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
                }
            }
            log.info("凯德订单同步返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 凯德订单同步失败",orderConsumeReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    private ResponseResult checkNewMemberWithOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef, CashierMallOrderLog cashierMallOrderLog) {
        ResponseResult commonResponse = new ResponseResult();
        MemberRegisterReq memberRegisterReq = new MemberRegisterReq();
        memberRegisterReq.setMobile(orderConsumeReq.getMemberTel());
        ResponseResult<KaiDeMemberRespEntity> result = kaiDeMemberStrategyService.getMemberInfo(memberRegisterReq, cashierMallAssetStoreRef);
        if(result.getCode() != 200){
            cashierMallOrderLog.setIsExecute("Y");
            cashierMallOrderLog.setRemark(result.getMsg());
            cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);

            BaseOrderResponseEntity baseOrderResponse = new BaseOrderResponseEntity();
            baseOrderResponse.setOrdNum(orderConsumeReq.getOrdNum());
            baseOrderResponse.setExchangeId(orderConsumeReq.getOrdNum());
            baseOrderResponse.setExchangeNo(orderConsumeReq.getOrdNum());
            commonResponse.setData(baseOrderResponse);
            commonResponse.setCode(200);
            commonResponse.setMsg(result.getMsg());
        }else {
            commonResponse.setCode(0);
            commonResponse.setData(result.getData().getMemberNo());
        }
        return commonResponse;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeNo(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setBjStoreId(orderConsumeReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(1);
        cashierMallOrderLog.setRefundAmount(BigDecimal.ZERO);
        cashierMallOrderLogMapper.insertAddLog(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    private static KaiDeOrderReqEntity initKaiDeOrderReqEntity(OrderConsumeReq orderConsumeReq,CashierMallAssetStoreRef assetStoreRef) {
        KaiDeOrderReqEntity req = new KaiDeOrderReqEntity();
        req.setMemberNo(orderConsumeReq.getOuterMemberId());
        req.setShop(assetStoreRef.getPlatformMallStoreId());
        String transTime = DateUtil.timeStamp3Date(orderConsumeReq.getOrdFinishTime(),null);
        req.setTransTime(transTime);
        req.setReceiptNo(orderConsumeReq.getOrdNum());
        req.setAmount(orderConsumeReq.getOrdAmount());
        req.setMethodCode(assetStoreRef.getPlatformAppId());
        req.setAmountReal(orderConsumeReq.getOrdAmount());
        req.setRemark(orderConsumeReq.getOrdChannel());
        List<Payment> payments = new ArrayList<>();
        List<PayItem> payItemList = orderConsumeReq.getPayItemList();
        if(CollectionUtil.isNotEmpty(payItemList)){
            payItemList.forEach(payItem -> {
                if(payItem.getId() == 62 || payItem.getId() == 231){
                    Payment payment = new Payment();
                    payment.setAmount(payItem.getPayamount());
                    payment.setPaymentType("wechat");
                    payments.add(payment);
                }
            });
            req.setPayments(payments);
        }
        return req;
    }

    @Override
    public ResponseResult pushRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("凯德订单退货退款输入参数：{}", JSON.toJSONString(orderRefundReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderRefundReq, cashierMallAssetStoreRef);

            // 校验是否新会员
            ResponseResult result = checkNewMemberWithRefund(orderRefundReq, cashierMallAssetStoreRef, cashierMallOrderLog);
            if (result.getCode() != 0){
                log.info("凯德订单退货退款校验该手机号{}非凯德会员",orderRefundReq.getMemberTel());
                return result;
            }
            orderRefundReq.setOuterMemberId(String.valueOf(result.getData()));
            CashierMallOrderLog orderLog = cashierMallOrderLogMapper.selectOrderLogByOrderNumAndExchangeId(orderRefundReq.getOrdNum(),orderRefundReq.getExchangeId());
            // 判断退款金额和总金额是否相等
            BigDecimal refundAmount = orderRefundReq.getRefundAmount();
            BigDecimal ordAmount = orderRefundReq.getOrdAmount();
            if(ordAmount.compareTo(refundAmount) == 0){
                // 整单退逻辑
                orderRefundReq.setExchangeNo(orderLog.getExchangeNo());
                doAllRefundBusiness(orderRefundReq, cashierMallAssetStoreRef, commonResponse, cashierMallOrderLog);
                log.info("凯德订单整单退货退款返回参数：{}", JSON.toJSONString(commonResponse));
                return commonResponse;
            }else {
                // 部分退逻辑,先退上笔单的总额，再创建剩余金额的新单
                Map<String, String> header = new HashMap<>();
                header.put(FieldConstant.HEADER_SUBSCRIPTION_KEY, cashierMallAssetStoreRef.getPlatformPublicKey());
                header.put(FieldConstant.HEADER_TOKEN, cashierMallAssetStoreRef.getPlatformPrivateKey());
                KaiDeOrderRefundReqEntity req = new KaiDeOrderRefundReqEntity();
                req.setMemberNo(orderRefundReq.getOuterMemberId());
                // 关键参数
                req.setIdentifier(orderLog.getExchangeNo());
                req.setMethodCode(cashierMallAssetStoreRef.getPlatformAppId());
                log.info("凯德订单部分退货退款调用远程接口参数：{}", JSON.toJSONString(req));
                Response<BaseKaiDeResp<KaiDeOrderRefundRespEntity>> execute = kaiDeRemoteHttpApi.orderRefund(header,req).execute();
                BaseKaiDeResp<KaiDeOrderRefundRespEntity> body = execute.body();
                if(body != null && body.getErrorCode() == 0){
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    commonResponse.setMsg(body.getMessage());
                    KaiDeOrderRefundRespEntity orderRefundRespEntity = body.getBody();
                    BaseOrderResponseEntity baseOrderResponse = new BaseOrderResponseEntity();
                    baseOrderResponse.setOrdNum(orderRefundReq.getOrdNum());
                    baseOrderResponse.setExchangeId(orderRefundRespEntity.getIdentifier());
                    baseOrderResponse.setExchangeNo(orderRefundRespEntity.getIdentifier());
                    commonResponse.setData(baseOrderResponse);
                    // 更新返回的参数
                    cashierMallOrderLog.setIsExecute("Y");
                    cashierMallOrderLog.setUpdateExchangeId(orderRefundRespEntity.getIdentifier());
                    cashierMallOrderLog.setRemark(ResultCodeEnum.SUCCESS.getMsg());
                    cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
                }else {
                    if(execute.errorBody() != null){
                        String errorBody = execute.errorBody().string();
                        BaseKaiDeResp baseKaiDeResp = JSONObject.parseObject(errorBody,BaseKaiDeResp.class);
                        commonResponse.setCode(baseKaiDeResp.getErrorCode());
                        commonResponse.setMsg("凯德订单部分退款失败：" + baseKaiDeResp.getMessage());
                        commonResponse.setData(baseKaiDeResp.getBody());
                        cashierMallOrderLog.setRemark(baseKaiDeResp.getMessage());
                        cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
                    }
                }
                if(commonResponse.getCode() == 200){
                    // 创建一笔新单,查询剩余金额
                    CashierMallOrderLog mallOrderLog = cashierMallOrderLogMapper.selectSumRefundAmount(orderRefundReq.getOrdNum());
                    BigDecimal refundAmountSum = mallOrderLog.getRefundAmount();
                    int isNeedCreate = ordAmount.subtract(refundAmountSum).compareTo(BigDecimal.ZERO);
                    if(isNeedCreate != 0){
                        // 不为0需要新建订单
                        BigDecimal amount = ordAmount.subtract(refundAmountSum);
                        // 记录新单流水
                        CashierMallOrderLog logInfo = recordNewOrderLog(orderRefundReq, cashierMallAssetStoreRef);

                        KaiDeOrderReqEntity kaiDeOrderReqEntity = initKaiDeOrderReqEntity(orderRefundReq, cashierMallAssetStoreRef, amount);
                        log.info("凯德订单同步创建新订单调用远程接口参数：{}", JSON.toJSONString(kaiDeOrderReqEntity));
                        Response<BaseKaiDeResp<KaiDeOrderRespEntity>> response = kaiDeRemoteHttpApi.orderRegister(header,kaiDeOrderReqEntity).execute();
                        BaseKaiDeResp<KaiDeOrderRespEntity> baseKaiDeResp = response.body();
                        if(baseKaiDeResp != null && baseKaiDeResp.getErrorCode() == 0){
                            KaiDeOrderRespEntity orderRespEntity = baseKaiDeResp.getBody();
                            String identifier = orderRespEntity.getIdentifier();
                            // 更新原单的exchangeNo
                            CashierMallOrderLog mallOrderLog1 = new CashierMallOrderLog();
                            mallOrderLog1.setOrdNum(orderRefundReq.getOrdNum());
                            mallOrderLog1.setExchangeNo(identifier);
                            mallOrderLog1.setExchangeId(orderRefundReq.getExchangeId());
                            mallOrderLog1.setRemark(ResultCodeEnum.SUCCESS.getMsg());
                            mallOrderLog1.setRefundAmount(amount);
                            cashierMallOrderLogMapper.updateExchangeNoWithParam(mallOrderLog1);

                            logInfo.setUpdateExchangeId(identifier);
                            logInfo.setRemark(ResultCodeEnum.SUCCESS.getMsg());
                            cashierMallOrderLogMapper.updateOrderLogByParam(logInfo);
                        }else {
                            if(execute.errorBody() != null){
                                String errorBody = response.errorBody().string();
                                BaseKaiDeResp deResp = JSONObject.parseObject(errorBody,BaseKaiDeResp.class);
                                commonResponse.setCode(deResp.getErrorCode());
                                commonResponse.setMsg("凯德原单已完成部分退款，但创建新单失败：" + deResp.getMessage());
                                commonResponse.setData(deResp.getBody());
                                // 推送订单消息
                                pushKaiDeMsg(kaiDeOrderReqEntity, deResp);
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 凯德订单退货退款失败",orderRefundReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    private void pushKaiDeMsg(KaiDeOrderReqEntity kaiDeOrderReqEntity, BaseKaiDeResp deResp) throws IOException {
        String msg = "数据推送异常：\n" + "功能：凯德订单原单退款成功，创建新单失败" + "\n请求参数: \n" + JSON.toJSON(kaiDeOrderReqEntity) + "\n请求结果: \n" + JSON.toJSON(deResp);
        DingDingMsg dingDingMsg = new DingDingMsg();
        Map<String,Object> param = new HashMap<>();
        param.put("content",msg);
        dingDingMsg.setText(param);
        dingDingMsg.setMsgtype("text");
        Response<DingDingResponse> dingDingResponse = dingDingRemoteHttpApi.pushMsg(dingDingMsg).execute();
        log.info("凯德订单推送消息返回结果：{}",dingDingResponse.body());
    }

    private ResponseResult checkNewMemberWithRefund(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef, CashierMallOrderLog cashierMallOrderLog) {
        ResponseResult commonResponse = new ResponseResult();
        CashierMallOrderLog logs = cashierMallOrderLogMapper.selectOrderLogByOrderNumAndExchangeId(orderRefundReq.getOrdNum(),orderRefundReq.getOrdNum());
        if(logs != null){
            BaseOrderResponseEntity baseOrderResponse = new BaseOrderResponseEntity();
            baseOrderResponse.setOrdNum(orderRefundReq.getOrdNum());
            baseOrderResponse.setExchangeId(orderRefundReq.getRefundNo());
            baseOrderResponse.setExchangeNo(orderRefundReq.getRefundNo());
            commonResponse.setData(baseOrderResponse);
            commonResponse.setCode(200);
            commonResponse.setMsg("会员不存在");

            cashierMallOrderLog.setIsExecute("Y");
            cashierMallOrderLog.setRemark("会员不存在");
            cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            return commonResponse;
        }
        MemberRegisterReq memberRegisterReq = new MemberRegisterReq();
        memberRegisterReq.setMobile(orderRefundReq.getMemberTel());
        ResponseResult<KaiDeMemberRespEntity> result = kaiDeMemberStrategyService.getMemberInfo(memberRegisterReq, cashierMallAssetStoreRef);
        if(result.getCode() != 200){
            commonResponse.setCode(999);
            commonResponse.setMsg(result.getMsg());
            cashierMallOrderLog.setRemark(result.getMsg());
            cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
        }else {
            commonResponse.setData(result.getData().getMemberNo());
            commonResponse.setCode(0);
        }
        return commonResponse;
    }

    private static KaiDeOrderReqEntity initKaiDeOrderReqEntity(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef, BigDecimal amount) {
        KaiDeOrderReqEntity kaiDeOrderReqEntity = new KaiDeOrderReqEntity();
        kaiDeOrderReqEntity.setMemberNo(orderRefundReq.getOuterMemberId());
        kaiDeOrderReqEntity.setShop(cashierMallAssetStoreRef.getPlatformMallStoreId());
        String transTime = DateUtil.timeStamp3Date(orderRefundReq.getRefundTime(),null);
        kaiDeOrderReqEntity.setTransTime(transTime);
        kaiDeOrderReqEntity.setReceiptNo(orderRefundReq.getRefundNo());
        kaiDeOrderReqEntity.setAmount(amount);
        kaiDeOrderReqEntity.setMethodCode(cashierMallAssetStoreRef.getPlatformAppId());
        kaiDeOrderReqEntity.setAmountReal(amount);
        List<Payment> payments = new ArrayList<>();
        Payment payment = new Payment();
        payment.setAmount(amount);
        payment.setPaymentType("wechat");
        payments.add(payment);
        kaiDeOrderReqEntity.setPayments(payments);
        return kaiDeOrderReqEntity;
    }

    private CashierMallOrderLog recordNewOrderLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog logInfo = new CashierMallOrderLog();
        logInfo.setOrdNum(orderRefundReq.getRefundNo());
        logInfo.setExchangeId(orderRefundReq.getRefundNo());
        logInfo.setExchangeNo(orderRefundReq.getRefundNo());
        logInfo.setBjStoreId(orderRefundReq.getStoreId());
        logInfo.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        logInfo.setIsCallback("Y");
        logInfo.setIsExecute("Y");
        logInfo.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        logInfo.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        logInfo.setType(1);
        logInfo.setRefundAmount(BigDecimal.ZERO);
        cashierMallOrderLogMapper.insertEntity(logInfo);
        return logInfo;
    }

    private void doAllRefundBusiness(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef, ResponseResult commonResponse, CashierMallOrderLog cashierMallOrderLog) throws IOException {
        Map<String, String> header = new HashMap<>();
        header.put(FieldConstant.HEADER_SUBSCRIPTION_KEY, cashierMallAssetStoreRef.getPlatformPublicKey());
        header.put(FieldConstant.HEADER_TOKEN, cashierMallAssetStoreRef.getPlatformPrivateKey());
        KaiDeOrderRefundReqEntity req = new KaiDeOrderRefundReqEntity();
        req.setMemberNo(orderRefundReq.getOuterMemberId());
        req.setIdentifier(orderRefundReq.getExchangeNo());
        req.setMethodCode(cashierMallAssetStoreRef.getPlatformAppId());
        log.info("凯德订单整单退货退款调用远程接口参数：{}", JSON.toJSONString(req));
        Response<BaseKaiDeResp<KaiDeOrderRefundRespEntity>> execute = kaiDeRemoteHttpApi.orderRefund(header,req).execute();
        BaseKaiDeResp<KaiDeOrderRefundRespEntity> body = execute.body();
        if(body != null && body.getErrorCode() == 0){
            commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
            commonResponse.setMsg(body.getMessage());
            KaiDeOrderRefundRespEntity orderRefundRespEntity = body.getBody();
            BaseOrderResponseEntity baseOrderResponse = new BaseOrderResponseEntity();
            baseOrderResponse.setOrdNum(orderRefundReq.getOrdNum());
            baseOrderResponse.setExchangeId(orderRefundRespEntity.getIdentifier());
            baseOrderResponse.setExchangeNo(orderRefundRespEntity.getIdentifier());
            commonResponse.setData(baseOrderResponse);
            // 更新返回的参数
            cashierMallOrderLog.setIsExecute("Y");
            cashierMallOrderLog.setUpdateExchangeId(orderRefundRespEntity.getIdentifier());
            cashierMallOrderLog.setRemark(ResultCodeEnum.SUCCESS.getMsg());
            cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
        }else {
            if(execute.errorBody() != null){
                String errorBody = execute.errorBody().string();
                BaseKaiDeResp baseKaiDeResp = JSONObject.parseObject(errorBody,BaseKaiDeResp.class);
                commonResponse.setCode(baseKaiDeResp.getErrorCode());
                commonResponse.setMsg("凯德订单整单全额退款异常：" + baseKaiDeResp.getMessage());
                commonResponse.setData(baseKaiDeResp.getBody());
                cashierMallOrderLog.setRemark(baseKaiDeResp.getMessage());
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            }
        }
    }

    @Override
    public ResponseResult prepareRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderRefundReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setExchangeNo(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setBjStoreId(orderRefundReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(2);
        cashierMallOrderLog.setRefundAmount(orderRefundReq.getRefundAmount());
        cashierMallOrderLogMapper.insertAddLog(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

}
