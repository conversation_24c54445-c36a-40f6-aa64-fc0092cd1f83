package com.jnby.mallasset.remote.xiexin.entity.vip;

import com.jnby.mallasset.remote.xiexin.entity.order.XiexinOrderSoapBody;
import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.NamespaceList;
import org.simpleframework.xml.Root;

@Data
@Root(name = "soap:Envelope" , strict = false)
@NamespaceList({
        @Namespace(reference = "http://www.w3.org/2001/XMLSchema-instance", prefix = "xsi"),
        @Namespace(reference = "http://www.w3.org/2001/XMLSchema", prefix = "xsd"),
        @Namespace(reference = "http://schemas.xmlsoap.org/soap/envelope/", prefix = "soap")
})
public class XiexinVipTagSoapEnvelope {

    @Element(name = "soap:Header", required = false)
    private XiexinVipTagSoapHeader header;

    @Element(name = "soap:Body", required = false)
    private XiexinVipTagSoapBody body;

}
