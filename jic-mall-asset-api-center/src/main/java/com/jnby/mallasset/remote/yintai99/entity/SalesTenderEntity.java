package com.jnby.mallasset.remote.yintai99.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SalesTenderEntity implements Serializable {
    @ApiModelProperty("本地貨幣編號")
    private String baseCurrencyCode;
    @ApiModelProperty("付款方式編號")
    private String tenderCode;
    @ApiModelProperty("付款金額")
    private BigDecimal payAmount;
    @ApiModelProperty("本位幣金額")
    private BigDecimal baseAmount;
    @ApiModelProperty("多收金額")
    private BigDecimal excessAmount;
    @ApiModelProperty("擴展參數")
    private String extendParameter;
}
