package com.jnby.mallasset.module.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jnby.mallasset.module.mapper.box.CashierPayConfigMapper;
import com.jnby.mallasset.module.mapper.box.CashierPaymentConfigMapper;
import com.jnby.mallasset.module.model.CashierPayConfig;
import com.jnby.mallasset.module.model.CashierPaymentConfig;
import com.jnby.mallasset.module.service.ICashierPayConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class CashierPayConfigServiceImpl extends ServiceImpl<CashierPayConfigMapper, CashierPayConfig> implements ICashierPayConfigService {

    @Resource
    private CashierPaymentConfigMapper cashierPaymentConfigMapper;
    private AtomicInteger ID = new AtomicInteger(1);

    public void migrateDataFromPaymentToPayConfig() {
        int pageNum = 1;
        int pageSize = 100;

        while (true) {
            // 开始分页
            PageHelper.startPage(pageNum, pageSize);
            // 执行查询
            List<CashierPaymentConfig> paymentConfigs = cashierPaymentConfigMapper.selectList(null);

            if (paymentConfigs == null || paymentConfigs.isEmpty()) {
                break;
            }

            // 转换并插入数据
            List<CashierPayConfig> payConfigs = convertToPayConfigs(paymentConfigs);
            this.saveBatch(payConfigs);

            pageNum++;
        }
    }


    private List<CashierPayConfig> convertToPayConfigs(List<CashierPaymentConfig> paymentConfigs) {
        List<CashierPayConfig> payConfigs = new ArrayList<>();

        for (CashierPaymentConfig paymentConfig : paymentConfigs) {
            log.info("原配置数据={}", JSON.toJSONString(paymentConfig));
            CashierPayConfig payConfig = new CashierPayConfig();
            payConfig.setIsDelete(paymentConfig.getIsDelete());
            payConfig.setCreateTime(paymentConfig.getCreateTime());
            payConfig.setUpdateTime(paymentConfig.getUpdateTime());
            payConfig.setBjStoreId(paymentConfig.getBjStoreId());
            payConfig.setStoreName(paymentConfig.getStoreName());
            payConfig.setPayChannel(paymentConfig.getPayChannel());
            payConfig.setPayConfigId(paymentConfig.getPayConfigId());
            payConfig.setDeviceNumber(paymentConfig.getDeviceNumber());

            // 根据不同的 HAS_XXX 字段设置 BUSINESS_TYPE
            if (Boolean.TRUE.equals(paymentConfig.getHasWxmall())) {
                payConfig.setBusinessType(1); // 微商城
                payConfigs.add(copyObject(payConfig));
            }
            if (Boolean.TRUE.equals(paymentConfig.getHasPos())) {
                payConfig.setBusinessType(3); // POS+
                payConfigs.add(copyObject(payConfig));
            }
            if (Boolean.TRUE.equals(paymentConfig.getHasPosOnline())) {
                payConfig.setBusinessType(5); // POS+ 线上
                payConfigs.add(copyObject(payConfig));
            }
            if (Boolean.TRUE.equals(paymentConfig.getHasBox())) {
                payConfig.setBusinessType(2); // BOX
                payConfigs.add(copyObject(payConfig));
            }
            if (Boolean.TRUE.equals(paymentConfig.getHasStoreCard())) {
                payConfig.setBusinessType(6); // 储值卡
                payConfigs.add(copyObject(payConfig));
            }
        }
        log.info("转换后的数据={}", JSON.toJSONString(payConfigs));
        return payConfigs;
    }

    // 深拷贝方法
    private CashierPayConfig copyObject(CashierPayConfig source) {
        CashierPayConfig target = new CashierPayConfig();
        target.setId(String.valueOf(ID.getAndIncrement()));
        target.setIsDelete(source.getIsDelete());
        target.setCreateTime(source.getCreateTime());
        target.setUpdateTime(source.getUpdateTime());
        target.setBjStoreId(source.getBjStoreId());
        target.setStoreName(source.getStoreName());
        target.setPayChannel(source.getPayChannel());
        target.setPayConfigId(source.getPayConfigId());
        target.setDeviceNumber(source.getDeviceNumber());
        target.setBusinessType(source.getBusinessType());
        return target;
    }
}
