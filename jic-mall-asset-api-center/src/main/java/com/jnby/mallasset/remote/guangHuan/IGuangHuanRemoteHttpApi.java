package com.jnby.mallasset.remote.guangHuan;


import com.jnby.mallasset.remote.guangHuan.entity.*;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.Map;


/**
 * 重庆光环
 */
public interface IGuangHuanRemoteHttpApi {


    /**
     * 功能描述: 查询会员
     * 使用场景:
     *
     * @param headers
     * @param id
     * @return retrofit2.Call<com.jnby.mallasset.remote.drc.entity.BaseDrcResp<com.jnby.mallasset.remote.drc.entity.DrcMemberRegisterRespEntity>>
     * <AUTHOR>
     * @date 2024/7/24 13:31
     */
    @GET("/inf/api/mbr/v1/member/{id}")
    Call<BaseGuangHuanResp<MemberRespEntity>> memberInfo(@HeaderMap Map<String, String> headers, @Path("id") String id);

    /**
     * 功能描述: 查询会员
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.drc.entity.BaseDrcResp<com.jnby.mallasset.remote.drc.entity.DrcMemberRegisterRespEntity>>
     * <AUTHOR>
     * @date 2024/7/24 13:31
     */
    @POST("/inf/api/mbr/v1/member/wechat/save")
    Call<BaseGuangHuanResp<String>> memberOpenCard(@HeaderMap Map<String, String> headers, @Body MemberReqEntity req);

    /**
     * 功能描述: 订单同步、售后
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.guangHuan.entity.BaseGuangHuanResp<com.jnby.mallasset.remote.guangHuan.entity.SaveTradeResp>>
     * <AUTHOR>
     * @date 2024/8/19 11:04
     */
    @POST("/inf/api/mbr/v1/trade/saveTrade")
    Call<BaseGuangHuanResp<SaveTradeResp>> saveTrade(@HeaderMap Map<String, String> headers, @Body SaveTradeParam req);

}
