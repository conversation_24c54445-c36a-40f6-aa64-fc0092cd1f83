package com.jnby.mallasset.strategy.platform.dyc;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.dto.req.order.PayItem;
import com.jnby.mallasset.dto.req.order.ProductItem;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.remote.dayuecheng.IDayuechengPosRemoteHttpApi;
import com.jnby.mallasset.remote.dayuecheng.entity.*;
import com.jnby.mallasset.remote.mallcoo.entity.MemberQueryRespEntity;
import com.jnby.mallasset.remote.mallcoo.entity.PointsPlusByOrderRespEntity;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 凯德订单平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.DA_YUE_CHENG, category = PlatformCategoryTypeEnum.ORDER)
@Slf4j
@AllArgsConstructor
public class DycOrderStrategyService extends AbstractOrderService {

    private IDayuechengPosRemoteHttpApi dayuechengPosRemoteHttpApi;
    private CashierMallOrderLogMapper cashierMallOrderLogMapper;
    private DycMemberStrategyService dycMemberStrategyService;

    @Override
    public ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("大悦城订单同步输入参数：{}", JSON.toJSONString(orderConsumeReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderConsumeReq, cashierMallAssetStoreRef);
            MemberRegisterReq memberRegisterReq = new MemberRegisterReq();
            memberRegisterReq.setMobile(orderConsumeReq.getMemberTel());
            memberRegisterReq.setStoreId(orderConsumeReq.getStoreId());
            // 给会员开卡
            ResponseResult<CommonMemberResponse> result = dycMemberStrategyService.openCard(memberRegisterReq,cashierMallAssetStoreRef);
            String memberCode = null;
            if(result.getCode() == 200){
                CommonMemberResponse commonMemberResponse = result.getData();
                //
                memberCode = commonMemberResponse.getMemberCardNo();
            }

            String platformMallId = cashierMallAssetStoreRef.getPlatformMallId();
            String platformMallStoreId = cashierMallAssetStoreRef.getPlatformMallStoreId();
            String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
            String integralSkuCode = cashierMallAssetStoreRef.getIntegralSkuCode();
            String memberTel = orderConsumeReq.getMemberTel();
            String ordNum = orderConsumeReq.getOrdNum();
            // 组装请求对象
            DycOrderPosReqEntity req = new DycOrderPosReqEntity();
            String[] items = integralSkuCode.split(",");
            String[] cashierIds = items[0].split("#");
            String cashierId = cashierIds[1];
            String[] tillIds = items[1].split("#");
            String tillId = tillIds[1];
            String[] itemCodes = items[2].split("#");
            String itemCode = itemCodes[1];
            String[] paymentMethods = items[3].split("#");
            String paymentMethod = paymentMethods[1];
            String[] sources = items[4].split("#");
            String source = sources[1];
            String[] projectCodes = items[5].split("#");
            String projectCode = projectCodes[1];
            String orderId = source.concat("-").concat(projectCode).concat(ordNum);

            req.setCashierId(cashierId);
            req.setMobile(memberTel);
            req.setMall(platformMallId);
            req.setOrderId(orderId);
            req.setStore(platformMallStoreId);
            req.setSource(source);
            req.setTillId(tillId);
            req.setComments("");
            req.setType("SALE");
            req.setUploadTime("");
            String dateTime = DateUtil.formatToStr(new Date(),DateUtil.DATEFORMATE_YYYYMMDDHHMMSS);
            String date = dateTime.substring(0,8);
            String time = dateTime.substring(8);
            req.setTradeDate(date);
            req.setTradeTime(time);
            // 商品明细
            List<ProductItem> productItemList = orderConsumeReq.getProductItemList();
            List<ItemProduct> itemList = new ArrayList<>();
            productItemList.forEach(productItem -> {
                ItemProduct itemProduct = new ItemProduct();
                itemProduct.setItemCode(itemCode);
                itemProduct.setPrice(productItem.getItemPrice());
                itemProduct.setQuantity(productItem.getItemQty());
                itemList.add(itemProduct);
            });
            req.setItemList(itemList);
            // 付款明细
            List<PayItem> payItemList = orderConsumeReq.getPayItemList();
            BigDecimal discountAmt = BigDecimal.ZERO;
            BigDecimal payAmt = BigDecimal.ZERO;
            for (PayItem payItem : payItemList) {
                // 微信
                if(payItem.getId() == 62 || payItem.getId() == 231){
                    payAmt = payItem.getPayamount();
                }
            }
            List<ItemPay> payList = new ArrayList<>();
            ItemPay itemPay = new ItemPay();
            itemPay.setPayAmt(payAmt);
            itemPay.setDiscountAmt(discountAmt);
            itemPay.setValue(payAmt);
            itemPay.setPaymentMethod(paymentMethod);
            itemPay.setTime(dateTime);
            payList.add(itemPay);
            req.setPayList(payList);
            req.setTotalAmt(orderConsumeReq.getOrdAmount());
            req.setMemberNo(memberCode);

            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization",platformPrivateKey);
            log.info("大悦城订单消费调用远程接口参数：{}", JSON.toJSONString(req));
            Response<DycOrderPosRespEntity> execute = dayuechengPosRemoteHttpApi.orderInfo(headers,req).execute();
            DycOrderPosRespEntity dycOrderPosRespEntity = execute.body();
            log.info("大悦城订单消费返回参数：{}", JSON.toJSONString(dycOrderPosRespEntity));
            DycOrderPosRespHeader header = dycOrderPosRespEntity.getHeader();
            if(header != null && header.getErrcode().equals("0000")){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                DycOrderResponse dycOrderResponse = new DycOrderResponse();
                dycOrderResponse.setOrdNum(ordNum);
                DycOrderPosRespBody body = dycOrderPosRespEntity.getBody();
                dycOrderResponse.setExchangeId(body.getOrderId());
                dycOrderResponse.setExchangeNo(ordNum);
                commonResponse.setData(dycOrderResponse);

                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLog.setUpdateExchangeId(body.getOrderId());
                cashierMallOrderLog.setOuterOrderId(orderId);
                cashierMallOrderLog.setMemberId(memberCode);
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            }else {
                commonResponse.setCode(Integer.parseInt(header.getErrcode()));
                commonResponse.setMsg(header.getErrmsg());
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 大悦城订单消费异常",orderConsumeReq.getOrdNum(), e);
        }
        return commonResponse;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeNo(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setBjStoreId(orderConsumeReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(1);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult pushRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("大悦城订单退货退款输入参数：{}", JSON.toJSONString(orderRefundReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderRefundReq, cashierMallAssetStoreRef);
            String platformMallId = cashierMallAssetStoreRef.getPlatformMallId();
            String platformMallStoreId = cashierMallAssetStoreRef.getPlatformMallStoreId();
            String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
            String integralSkuCode = cashierMallAssetStoreRef.getIntegralSkuCode();
            String memberTel = orderRefundReq.getMemberTel();
            String refundNo = orderRefundReq.getRefundNo();

            // 查询会员积分是否大于0
            MemberRegisterReq memberRegisterReq = new MemberRegisterReq();
            memberRegisterReq.setMobile(orderRefundReq.getMemberTel());
            String memberCode = null;
            // 查询原单的会员信息
            CashierMallOrderLog orderLog = cashierMallOrderLogMapper.selectOrderLogByOrderNumAndExchangeId(orderRefundReq.getOrdNum(),orderRefundReq.getExchangeId());
            if(orderLog == null){
                commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                commonResponse.setMsg("联域平台单号：" + orderRefundReq.getOrdNum() + "售后找不到原单");
                return commonResponse;
            }
            // 原单存在会员信息的，售后单必须携带会员信息
            if(orderLog.getMemberId() != null){
                ResponseResult<MemberQueryRespEntity> result = dycMemberStrategyService.getMemberInfo(memberRegisterReq,cashierMallAssetStoreRef);
                if(result.getCode() == 200){
                    MemberQueryRespEntity dycMemberQueryRespEntity = result.getData();
                    if(dycMemberQueryRespEntity.getScore() != null && dycMemberQueryRespEntity.getScore() <= 0){
                        commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                        commonResponse.setMsg("会员积分小于等于0，不可退");
                        return commonResponse;
                    }
                    memberCode = dycMemberQueryRespEntity.getMallCardNo();
                }
            }

            // 组装请求对象
            DycOrderPosReqEntity req = new DycOrderPosReqEntity();

            String[] items = integralSkuCode.split(",");
            String[] cashierIds = items[0].split("#");
            String cashierId = cashierIds[1];
            String[] tillIds = items[1].split("#");
            String tillId = tillIds[1];
            String[] itemCodes = items[2].split("#");
            String itemCode = itemCodes[1];
            String[] paymentMethods = items[3].split("#");
            String paymentMethod = paymentMethods[1];
            String[] sources = items[4].split("#");
            String source = sources[1];
            String[] projectCodes = items[5].split("#");
            String projectCode = projectCodes[1];

            req.setCashierId(cashierId);
            req.setMobile(memberTel);
            req.setMall(platformMallId);
            String orderId = source.concat("-").concat(projectCode).concat(refundNo);
            req.setOrderId(orderId);
            req.setStore(platformMallStoreId);
            req.setSource(source);
            req.setTillId(tillId);
            req.setComments("");
            req.setType("ONLINEREFUND");
            req.setUploadTime("");
            String dateTime = DateUtil.formatToStr(new Date(),DateUtil.DATEFORMATE_YYYYMMDDHHMMSS);
            String date = dateTime.substring(0,8);
            String time = dateTime.substring(8);
            req.setTradeDate(date);
            req.setTradeTime(time);
            // 商品明细
            List<ProductItem> productItemList = orderRefundReq.getProductItemList();
            List<ItemProduct> itemList = new ArrayList<>();
            productItemList.forEach(productItem -> {
                ItemProduct itemProduct = new ItemProduct();
                itemProduct.setItemCode(itemCode);
                // 单价负数
                itemProduct.setPrice(productItem.getItemPrice().divide(new BigDecimal(productItem.getItemQty()),4,RoundingMode.HALF_UP));
                itemProduct.setQuantity(-productItem.getItemQty());
                itemList.add(itemProduct);
            });
            req.setItemList(itemList);
            // 付款明细
            List<PayItem> payItemList = orderRefundReq.getPayItemList();
            BigDecimal discountAmt = BigDecimal.ZERO;
            BigDecimal payAmt = BigDecimal.ZERO;
            for (PayItem payItem : payItemList) {
                // 微信
                if(payItem.getId() == 62 || payItem.getId() == 231){
                    payAmt = payItem.getPayamount();
                }
            }
            List<ItemPay> payList = new ArrayList<>();
            ItemPay itemPay = new ItemPay();
            itemPay.setPayAmt(payAmt);
            itemPay.setDiscountAmt(discountAmt);
            itemPay.setValue(payAmt);
            itemPay.setPaymentMethod(paymentMethod);
            itemPay.setTime(dateTime);
            payList.add(itemPay);
            req.setPayList(payList);

            req.setTotalAmt(orderRefundReq.getRefundAmount().multiply(new BigDecimal(-1)));
            req.setMemberNo(memberCode);
            String ordId = source.concat("-").concat(projectCode).concat(orderRefundReq.getOrdNum());
            req.setRefOrderId(ordId);

            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization",platformPrivateKey);
            log.info("大悦城订单消费调用远程接口参数：{}", JSON.toJSONString(req));
            Response<DycOrderPosRespEntity> execute = dayuechengPosRemoteHttpApi.orderInfo(headers,req).execute();
            DycOrderPosRespEntity dycOrderPosRespEntity = execute.body();
            log.info("大悦城订单消费返回参数：{}", JSON.toJSONString(dycOrderPosRespEntity));
            DycOrderPosRespHeader header = dycOrderPosRespEntity.getHeader();
            if(header != null && header.getErrcode().equals("0000")){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                DycOrderResponse dycOrderResponse = new DycOrderResponse();
                dycOrderResponse.setOrdNum(refundNo);
                DycOrderPosRespBody body = dycOrderPosRespEntity.getBody();
                dycOrderResponse.setExchangeId(body.getOrderId());
                dycOrderResponse.setExchangeNo(refundNo);
                commonResponse.setData(dycOrderResponse);

                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLog.setUpdateExchangeId(body.getOrderId());
                cashierMallOrderLog.setOuterOrderId(orderId);
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            }else {
                commonResponse.setCode(Integer.parseInt(header.getErrcode()));
                commonResponse.setMsg(header.getErrmsg());
            }

        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 大悦城订单退货退款异常",orderRefundReq.getRefundNo(), e);
        }
        return commonResponse;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderRefundReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setExchangeNo(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setBjStoreId(orderRefundReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(2);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult prepareRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

}
