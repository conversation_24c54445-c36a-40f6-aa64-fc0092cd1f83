package com.jnby.mallasset.remote.kaide.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DrcMemberRegisterReqEntity.java
 * 文件简介: 会员请求
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class KaiDeMemberReqEntity implements Serializable {
    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "身份证号")
    private String NRIC;
    @ApiModelProperty(value = "会员卡号")
    private String memberNo;
}
