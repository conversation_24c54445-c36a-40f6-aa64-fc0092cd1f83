package com.jnby.mallasset.remote.wanke.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BaseWanKeResp<T> implements Serializable {
    @ApiModelProperty(value = "结果")
    private int status;
    @ApiModelProperty(value = "返回消息")
    private String message;
    @ApiModelProperty(value = "返回时间")
    private T data;

    public boolean isSuccess(){
        if (this.status == 200) {
            return true;
        }
        return false;
    }
}
