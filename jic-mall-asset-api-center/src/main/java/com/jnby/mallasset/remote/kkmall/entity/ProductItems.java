package com.jnby.mallasset.remote.kkmall.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductItems {
    @ApiModelProperty(value = "商品编号")
    private String code;
    @ApiModelProperty(value = "数量")
    private int quantity;
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;
    @ApiModelProperty(value = "是否允许积分 1 是 0 否")
    private int canGetPoint;
}
