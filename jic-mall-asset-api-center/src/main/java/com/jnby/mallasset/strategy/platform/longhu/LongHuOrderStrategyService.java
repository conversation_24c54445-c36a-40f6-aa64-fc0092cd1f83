package com.jnby.mallasset.strategy.platform.longhu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.dto.req.order.ProductItem;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.remote.dayuecheng.entity.DycOrderResponse;
import com.jnby.mallasset.remote.longhu.ILongHuRemoteHttpApi;
import com.jnby.mallasset.remote.longhu.entity.*;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.JsonUtil;
import com.jnby.mallasset.util.longhu.LongHuEncryptUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.LONG_HU, category = PlatformCategoryTypeEnum.ORDER)
@Slf4j
@AllArgsConstructor
public class LongHuOrderStrategyService extends AbstractOrderService {

    ILongHuRemoteHttpApi longHuRemoteHttpApi;
    private CashierMallOrderLogMapper cashierMallOrderLogMapper;

    private static final List<Integer> LONG_HU_GRANT_SUCCESS_STATUS = Arrays.asList(ResultCodeEnum.SUCCESS.getCode(), SystemErrorEnum.LONG_HU_NOT_REG_WAIT_GRANT.getErrorCode());

    @Override
    public ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("龙湖订单同步龙湖积分输入参数：{}", JSON.toJSONString(orderConsumeReq));
        ResponseResult commonResponse = new ResponseResult();
        try {
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderConsumeReq, cashierMallAssetStoreRef);

            String memberTel = orderConsumeReq.getMemberTel();
            String ordNum = orderConsumeReq.getOrdNum();

            Map<String, String> headers = new HashMap<>();
            LongHuEnterpriseGrantReq longHuReq = new LongHuEnterpriseGrantReq();
            longHuReq.setRequestNo(ordNum);
            longHuReq.setPhoneNo(memberTel);
            // 龙珠金额 20/1
            BigDecimal grantAmount = orderConsumeReq.getOrdAmount().multiply(new BigDecimal("0.05")).setScale(1, RoundingMode.HALF_UP);
            longHuReq.setGrantAmount(grantAmount);
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(longHuReq), JSONObject.class);
            //加密请求参数
            LongHuEnterpriseEncryptResp encryptParams = handleEncryptRequest(cashierMallAssetStoreRef, jsonObject, headers);

            Response<LongHuEnterpriseEncryptResp> execute = longHuRemoteHttpApi.enterpriseGrant(headers, encryptParams).execute();
            log.info("龙湖订单同步积分返回结果 code: {} message: {} body: {}", execute.code(), execute.message(), JSON.toJSONString(execute.body()));
            if (execute.code() != 200) {
                commonResponse.setCode(SystemErrorEnum.LONG_HU_TIME_OUT_ERROR.getErrorCode());
                commonResponse.setMsg(execute.message());
                return commonResponse;
            }
            LongHuEnterpriseEncryptResp encryptResp = execute.body();
            if (encryptResp == null) {
                commonResponse.setCode(SystemErrorEnum.LONG_HU_TIME_OUT_ERROR.getErrorCode());
                commonResponse.setMsg(SystemErrorEnum.LONG_HU_TIME_OUT_ERROR.getErrorMsg());
                log.error("{} 龙湖订单同步积分超时", orderConsumeReq.getOrdNum());
                return commonResponse;
            }
            if (encryptResp.getCode() != null && "".equals(encryptResp.getCode())) {
                commonResponse.setCode(SystemErrorEnum.LONG_HU_UNKNOWN_ERROR.getErrorCode());
                commonResponse.setMsg(encryptResp.getMsg());
                log.error("{} 龙湖订单同步积分异常", orderConsumeReq.getOrdNum());
                return commonResponse;
            }

            LongHuEnterpriseEncryptResp decryptResp = handleDecryptResponse(cashierMallAssetStoreRef, encryptResp);
            log.info("龙湖订单同步积分返回解密结果：{}", JSON.toJSONString(decryptResp));
            handleLongHuResponse(decryptResp, commonResponse);
            if (LONG_HU_GRANT_SUCCESS_STATUS.contains(commonResponse.getCode())) {
                //龙湖积分发放成功
                DycOrderResponse dycOrderResponse = new DycOrderResponse();
                dycOrderResponse.setOrdNum(ordNum);
                dycOrderResponse.setExchangeId(ordNum);
                dycOrderResponse.setExchangeNo(ordNum);
                commonResponse.setData(dycOrderResponse);
                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLog.setQty(orderConsumeReq.getTotQty());
                cashierMallOrderLog.setRefundAmount(grantAmount);
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                return commonResponse;
            } else if (SystemErrorEnum.LONG_HU_GRANT_PROCESS.getErrorCode() == commonResponse.getCode()) {
                //发放处理中，调用查询接口查询最终结果
                ResponseResult queryResponseResult = enterpriseGrantQuery(ordNum, cashierMallAssetStoreRef);
                if (LONG_HU_GRANT_SUCCESS_STATUS.contains(queryResponseResult.getCode())) {
                    //龙湖积分发放成功
                    DycOrderResponse dycOrderResponse = new DycOrderResponse();
                    dycOrderResponse.setOrdNum(ordNum);
                    dycOrderResponse.setExchangeId(ordNum);
                    dycOrderResponse.setExchangeNo(ordNum);
                    commonResponse.setData(dycOrderResponse);
                    cashierMallOrderLog.setIsExecute("Y");
                    cashierMallOrderLog.setQty(orderConsumeReq.getTotQty());
                    cashierMallOrderLog.setRefundAmount(grantAmount);
                    cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    return commonResponse;
                }
            } else {
                log.error("龙湖订单同步积分失败 订单 {}, 原因 {}", orderConsumeReq.getOrdNum(), JsonUtil.toJson(commonResponse));
                return commonResponse;
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 龙湖订单同步积分消费异常",orderConsumeReq.getOrdNum(), e);
        }
        return commonResponse;
    }

    @Override
    public ResponseResult pushRefundOrder(OrderRefundReq OrderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("龙湖售后订单同步扣减龙湖积分输入参数：{}", JSON.toJSONString(OrderRefundReq));
        ResponseResult commonResponse = new ResponseResult();
        try {
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(OrderRefundReq, cashierMallAssetStoreRef);

            String ordNum = OrderRefundReq.getOrdNum();
            String refundNo = OrderRefundReq.getRefundNo();

            CashierMallOrderLog firstRefundOrder = cashierMallOrderLogMapper.queryFirstRefundOrderByOrdNum(ordNum);
            if (firstRefundOrder != null && Objects.equals(firstRefundOrder.getMemberType(),2)) {
                log.info("未注册会员售后第二次不用推送");
                //存在未注册待发放的订单，已整单撤回。后续售后直接返回成功
                DycOrderResponse dycOrderResponse = new DycOrderResponse();
                dycOrderResponse.setOrdNum(ordNum);
                dycOrderResponse.setExchangeId(refundNo);
                dycOrderResponse.setExchangeNo(refundNo);
                commonResponse.setData(dycOrderResponse);

                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                return commonResponse;
            }

            boolean orderWaitGrant = false;
            //调用查询接口查询原单状态
            ResponseResult queryOrderResult = enterpriseGrantQuery(ordNum, cashierMallAssetStoreRef);
            if (SystemErrorEnum.LONG_HU_NOT_REG_WAIT_GRANT.getErrorCode() == queryOrderResult.getCode()) {
                //订单当前为待发放状态，售后为整单撤回
                orderWaitGrant = true;
            }

            Map<String, String> headers = new HashMap<>();
            LongHuEnterpriseGrantRefundReq longHuReq = new LongHuEnterpriseGrantRefundReq();
            longHuReq.setRequestNo(refundNo);
            longHuReq.setOrigRequestNo(ordNum);
            if (orderWaitGrant) {
                longHuReq.setActionType(2);
            } else {
                longHuReq.setActionType(1);
            }

            BigDecimal grantAmount = BigDecimal.ZERO;
            int refundQty = 0;
            if(orderWaitGrant){
                grantAmount = OrderRefundReq.getRefundAmount().multiply(new BigDecimal("0.05")).setScale(1, RoundingMode.HALF_UP);
            }else{
                // 如果不是未注册会员退款，处理部分退尾差逻辑
                CashierMallOrderLog orderLog = cashierMallOrderLogMapper.selectOrderLogByOrderNumAndExchangeId(OrderRefundReq.getOrdNum(),OrderRefundReq.getOrdNum());
                List<ProductItem> productItemList = OrderRefundReq.getProductItemList();
                int totalQty = cashierMallOrderLogMapper.selectSumRefundProductQty(OrderRefundReq.getOrdNum());
                for (ProductItem productItem : productItemList) {
                    refundQty += Math.abs(productItem.getItemQty());
                }
                // 与原单商品数量比较，判断是否整单退或部分退
                if(refundQty == orderLog.getQty()){
                    grantAmount = orderLog.getRefundAmount();
                }else if(refundQty < orderLog.getQty()){
                    totalQty = totalQty + refundQty;
                    if(totalQty < orderLog.getQty()){
                        grantAmount = OrderRefundReq.getRefundAmount().multiply(new BigDecimal("0.05")).setScale(1, RoundingMode.HALF_UP);
                    }else{
                        // 处理尾差
                        CashierMallOrderLog mallOrderLog = cashierMallOrderLogMapper.selectSumRefundAmount(OrderRefundReq.getOrdNum());
                        BigDecimal refundAmount = mallOrderLog.getRefundAmount();
                        grantAmount = orderLog.getRefundAmount().subtract(refundAmount);
                        if(grantAmount.compareTo(BigDecimal.ZERO) < 0){
                            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                            commonResponse.setMsg("订单金额与实际不符");
                            return commonResponse;
                        }
                        // 控制误差
                        if(refundAmount.add(grantAmount).subtract(orderLog.getRefundAmount()).compareTo(new BigDecimal(1)) >= 0){
                            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                            commonResponse.setMsg("订单金额与实际不符");
                            return commonResponse;
                        }

                    }
                }
            }
            longHuReq.setRefundAmount(grantAmount);
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(longHuReq), JSONObject.class);
            LongHuEnterpriseEncryptResp encryptParams = handleEncryptRequest(cashierMallAssetStoreRef, jsonObject, headers);

            Response<LongHuEnterpriseEncryptResp> execute = longHuRemoteHttpApi.enterpriseGrantRefund(headers, encryptParams).execute();
            log.info("龙湖售后订单同步积分响应结果 code: {} message: {} body: {}", execute.code(), execute.message(), JSON.toJSONString(execute.body()));
            if (execute.code() != 200) {
                commonResponse.setCode(SystemErrorEnum.LONG_HU_TIME_OUT_ERROR.getErrorCode());
                commonResponse.setMsg(execute.message());
                return commonResponse;
            }
            LongHuEnterpriseEncryptResp encryptResp = execute.body();
            if (encryptResp == null) {
                commonResponse.setCode(SystemErrorEnum.LONG_HU_TIME_OUT_ERROR.getErrorCode());
                commonResponse.setMsg(SystemErrorEnum.LONG_HU_TIME_OUT_ERROR.getErrorMsg());
                log.error("{} 龙湖售后订单同步积分超时", refundNo);
                return commonResponse;
            }
            if (encryptResp.getCode() != null && "".equals(encryptResp.getCode())) {
                commonResponse.setCode(SystemErrorEnum.LONG_HU_UNKNOWN_ERROR.getErrorCode());
                commonResponse.setMsg(encryptResp.getMsg());
                log.error("{} 龙湖售后订单同步积分异常", refundNo);
                return commonResponse;
            }

            LongHuEnterpriseEncryptResp decryptResp = handleDecryptResponse(cashierMallAssetStoreRef, encryptResp);
            log.info("龙湖售后订单同步积分返回解密结果：{}", JSON.toJSONString(decryptResp));
            handleLongHuResponse(decryptResp, commonResponse);
            if (ResultCodeEnum.SUCCESS.getCode() == commonResponse.getCode()) {
                //龙湖积分发放成功
                DycOrderResponse dycOrderResponse = new DycOrderResponse();
                dycOrderResponse.setOrdNum(ordNum);
                dycOrderResponse.setExchangeId(refundNo);
                dycOrderResponse.setExchangeNo(refundNo);
                commonResponse.setData(dycOrderResponse);
                cashierMallOrderLog.setIsExecute("Y");
                if (orderWaitGrant) {
                    cashierMallOrderLog.setMemberType(2);
                }
                cashierMallOrderLog.setQty(refundQty);
                cashierMallOrderLog.setRefundAmount(grantAmount);
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
                return commonResponse;
            } else if (SystemErrorEnum.LONG_HU_GRANT_PROCESS.getErrorCode() == commonResponse.getCode()) {
                //发放中，调用查询接口查询最终结果
                ResponseResult queryRefundResult = enterpriseGrantRefundQuery(refundNo, cashierMallAssetStoreRef);
                if (ResultCodeEnum.SUCCESS.getCode() == queryRefundResult.getCode()) {
                    //龙湖积分发放成功
                    DycOrderResponse dycOrderResponse = new DycOrderResponse();
                    dycOrderResponse.setOrdNum(ordNum);
                    dycOrderResponse.setExchangeId(refundNo);
                    dycOrderResponse.setExchangeNo(refundNo);
                    commonResponse.setData(dycOrderResponse);
                    cashierMallOrderLog.setIsExecute("Y");
                    if (orderWaitGrant) {
                        cashierMallOrderLog.setMemberType(2);
                    }
                    cashierMallOrderLog.setQty(refundQty);
                    cashierMallOrderLog.setRefundAmount(grantAmount);
                    cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
                    return commonResponse;
                }
            } else {
                log.error("售后龙湖订单同步积分结果失败 订单 {}, 原因 {}", refundNo, JsonUtil.toJson(commonResponse));
                return commonResponse;
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 大悦城订单消费异常",OrderRefundReq.getRefundNo(), e);
        }
        return commonResponse;
    }

    @Override
    public ResponseResult prepareRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }


    /**
     * 查询龙湖订单同步积分结果
     * @param ordNum
     * @param cashierMallAssetStoreRef
     * @return
     */
    private ResponseResult enterpriseGrantQuery(String ordNum, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("查询龙湖订单同步积分结果参数：{}", ordNum);
        ResponseResult commonResponse = new ResponseResult();
        try {
            Map<String, String> headers = new HashMap<>();
            LongHuEnterpriseGrantQueryReq longHuReq = new LongHuEnterpriseGrantQueryReq();
            longHuReq.setRequestNo(ordNum);
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(longHuReq), JSONObject.class);
            LongHuEnterpriseEncryptResp encryptParams = handleEncryptRequest(cashierMallAssetStoreRef, jsonObject, headers);

            Response<LongHuEnterpriseEncryptResp> execute = longHuRemoteHttpApi.enterpriseGrantQuery(headers, encryptParams).execute();
            log.info("查询龙湖订单同步积分结果加密结果 code: {} message: {} body: {}", execute.code(), execute.message(), JSON.toJSONString(execute.body()));
            if (execute.code() != 200) {
                commonResponse.setCode(SystemErrorEnum.LONG_HU_TIME_OUT_ERROR.getErrorCode());
                commonResponse.setMsg(execute.message());
                return commonResponse;
            }
            LongHuEnterpriseEncryptResp encryptResp = execute.body();
            if (encryptResp == null) {
                commonResponse.setCode(SystemErrorEnum.LONG_HU_TIME_OUT_ERROR.getErrorCode());
                commonResponse.setMsg(SystemErrorEnum.LONG_HU_TIME_OUT_ERROR.getErrorMsg());
                log.error("{} 查询龙湖订单同步积分结果超时", ordNum);
                return commonResponse;
            }
            if (encryptResp.getCode() != null && "".equals(encryptResp.getCode())) {
                commonResponse.setCode(SystemErrorEnum.LONG_HU_UNKNOWN_ERROR.getErrorCode());
                commonResponse.setMsg(encryptResp.getMsg());
                log.error("{} 查询龙湖订单同步积分结果异常", ordNum);
                return commonResponse;
            }

            LongHuEnterpriseEncryptResp decryptResp = handleDecryptResponse(cashierMallAssetStoreRef, encryptResp);
            log.info("查询龙湖订单同步积分结果返回解密结果：{}", JSON.toJSONString(decryptResp));

            handleLongHuResponse(decryptResp, commonResponse);
            if (LONG_HU_GRANT_SUCCESS_STATUS.contains(commonResponse.getCode())) {
                return commonResponse;
            } else {
                log.error("查询龙湖订单同步积分结果失败 订单 {}, 原因 {}", ordNum, JsonUtil.toJson(commonResponse));
                return commonResponse;
            }
        } catch (Exception e) {
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 查询龙湖订单同步积分结果异常", ordNum, e);
        }
        return commonResponse;
    }



    /**
     * 查询龙湖订单同步积分结果
     * @param refundNo
     * @param cashierMallAssetStoreRef
     * @return
     */
    private ResponseResult enterpriseGrantRefundQuery(String refundNo, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("查询龙湖售后订单同步积分结果参数：{}", refundNo);
        ResponseResult commonResponse = new ResponseResult();
        try {
            Map<String, String> headers = new HashMap<>();
            LongHuEnterpriseGrantQueryReq longHuReq = new LongHuEnterpriseGrantQueryReq();
            longHuReq.setRequestNo(refundNo);
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(longHuReq), JSONObject.class);
            LongHuEnterpriseEncryptResp encryptParams = handleEncryptRequest(cashierMallAssetStoreRef, jsonObject, headers);

            Response<LongHuEnterpriseEncryptResp> execute = longHuRemoteHttpApi.enterpriseGrantRefundQuery(headers, encryptParams).execute();
            log.info("查询龙湖售后订单同步积分响应 code: {} message: {} body: {}", execute.code(), execute.message(), JSON.toJSONString(execute.body()));
            if (execute.code() != 200) {
                commonResponse.setCode(SystemErrorEnum.LONG_HU_TIME_OUT_ERROR.getErrorCode());
                commonResponse.setMsg(execute.message());
                return commonResponse;
            }
            LongHuEnterpriseEncryptResp encryptResp = execute.body();
            if (encryptResp == null) {
                commonResponse.setCode(SystemErrorEnum.LONG_HU_TIME_OUT_ERROR.getErrorCode());
                commonResponse.setMsg(SystemErrorEnum.LONG_HU_TIME_OUT_ERROR.getErrorMsg());
                log.error("{} 查询龙湖售后订单同步积分结果超时", refundNo);
                return commonResponse;
            }
            if (encryptResp.getCode() != null && "".equals(encryptResp.getCode())) {
                commonResponse.setCode(SystemErrorEnum.LONG_HU_UNKNOWN_ERROR.getErrorCode());
                commonResponse.setMsg(encryptResp.getMsg());
                log.error("{} 查询龙湖售后订单同步积分结果异常", refundNo);
                return commonResponse;
            }

            LongHuEnterpriseEncryptResp decryptResp = handleDecryptResponse(cashierMallAssetStoreRef, encryptResp);
            log.info("查询龙湖售后订单同步积分结果返回解密结果：{}", JSON.toJSONString(decryptResp));

            handleLongHuResponse(decryptResp, commonResponse);
            if (ResultCodeEnum.SUCCESS.getCode() == commonResponse.getCode()) {
                return commonResponse;
            } else {
                log.error("查询龙湖售后订单同步积分结果失败 订单 {}, 原因 {}", refundNo, JsonUtil.toJson(commonResponse));
                return commonResponse;
            }
        } catch (Exception e) {
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 查询龙湖售后订单同步积分结果异常", refundNo, e);
        }
        return commonResponse;
    }


    private void handleLongHuResponse(LongHuEnterpriseEncryptResp decryptResp, ResponseResult commonResponse) {
        if (decryptResp.getCode() != null && "0000".equals(decryptResp.getCode()) && decryptResp.getData() != null) {
            LongHuEnterpriseGrantResp data = decryptResp.getData();
            if (data.getStatus() == 1) {
                //龙湖积分发放成功 1-发放成功
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
            } else if (data.getStatus() == 3) {
                //3-未注册待发放(认为已发放)
                commonResponse.setCode(SystemErrorEnum.LONG_HU_NOT_REG_WAIT_GRANT.getErrorCode());
                commonResponse.setMsg(SystemErrorEnum.LONG_HU_NOT_REG_WAIT_GRANT.getErrorMsg());
            } else if (data.getStatus() == 0) {
                //发放中，调用查询接口查询最终结果
                commonResponse.setCode(SystemErrorEnum.LONG_HU_GRANT_PROCESS.getErrorCode());
                commonResponse.setMsg(SystemErrorEnum.LONG_HU_GRANT_PROCESS.getErrorMsg());
            } else {
                //发放失败
                commonResponse.setCode(SystemErrorEnum.LONG_HU_BUSINESS_ERROR.getErrorCode());
                commonResponse.setMsg(decryptResp.getMsg());
            }
        } else {
            commonResponse.setCode(SystemErrorEnum.LONG_HU_BUSINESS_ERROR.getErrorCode());
            commonResponse.setMsg(decryptResp.getMsg());
        }
    }

    /**
     * 加密请求参数
     * @param cashierMallAssetStoreRef
     * @param jsonObject
     * @param headers
     * @return
     * @throws Exception
     */
    private LongHuEnterpriseEncryptResp handleEncryptRequest(CashierMallAssetStoreRef cashierMallAssetStoreRef, JSONObject jsonObject, Map<String, String> headers) throws Exception {
        String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
        String platformPublicKey = cashierMallAssetStoreRef.getPlatformPublicKey();
        String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
        Map<String, String> platformParams = analysisPlatformParams(cashierMallAssetStoreRef.getIntegralSkuCode());
        String gaiaApiKey = platformParams.get("gaiaApiKey");
        String enterpriseNo = platformParams.get("enterpriseNo");
        String grantType = platformParams.get("grantType");

        headers.put("X-safety-source", "openApiSdk");
        headers.put("X-longfor-app-id", platformAppId);
        headers.put("X-Gaia-Api-Key", gaiaApiKey);

        jsonObject.put("enterpriseNo", enterpriseNo);
        jsonObject.put("grantType", grantType);
        jsonObject.put("appId", platformAppId);
        jsonObject.put("timestamp", System.currentTimeMillis());
        LongHuEnterpriseEncryptResp encrypt = LongHuEncryptUtil.encrypt(jsonObject, platformPublicKey, platformPrivateKey);
        encrypt.setAppId(platformAppId);
        return encrypt;
    }

    private Map<String, String> analysisPlatformParams(String platformParams) {
        Map<String, String> params = new HashMap<>();
        String[] items = platformParams.split(",");
        for (String item : items) {
            String[] split = item.split("#");
            String key = split[0];
            String value = split[1];
            params.put(key, value);
        }
        return params;
    }

    /**
     * 解密响应参数
     * @param cashierMallAssetStoreRef
     * @param encryptResp
     * @return
     * @throws Exception
     */
    private LongHuEnterpriseEncryptResp handleDecryptResponse(CashierMallAssetStoreRef cashierMallAssetStoreRef, LongHuEnterpriseEncryptResp encryptResp) throws Exception {
        String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
        String platformPublicKey = cashierMallAssetStoreRef.getPlatformPublicKey();
        return LongHuEncryptUtil.decrypt(encryptResp, platformPublicKey, platformPrivateKey);
    }


    private CashierMallOrderLog recordCashierMallOrderLog(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeNo(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setBjStoreId(orderConsumeReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(1);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderRefundReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setExchangeNo(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setBjStoreId(orderRefundReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(2);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

}
