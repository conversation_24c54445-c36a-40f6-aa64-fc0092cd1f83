package com.jnby.mallasset.remote.huarun.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class SynchronizationOrderRespEntity {
    @ApiModelProperty("数据交换平台交换ID")
    @JSONField(ordinal = 1)
    private String exchangeId;
    @ApiModelProperty("异步回调附加数据")
    @JSONField(ordinal = 2)
    private String notifyAttach;
    @ApiModelProperty("华润会员系统会员ID")
    @JSONField(ordinal = 3)
    private String memberId;
    @ApiModelProperty("本次购物积分")
    @JSONField(ordinal = 4)
    private Integer memberIntegral;
    @ApiModelProperty("数字化交易订单号")
    @JSONField(ordinal = 5)
    private String orderNo;
    @ApiModelProperty("数字化交易售后单号")
    @JSONField(ordinal = 6)
    private String salesNo;
}
