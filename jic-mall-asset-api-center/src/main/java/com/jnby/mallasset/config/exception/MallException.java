package com.jnby.mallasset.config.exception;

public class MallException extends RuntimeException {

    private int code;

    public int getCode() {
        return code;
    }

    public MallException(SystemErrorEnum errorEnum) {
        super(errorEnum.getErrorMsg());
        this.code = errorEnum.getErrorCode();
    }

    public MallException(String errorMsg) {
        super(errorMsg);
        this.code = SystemErrorEnum.UNIFIED_CODE_ERROR.getErrorCode();
    }
}
