package com.jnby.mallasset.remote.guangHuan.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DrcMemberRegisterReqEntity.java
 * 文件简介: 会员注册请求
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MemberRespEntity implements Serializable {
    @ApiModelProperty(value = "会员 id")
    private String id;
    @ApiModelProperty(value = "会员卡号")
    private String memberCardNum;
    @ApiModelProperty(value = "微信号")
    private String wechatId;
    @ApiModelProperty(value = "积分余额")
    private int points;
}
