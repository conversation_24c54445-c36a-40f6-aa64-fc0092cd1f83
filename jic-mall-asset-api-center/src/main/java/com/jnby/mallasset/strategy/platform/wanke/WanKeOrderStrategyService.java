package com.jnby.mallasset.strategy.platform.wanke;

import cn.com.scpgroup.SignUtil;
import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.dto.req.order.PayItem;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.remote.wanke.IWanKeRemoteHttpApi;
import com.jnby.mallasset.remote.wanke.entity.BaseWanKeResp;
import com.jnby.mallasset.remote.wanke.entity.WanKeOrderRefundReqEntity;
import com.jnby.mallasset.remote.wanke.entity.WanKeOrderReqEntity;
import com.jnby.mallasset.remote.wanke.entity.WanKeOrderRespEntity;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.math.BigDecimal;
import java.util.*;

/**
 * 万科订单平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.WAN_KE, category = PlatformCategoryTypeEnum.ORDER)
@Slf4j
@AllArgsConstructor
public class WanKeOrderStrategyService extends AbstractOrderService {

    private CashierMallOrderLogMapper cashierMallOrderLogMapper;
    private IWanKeRemoteHttpApi wanKeRemoteHttpApi;

    @Override
    public ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("万科订单同步输入参数：{}", JSON.toJSONString(orderConsumeReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderConsumeReq, cashierMallAssetStoreRef);

            long timestamp = new Date().getTime();
            String appId = cashierMallAssetStoreRef.getPlatformAppId();
            String orgCode = cashierMallAssetStoreRef.getPlatformMallId();
            String privateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
            String mobile = orderConsumeReq.getMemberTel();
            int ordAmount = orderConsumeReq.getOrdAmount().multiply(new BigDecimal(100)).intValue();
            String payAmount = String.valueOf(ordAmount);

            Map<String,Object> bodyParamMap = new LinkedHashMap<>();
            bodyParamMap.put("orderNo",orderConsumeReq.getOrdNum());
            bodyParamMap.put("storeCode",cashierMallAssetStoreRef.getPlatformMallStoreId());
            bodyParamMap.put("channel","1");
            bodyParamMap.put("memberId",orderConsumeReq.getOuterMemberId());
            bodyParamMap.put("telephone",mobile);
            bodyParamMap.put("totalAmount",payAmount);
            bodyParamMap.put("payAmount",payAmount);
            List<Map> paymentInfo = getPayments(orderConsumeReq.getPayItemList(),orderConsumeReq.getOrdPayNo());
            bodyParamMap.put("paymentInfo",paymentInfo);
            bodyParamMap.put("type","0");
            bodyParamMap.put("orderTime", orderConsumeReq.getOrdFinishTime().toString());

            // 使用SignSDK生成签名字符串
            String sign = SignUtil.generateSignByPost(appId, orgCode, timestamp, privateKey, bodyParamMap);
            log.info("万科订单同步生成签名：{}", sign);
            Map<String, String> headers = getHeaders(appId, orgCode, timestamp, sign);
            WanKeOrderReqEntity req = new WanKeOrderReqEntity();
            req.setOrderNo(orderConsumeReq.getOrdNum());
            req.setStoreCode(cashierMallAssetStoreRef.getPlatformMallStoreId());
            req.setChannel("1");
            req.setMemberId(orderConsumeReq.getOuterMemberId());
            req.setTelephone(mobile);
            req.setTotalAmount(payAmount);
            req.setPayAmount(payAmount);
            req.setPaymentInfo(paymentInfo);
            req.setType("0");
            req.setOrderTime(orderConsumeReq.getOrdFinishTime().toString());
            log.info("万科订单同步调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseWanKeResp> execute = wanKeRemoteHttpApi.pointOrder(headers,req).execute();
            BaseWanKeResp baseWanKeResp = execute.body();
            commonResponse.setMsg(baseWanKeResp.getMessage());
            commonResponse.setCode(baseWanKeResp.getStatus());
            if(baseWanKeResp.getStatus() == 200){
                commonResponse.setCode(200);
                WanKeOrderRespEntity respEntity = new WanKeOrderRespEntity();
                respEntity.setOrdNum(orderConsumeReq.getOrdNum());
                respEntity.setExchangeId(orderConsumeReq.getOrdNum());
                respEntity.setExchangeNo(orderConsumeReq.getOrdNum());
                commonResponse.setData(respEntity);
                // 更新返回的参数
                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
            }
            log.info("万科订单同步返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 万科订单同步失败",orderConsumeReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    /**
     * 功能描述: 获取头部
     * 使用场景:
     *
     * @param appId
     * @param orgCode
     * @param timestamp
     * @param sign
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR>
     * @date 2024/7/30 15:18
     */
    private Map<String, String> getHeaders(String appId, String orgCode, long timestamp, String sign) {
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put("appId", appId);
        headers.put("orgCode", orgCode);
        headers.put("timestamp",String.valueOf(timestamp));
        headers.put("sign", sign);
        return headers;
    }

    /**
     * 功能描述: 付款方式
     * 使用场景:
     *
     * @param payItemList
     * @return java.util.List<com.jnby.mallasset.remote.wanke.entity.Payment>
     * <AUTHOR>
     * @date 2024/7/30 15:19
     */
    private List<Map> getPayments(List<PayItem> payItemList,String ordPayNo) {
        List<Map> paymentInfo = new ArrayList<>();
        if(!payItemList.isEmpty()){
            payItemList.forEach(payItem -> {
                int payAmount = payItem.getPayamount().multiply(new BigDecimal(100)).intValue();
                Map<String,Object> payment = new LinkedHashMap<>();
                // 微信
                if(payItem.getId() == 62 || payItem.getId() == 231){
                    payment.put("payMethod","3");
                    payment.put("amount",String.valueOf(payAmount));
                    payment.put("tradeNo",ordPayNo);
                    paymentInfo.add(payment);
                    // 支付宝
                }else if(payItem.getId() == 61){
                    payment.put("payMethod","4");
                    payment.put("amount",String.valueOf(payAmount));
                    payment.put("tradeNo",ordPayNo);
                    paymentInfo.add(payment);
                }
            });
        }
        return paymentInfo;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeNo(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setBjStoreId(orderConsumeReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(1);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult pushRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef assetStoreRef) {
        log.info("万科订单退货退款输入参数：{}", JSON.toJSONString(orderRefundReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderRefundReq, assetStoreRef);

            long timestamp = new Date().getTime();
            String appId = assetStoreRef.getPlatformAppId();
            String orgCode = assetStoreRef.getPlatformMallId();
            String privateKey = assetStoreRef.getPlatformPrivateKey();
            int ordAmount = orderRefundReq.getOrdAmount().multiply(new BigDecimal(100)).intValue();
            String payAmount = String.valueOf(ordAmount);
            int refundamount = orderRefundReq.getRefundAmount().multiply(new BigDecimal(100)).intValue();
            String refundAmount = String.valueOf(refundamount);
            String refundTime = DateUtil.timeStamp3Date(orderRefundReq.getRefundTime(),null);

            Map<String,Object> bodyParamMap = new LinkedHashMap<>();
            bodyParamMap.put("orderNo",orderRefundReq.getRefundNo());
            bodyParamMap.put("oriOrderNo",orderRefundReq.getOrdNum());
            bodyParamMap.put("storeCode",assetStoreRef.getPlatformMallStoreId());
            bodyParamMap.put("channel","1");
            bodyParamMap.put("payAmount",payAmount);
            bodyParamMap.put("refundAmount",refundAmount);
            bodyParamMap.put("refundTime",refundTime);
            bodyParamMap.put("memberId",orderRefundReq.getOuterMemberId());
            bodyParamMap.put("telephone",orderRefundReq.getMemberTel());
            List<PayItem> payItemList = orderRefundReq.getPayItemList();
            payItemList.forEach(payItem -> {
                // 微信
                if(payItem.getId() == 62 || payItem.getId() == 231){
                    bodyParamMap.put("payType","3");
                    bodyParamMap.put("payMethod","3");
                    // 支付宝
                }else if(payItem.getId() == 61){
                    bodyParamMap.put("payType","4");
                    bodyParamMap.put("payMethod","4");
                }
            });
            // 使用SignSDK生成签名字符串
            String sign = SignUtil.generateSignByPost(appId, orgCode, timestamp, privateKey, bodyParamMap);
            log.info("万科订单退货退款生成签名：{}", sign);
            Map<String, String> headers = getHeaders(appId, orgCode, timestamp, sign);
            WanKeOrderRefundReqEntity req = new WanKeOrderRefundReqEntity();
            req.setOrderNo(orderRefundReq.getRefundNo());
            req.setOriOrderNo(orderRefundReq.getOrdNum());
            req.setStoreCode(assetStoreRef.getPlatformMallStoreId());
            req.setChannel("1");
            req.setPayAmount(payAmount);
            req.setRefundAmount(refundAmount);
            req.setRefundTime(refundTime);
            req.setMemberId(orderRefundReq.getOuterMemberId());
            req.setTelephone(orderRefundReq.getMemberTel());
            req.setPayType("3");
            req.setPayMethod("3");
            log.info("万科订单退货退款调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseWanKeResp> execute = wanKeRemoteHttpApi.refundOrder(headers,req).execute();
            BaseWanKeResp baseWanKeResp = execute.body();
            commonResponse.setMsg(baseWanKeResp.getMessage());
            commonResponse.setCode(baseWanKeResp.getStatus());
            if(baseWanKeResp.getStatus() == 200){
                WanKeOrderRespEntity respEntity = new WanKeOrderRespEntity();
                respEntity.setOrdNum(orderRefundReq.getOrdNum());
                respEntity.setExchangeId(orderRefundReq.getRefundNo());
                respEntity.setExchangeNo(orderRefundReq.getRefundNo());
                commonResponse.setData(respEntity);
                commonResponse.setCode(200);
                // 更新返回的参数
                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
            }
            log.info("万科订单退货退款返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 万科订单退货退款失败",orderRefundReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    @Override
    public ResponseResult prepareRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderRefundReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setExchangeNo(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setBjStoreId(orderRefundReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(2);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

}
