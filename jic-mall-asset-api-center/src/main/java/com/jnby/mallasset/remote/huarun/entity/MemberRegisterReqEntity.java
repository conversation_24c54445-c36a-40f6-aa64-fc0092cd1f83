package com.jnby.mallasset.remote.huarun.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MemberRegisterReqEntity implements Serializable {
    @ApiModelProperty(value = "手机号", required = true)
    private String memberTel;
    @ApiModelProperty(value = "来源：微信", required = true)
    private String memberSource;
    @ApiModelProperty(value = "性别：男、女", required = true)
    private String memberSex;
    @ApiModelProperty(value = "会员姓名", required = true)
    private String memberName;
}
