package com.jnby.mallasset.remote.guangHuan.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TradeProductDTO {
    @ApiModelProperty("商品金额")
    private int amount;
    @ApiModelProperty("商品款号")
    private String code;
    @ApiModelProperty("行 id，交易内不重复即可")
    private String id;
    @ApiModelProperty("商品名称")
    private String name;
    @ApiModelProperty("店铺代码")
    private String storeCode;
    @ApiModelProperty("指定交易退货时的原行 id（指定交易退货时必须）")
    private String srcLine;
}
