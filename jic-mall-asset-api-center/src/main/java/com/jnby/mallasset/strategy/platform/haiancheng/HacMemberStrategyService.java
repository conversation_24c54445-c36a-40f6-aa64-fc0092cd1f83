package com.jnby.mallasset.strategy.platform.haiancheng;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.remote.haiancheng.IHacRemoteHttpApi;
import com.jnby.mallasset.remote.haiancheng.entity.BaseHacResp;
import com.jnby.mallasset.remote.haiancheng.entity.HacMemberReqEntity;
import com.jnby.mallasset.remote.haiancheng.entity.HacMemberRespEntity;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.BASE64Utils;
import com.jnby.mallasset.util.Md5Util;
import com.jnby.mallasset.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 海岸城会员平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.HAI_AN_CHENG, category = PlatformCategoryTypeEnum.MEMBER)
@AllArgsConstructor
@Slf4j
public class HacMemberStrategyService extends AbstractMemberService {

    private IHacRemoteHttpApi hacRemoteHttpApi;
    private CashierMallMemberLogMapper cashierMallMemberLogMapper;

    @Override
    public ResponseResult<CommonMemberResponse> openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("海岸城会员注册输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult<CommonMemberResponse> commonResponse = new ResponseResult<>();
        try{
            String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
            String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
            String platformMallId = cashierMallAssetStoreRef.getPlatformMallId();
            String mobile = memberRegisterReq.getMobile();
            String storeId = memberRegisterReq.getStoreId();
            // 记录流水
            CashierMallMemberLog cashierMallMemberLog = recordMemberLog(cashierMallAssetStoreRef, storeId, mobile);
            // 判断会员是否存在
            CommonMemberResponse commonMemberResponse = new CommonMemberResponse();
            ResponseResult<CommonMemberResponse> result = getMemberInfo(memberRegisterReq,cashierMallAssetStoreRef);
            if(result != null && result.getCode() == 200){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                String memberId = result.getData().getMemberId();
                commonMemberResponse.setMemberFlag(1);
                commonMemberResponse.setMemberId(memberId);
                commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                commonResponse.setData(commonMemberResponse);
                // 更新执行标识
                cashierMallMemberLog.setOpenUserId(memberId);
                cashierMallMemberLog.setIsExecute("Y");
                cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
                return commonResponse;
            }

            String requestId = StringUtil.randomUUID();
            Map<String, String> httpHeaders = new HashMap<>();
            httpHeaders.put("x-app-id", platformAppId);
            httpHeaders.put("x-request-id", requestId); // 请求ID, 不同请求需要不一样
            String dateTimestamp = System.currentTimeMillis() + "";
            httpHeaders.put("x-timestamp", dateTimestamp);  // 时间戳

            Map<String, Object> paramMap = new TreeMap<>();
            paramMap.put("mallId", Integer.parseInt(platformMallId));
            paramMap.put("mobile",mobile);
            httpHeaders.put("x-sign", getMD5SignString(platformPrivateKey, requestId, dateTimestamp, paramMap));  // 根据参数计算签名
            log.info("海岸城会员注册header参数：{}",JSON.toJSONString(httpHeaders));
            HacMemberReqEntity req = new HacMemberReqEntity();
            req.setMallId(Integer.parseInt(platformMallId));
            req.setMobile(mobile);
            String paramStr = JSON.toJSONString(req);
            String base64Param = BASE64Utils.encode(paramStr);
            log.info("海岸城会员注册参数base64加密结果：{}",base64Param);
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), base64Param);

            Response<BaseHacResp<HacMemberRespEntity>> response = hacRemoteHttpApi.registerForMember(httpHeaders,requestBody).execute();
            BaseHacResp<HacMemberRespEntity> baseHacResp = response.body();
            log.info("海岸城会员注册返回结果：{}", JSON.toJSONString(baseHacResp));
            if(baseHacResp != null && baseHacResp.getCode().equals("ok")){
                HacMemberRespEntity hacMemberRespEntity = baseHacResp.getData();
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonMemberResponse.setMemberFlag(0);
                commonMemberResponse.setMemberId(hacMemberRespEntity.getId());
                commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                commonResponse.setData(commonMemberResponse);
                // 更新执行标识
                cashierMallMemberLog.setOpenUserId(hacMemberRespEntity.getId());
                cashierMallMemberLog.setIsExecute("Y");
                cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
            }else {
                commonResponse.setCode(1000);
                commonResponse.setMsg(baseHacResp.getMsg());
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 海岸城会员注册失败",memberRegisterReq.getMobile(),e);
        }
        return commonResponse;
    }

    private CashierMallMemberLog recordMemberLog(CashierMallAssetStoreRef cashierMallAssetStoreRef, String storeId, String mobile) {
        CashierMallMemberLog cashierMallMemberLog = new CashierMallMemberLog();
        cashierMallMemberLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallMemberLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallMemberLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallMemberLog.setBjStoreId(storeId);
        cashierMallMemberLog.setMobile(mobile);
        cashierMallMemberLog.setIsExecute("N");
        cashierMallMemberLogMapper.insertEntity(cashierMallMemberLog);
        return cashierMallMemberLog;
    }

    @Override
    public ResponseResult<CommonMemberResponse> getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("海岸城会员查询输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult<CommonMemberResponse> commonResponse = new ResponseResult<>();
        try{
            String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
            String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
            String platformMallId = cashierMallAssetStoreRef.getPlatformMallId();
            String mobile = memberRegisterReq.getMobile();

            String requestId = StringUtil.randomUUID();
            Map<String, String> httpHeaders = new HashMap<>();
            httpHeaders.put("x-app-id", platformAppId);
            httpHeaders.put("x-request-id", requestId); // 请求ID, 不同请求需要不一样
            String dateTimestamp = System.currentTimeMillis() + "";
            httpHeaders.put("x-timestamp", dateTimestamp);  // 时间戳

            Map<String, Object> paramMap = new TreeMap<>();
            paramMap.put("mallId", Integer.parseInt(platformMallId));
            paramMap.put("mobile",mobile);
            httpHeaders.put("x-sign", getMD5SignString(platformPrivateKey, requestId, dateTimestamp, paramMap));  // 根据参数计算签名
            log.info("海岸城会员查询header参数：{}",JSON.toJSONString(httpHeaders));
            HacMemberReqEntity req = new HacMemberReqEntity();
            req.setMallId(Integer.parseInt(platformMallId));
            req.setMobile(mobile);
            String paramStr = JSON.toJSONString(req);
            String base64Param = BASE64Utils.encode(paramStr);
            log.info("海岸城会员查询参数base64加密结果：{}",base64Param);
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), base64Param);
            Response<BaseHacResp<HacMemberRespEntity>> response = hacRemoteHttpApi.queryMember(httpHeaders,requestBody).execute();
            BaseHacResp<HacMemberRespEntity> baseHacResp = response.body();
            log.info("海岸城会员查询返回结果：{}", JSON.toJSONString(baseHacResp));
            if(baseHacResp != null && baseHacResp.getCode().equals("ok")){
                CommonMemberResponse commonMemberResponse = new CommonMemberResponse();

                HacMemberRespEntity hacMemberRespEntity = baseHacResp.getData();
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonMemberResponse.setMemberFlag(1);
                commonMemberResponse.setMemberId(hacMemberRespEntity.getId());
                commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                commonResponse.setData(commonMemberResponse);
            }else {
                commonResponse.setCode(1000);
                commonResponse.setMsg(baseHacResp.getMsg());
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 海岸城会员查询失败",memberRegisterReq.getMobile(),e);
        }
        return commonResponse;
    }

    private String getMD5SignString(String appSecret, String requestId, String dateTimestamp, Map<String, Object> params) {
        String str = appSecret + ":" + requestId + ":" + dateTimestamp + ":" + JSON.toJSONString(params);
        return Md5Util.getMd5Hash(str); // 进行MD5
    }
}
