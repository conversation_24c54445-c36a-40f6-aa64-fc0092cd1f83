FROM jnbyharbor.jnby.com/base-images/baseimagejdk8:v1.3

MAINTAINER box-group

RUN mkdir -p /jic-mall-asset/jic-mall-asset-api-center

WORKDIR /jic-mall-asset/jic-mall-asset-api-center

EXPOSE 9601

COPY target/jic-mall-asset-api-center.jar jic-mall-asset-api-center.jar

ENV TZ='Asia/Shanghai'

ENTRYPOINT ["java", "-Xmx4g", "-Xms4g","-XX:NewRatio=3","-Xss512k", "-Xmn2g","-XX:SurvivorRatio=2", "-XX:+UseParallelGC","-Dreactor.netty.pool.leasingStrategy=lifo", "-jar", "jic-mall-asset-api-center.jar"]

CMD ["--spring.profiles.active=prod"]


