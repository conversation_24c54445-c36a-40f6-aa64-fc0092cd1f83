package com.jnby.mallasset.config.redis;
import com.jnby.common.cache.RedisPoolUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/10/21 9:11 AM
 */
@Configuration
public class RedisConfig {
    @Value("${redis.host}")
    private String host;

    @Value("${redis.cacheName}")
    private String cacheName;

    @Value("${redis.password:#{null}}")
    private String auth;

//    @Value("${redis.maxidel}")
//    private String maxIdle;
//
//    @Value("${redis.maxtotal}")
//    private String maxTotal;

    private RedisPoolUtil createConnection(){
        RedisPoolUtil redisUtil = new RedisPoolUtil();
        redisUtil.setAuth(StringUtils.isEmpty(auth) ? null : auth);
        redisUtil.setHost(host);
        redisUtil.setCacheName(cacheName);
//        redisUtil.setMaxIdle(maxIdle);
//        redisUtil.setMaxTotal(maxTotal);
        return redisUtil;
    }

    @Bean(initMethod = "init", destroyMethod = "close")
    public RedisPoolUtil redisUtil() {
        return createConnection();
    }
}
