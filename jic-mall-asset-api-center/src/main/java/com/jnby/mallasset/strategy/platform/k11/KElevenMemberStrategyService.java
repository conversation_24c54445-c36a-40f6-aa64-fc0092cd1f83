package com.jnby.mallasset.strategy.platform.k11;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.remote.k11.IKelevenRemoteHttpApi;
import com.jnby.mallasset.remote.k11.entity.BaseElevenResultResp;
import com.jnby.mallasset.remote.k11.entity.MemberPushReqEntity;
import com.jnby.mallasset.remote.k11.entity.MemberRegisterEntity;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * 凯德会员平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.K_ELEVEN, category = PlatformCategoryTypeEnum.MEMBER)
@AllArgsConstructor
@Slf4j
public class KElevenMemberStrategyService extends AbstractMemberService {

    private IKelevenRemoteHttpApi kelevenRemoteHttpApi;
    private CashierMallMemberLogMapper cashierMallMemberLogMapper;

    @Override
    public ResponseResult openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("K11会员注册输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult commonResponse = new ResponseResult();
        CommonMemberResponse commonMemberResponse = new CommonMemberResponse();
        try{
            // 记录流水
            String storeId = memberRegisterReq.getStoreId();
            String mobile = memberRegisterReq.getMobile();
            CashierMallMemberLog cashierMallMemberLog = recordMemberLog(cashierMallAssetStoreRef, storeId, mobile);
            String platformMallId = cashierMallAssetStoreRef.getPlatformMallId();
            String integralSkuCode = cashierMallAssetStoreRef.getIntegralSkuCode();
            String[] fields = integralSkuCode.split(",");
            String apiKey = fields[0].split("#")[1];
            String apiSecret = fields[1].split("#")[1];
            String interfaceId = fields[2].split("#")[1];
            String originId = fields[3].split("#")[1];
            String skey = fields[4].split("#")[1];
            String timestamp = String.valueOf(System.currentTimeMillis()/1000);
            String nickName = memberRegisterReq.getNickName() == null ? "" : memberRegisterReq.getNickName();

            // 先查询没有再注册
            ResponseResult commonResponseNew = queryMemberInfo(memberRegisterReq, cashierMallAssetStoreRef, commonMemberResponse, commonResponse);
            if (commonResponseNew != null) {
                return commonResponseNew;
            }


            MemberPushReqEntity memberPushReqEntity = new MemberPushReqEntity();
            memberPushReqEntity.setPhone(memberRegisterReq.getMobile());
            memberPushReqEntity.setPhone_code("86");
            memberPushReqEntity.setReg_origin(platformMallId);
            memberPushReqEntity.setNick_name(nickName);
            memberPushReqEntity.setOrigin(Integer.parseInt(originId));

            String content = JsonUtil.toJson(memberPushReqEntity);

            Map<String, String> params = new TreeMap<>();
            params.put("content", content);
            params.put("timestamp", timestamp);
            params.put("apiKey", apiKey);
            params.put("apiSecret", apiSecret);
            params.put("interfaceId", interfaceId);

            String sign = KElevenGenerateSign.genSignature(params);
            log.info("会员注册生成的签名为：{}",sign);

            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("apiKey",apiKey);
            paramMap.put("interfaceId",interfaceId);
            paramMap.put("timestamp",timestamp);
            paramMap.put("sign",sign);
            paramMap.put("content",content);
            log.info("K11会员远程注册传输参数：{}", JSON.toJSONString(paramMap));
            Response<String> execute = kelevenRemoteHttpApi.registerMember(paramMap).execute();
            String result = KElevenGenerateSign.decodeSignature(execute.body(),skey);
            log.info("K11会员远程注册返回参数：{}",result);
            BaseElevenResultResp baseElevenResultResp = JSONObject.parseObject(result,BaseElevenResultResp.class);

            if(baseElevenResultResp != null && baseElevenResultResp.getCode() == 0){
                MemberRegisterEntity memberRegisterEntity = JSONObject.parseObject(baseElevenResultResp.getData().toString(),MemberRegisterEntity.class);
                // 注册成功，绑定KDP
                Map<String,Object> json = new HashMap<>();
                json.put("member_code",memberRegisterEntity.getVip_code());

                String reqContent = JsonUtil.toJson(json);
                Map<String, String> kdpParams = new TreeMap<>();
                kdpParams.put("content", reqContent);
                kdpParams.put("timestamp", timestamp);
                kdpParams.put("apiKey", apiKey);
                kdpParams.put("apiSecret", apiSecret);
                kdpParams.put("interfaceId", interfaceId);

                String kdpSign = KElevenGenerateSign.genSignature(kdpParams);
                log.info("会员绑定KDP生成的签名为：{}",kdpSign);

                Map<String,Object> kdpParamMap = new HashMap<>();
                kdpParamMap.put("apiKey",apiKey);
                kdpParamMap.put("interfaceId",interfaceId);
                kdpParamMap.put("timestamp",timestamp);
                kdpParamMap.put("sign",kdpSign);
                kdpParamMap.put("content",reqContent);
                log.info("K11会员远程绑定KDP传输参数：{}", JSON.toJSONString(kdpParamMap));
                Response<String> kdpExecute = kelevenRemoteHttpApi.bindKdp(kdpParamMap).execute();
                String kdpResult = KElevenGenerateSign.decodeSignature(kdpExecute.body(),skey);
                log.info("K11会员远程绑定KDP返回参数：{}", kdpResult);
                BaseElevenResultResp kdpResp = JSONObject.parseObject(kdpResult,BaseElevenResultResp.class);
                if(kdpResp.getCode() == 0){
                    commonMemberResponse.setMemberFlag(0);
                    commonMemberResponse.setMemberId(memberRegisterEntity.getVip_code());
                    commonMemberResponse.setMemberCardNo(memberRegisterEntity.getVip_code());
                    commonMemberResponse.setLevelId(memberRegisterEntity.getLevel_id());
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                    commonResponse.setData(commonMemberResponse);

                    // 更新执行标识
                    cashierMallMemberLog.setOpenUserId(memberRegisterEntity.getVip_code());
                    cashierMallMemberLog.setIsExecute("Y");
                    cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
                    return commonResponse;
                }else{
                    commonResponse.setCode(kdpResp.getCode());
                    commonResponse.setMsg(kdpResp.getMsg());
                }
            }else{
                commonResponse.setCode(baseElevenResultResp.getCode());
                commonResponse.setMsg(baseElevenResultResp.getMsg());
            }
            log.info("K11会员注册返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} K11会员注册开卡失败",memberRegisterReq.getMobile(),e);
        }

        return commonResponse;
    }

    private ResponseResult queryMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef, CommonMemberResponse commonMemberResponse, ResponseResult commonResponse) {
        ResponseResult<MemberRegisterEntity> resultResp = getMemberInfo(memberRegisterReq, cashierMallAssetStoreRef);
        if(resultResp.getCode() == ResultCodeEnum.SUCCESS.getCode()){
            MemberRegisterEntity memberRegisterEntity = resultResp.getData();
            commonMemberResponse.setMemberFlag(1);
            commonMemberResponse.setMemberId(memberRegisterEntity.getVip_code());
            commonMemberResponse.setLevelId(memberRegisterEntity.getLevel_id());
            commonMemberResponse.setMemberCardNo(memberRegisterEntity.getVip_code());
            commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
            commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
            commonResponse.setData(commonMemberResponse);
            return commonResponse;
        }
        return null;
    }

    @Override
    public ResponseResult<MemberRegisterEntity> getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("K11会员查询输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            String integralSkuCode = cashierMallAssetStoreRef.getIntegralSkuCode();
            String[] fields = integralSkuCode.split(",");

            String timestamp = String.valueOf(System.currentTimeMillis()/1000);
            String apiKey = fields[0].split("#")[1];
            String apiSecret = fields[1].split("#")[1];
            String interfaceId = fields[2].split("#")[1];
            String skey = fields[4].split("#")[1];

            Map<String, String> params = new TreeMap<>();
            params.put("keyword", memberRegisterReq.getMobile());
            params.put("type", "2");

            params.put("timestamp", timestamp);
            params.put("apiKey", apiKey);
            params.put("apiSecret", apiSecret);
            params.put("interfaceId", interfaceId);

            String sign = KElevenGenerateSign.genSignature(params);
            log.info("会员查询生成的签名为：{}",sign);
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("apiKey",apiKey);
            paramMap.put("interfaceId",interfaceId);
            paramMap.put("timestamp",timestamp);
            paramMap.put("sign",sign);

            paramMap.put("keyword",memberRegisterReq.getMobile());
            paramMap.put("type","2");
            log.info("K11会员远程查询传输参数：{}", JSON.toJSONString(paramMap));
            Response<String> execute = kelevenRemoteHttpApi.getMember(paramMap).execute();
            String result = KElevenGenerateSign.decodeSignature(execute.body(),skey);
            log.info("K11会员远程查询返回参数：{}", result);

            BaseElevenResultResp baseElevenResultResp = JSONObject.parseObject(result,BaseElevenResultResp.class);
            if(baseElevenResultResp != null && baseElevenResultResp.getCode() == 0){
                if(baseElevenResultResp.getData() != null){
                    List<MemberRegisterEntity> jsonArray = JSONObject.parseArray(baseElevenResultResp.getData().toString(),MemberRegisterEntity.class);
                    MemberRegisterEntity memberRegisterEntity = jsonArray.get(0);
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                    commonResponse.setData(memberRegisterEntity);
                }else{
                    commonResponse.setCode(baseElevenResultResp.getCode());
                    commonResponse.setMsg(baseElevenResultResp.getMsg());
                }
            }else{
                commonResponse.setCode(baseElevenResultResp.getCode());
                commonResponse.setMsg(baseElevenResultResp.getMsg());
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} K11会员查询失败",memberRegisterReq.getMobile(),e);
        }
        log.info("K11会员查询结果返回参数：{}", JSON.toJSONString(commonResponse));
        return commonResponse;
    }

    private CashierMallMemberLog recordMemberLog(CashierMallAssetStoreRef cashierMallAssetStoreRef, String storeId, String mobile) {
        CashierMallMemberLog cashierMallCooMemberLog = new CashierMallMemberLog();
        cashierMallCooMemberLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallCooMemberLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallCooMemberLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallCooMemberLog.setBjStoreId(storeId);
        cashierMallCooMemberLog.setMobile(mobile);
        cashierMallCooMemberLog.setIsExecute("N");
        cashierMallMemberLogMapper.insertEntity(cashierMallCooMemberLog);
        return cashierMallCooMemberLog;
    }
}
