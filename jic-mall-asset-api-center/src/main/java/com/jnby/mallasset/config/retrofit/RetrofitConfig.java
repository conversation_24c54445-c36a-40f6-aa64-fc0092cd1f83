package com.jnby.mallasset.config.retrofit;

import brave.http.HttpTracing;
import brave.okhttp3.TracingInterceptor;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.jnby.mallasset.remote.callback.IOrderCallbackRemoteHttpApi;
import com.jnby.mallasset.remote.dayuecheng.IDayuechengPosRemoteHttpApi;
import com.jnby.mallasset.remote.dayuecheng.IDayuechengRemoteHttpApi;
import com.jnby.mallasset.remote.dingding.IDingDingRemoteHttpApi;
import com.jnby.mallasset.remote.drc.IDrcRemoteHttpApi;
import com.jnby.mallasset.remote.guangHuan.IGuangHuanRemoteHttpApi;
import com.jnby.mallasset.remote.haiancheng.IHacRemoteHttpApi;
import com.jnby.mallasset.remote.huarun.IHuaRunCouponRemoteHttpApi;
import com.jnby.mallasset.remote.huarun.IHuaRunRefundRemoteHttpApi;
import com.jnby.mallasset.remote.huarun.IHuaRunRemoteHttpApi;
import com.jnby.mallasset.remote.kaide.IKaiDeRemoteHttpApi;
import com.jnby.mallasset.remote.kkmall.IKkMallRemoteHttpApi;
import com.jnby.mallasset.remote.longhu.ILongHuRemoteHttpApi;
import com.jnby.mallasset.remote.mallcoo.IMallCooPosRemoteHttpApi;
import com.jnby.mallasset.remote.mallcoo.IMallCooRemoteHttpApi;
import com.jnby.mallasset.remote.oeli.IOeliRemoteHttpApi;
import com.jnby.mallasset.remote.wanke.IWanKeRemoteHttpApi;
import com.jnby.mallasset.remote.xiexin.IXiexinRemoteHttpApi;
import com.jnby.mallasset.remote.xiexin.IXiexinSoapRemoteHttpApi;
import com.jnby.mallasset.remote.yintai99.IYinTai99RemoteHttpApi;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;
import retrofit2.converter.simplexml.SimpleXmlConverterFactory;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Configuration
public class RetrofitConfig {
    /**
     * 猫酷服务
     */
    @Value("${mallCoo.url}")
    private String mallCooUrl;
    /**
     * 华润服务
     */
    @Value("${huaRun.url}")
    private String huaRunUrl;
    /**
     * 天目里服务
     */
    @Value("${oeli.url}")
    private String oEliUrl;

    /**
     * 大融城服务
     */
    @Value("${drc.url}")
    private String drcUrl;
    /**
     * 凯德服务：uatcsmpsfapi.capitastar.com.cn
     */
    @Value("${kaiDe.url}")
    private String kaiDeUrl;
    /**
     * 万科服务：crm-test.scpgroup.com.cn
     */
    @Value("${wanKe.url}")
    private String wanKeUrl;
    /**
     * KKMALL服务:https://crm.kingkeybanner.com
     */
    @Value("${kkMall.url}")
    private String kkMallUrl;
    /**
     * 订单回调服务
     */
    @Value("${order.callback.url}")
    private String orderCallbackUrl;

    /**
     * 钉钉服务
     */
    @Value("${dingDing.msg.url}")
    private String dingDingMsgUrl;
    /**
     * 万科服务：crm-test.scpgroup.com.cn
     */
    @Value("${guangHuan.url}")
    private String guangHuanUrl;
    /**
     * 颐堤港POS服务
     */
    @Value("${yidigang.pos.url}")
    private String yidigangPosUrl;
    /**
     * 大悦城服务
     */
    @Value("${dayuecheng.url}")
    private String dycUrl;
    /**
     * 大悦城POS服务
     */
    @Value("${dayuecheng.pos.url}")
    private String dycPosUrl;

    @Value("${xiexin.url}")
    private String xiexinUrl;

    @Value("${xiexin.soap.url}")
    private String xiexinSoapUrl;

    @Value("${haiAnCheng.url}")
    private String haiAnChengUrl;

    @Value("${yintai.99.url}")
    private String yinTai99Url;

    /**
     * 龙湖服务
     */
    @Value("${longHu.url}")
    private String longHuUrl;

    @Bean
    public IMallCooRemoteHttpApi createIMallCooRemoteHttpApi() {
        return getApi(mallCooUrl, IMallCooRemoteHttpApi.class);
    }

    @Bean
    public IOrderCallbackRemoteHttpApi createIOrderCallbackRemoteHttpApi() {
        return getApi(orderCallbackUrl, IOrderCallbackRemoteHttpApi.class);
    }

    @Bean
    public IHuaRunRemoteHttpApi createIHuaRunRemoteHttpApi() {
        return getApi(huaRunUrl, IHuaRunRemoteHttpApi.class);
    }

    @Bean
    public IOeliRemoteHttpApi createIOeliRemoteHttpApi() {
        return getApi(oEliUrl, IOeliRemoteHttpApi.class);
    }

    @Bean
    public IDrcRemoteHttpApi createIDrcRemoteHttpApi(){
        return getApi(drcUrl, IDrcRemoteHttpApi.class);
    }
    @Bean
    public IKaiDeRemoteHttpApi createIKaiDeRemoteHttpApi(){
        return getApi(kaiDeUrl, IKaiDeRemoteHttpApi.class);
    }
    @Bean
    public IWanKeRemoteHttpApi createIWanKeRemoteHttpApi(){
        return getApi(wanKeUrl, IWanKeRemoteHttpApi.class);
    }
    @Bean
    public IKkMallRemoteHttpApi createIKkMallRemoteHttpApi(){
        return getApi(kkMallUrl, IKkMallRemoteHttpApi.class);
    }

    // 华润退款单独设置超时时间
    @Bean
    public IHuaRunRefundRemoteHttpApi createIHuaRunRefundRemoteHttpApi(){
        return getApiForHuaRunRefund(huaRunUrl, 100,50,IHuaRunRefundRemoteHttpApi.class);
    }

    @Bean
    public IDingDingRemoteHttpApi createIDingDingRemoteHttpApi(){
        return getApi(dingDingMsgUrl, IDingDingRemoteHttpApi.class);
    }

    @Bean
    public IGuangHuanRemoteHttpApi createIGuangHuanRemoteHttpApi(){
        return getApi(guangHuanUrl, IGuangHuanRemoteHttpApi.class);
    }

    @Bean
    public IHuaRunCouponRemoteHttpApi createIHuaRunCouponRemoteHttpApi(){
        return getApiForHuaRunRefund(huaRunUrl, 100,50,IHuaRunCouponRemoteHttpApi.class);
    }

    @Bean
    public IMallCooPosRemoteHttpApi createIMallCooPosRemoteHttpApi() {
        return getApi(yidigangPosUrl, IMallCooPosRemoteHttpApi.class);
    }

    @Bean
    public IDayuechengRemoteHttpApi createIDayuechengRemoteHttpApi() {
        return getApi(dycUrl, IDayuechengRemoteHttpApi.class);
    }

    @Bean
    public IDayuechengPosRemoteHttpApi createIDayuechengPosRemoteHttpApi() {
        return getApi(dycPosUrl, IDayuechengPosRemoteHttpApi.class);
    }


    @Bean
    public IXiexinSoapRemoteHttpApi createIXiexinSoapRemoteHttpApi(){
        return getXiexinApi(xiexinSoapUrl, IXiexinSoapRemoteHttpApi.class);
    }

    @Bean
    public IXiexinRemoteHttpApi createIXiexinRemoteHttpApi(){
        return getApi(xiexinUrl, IXiexinRemoteHttpApi.class);
    }

    @Bean
    public IHacRemoteHttpApi createIHacRemoteHttpApi(){
        return getApi(haiAnChengUrl, IHacRemoteHttpApi.class);
    }

    @Bean
    public IYinTai99RemoteHttpApi createIYinTai99RemoteHttpApi(){
        return getApiForYinTai99(yinTai99Url, 100,30,IYinTai99RemoteHttpApi.class);
    }

    @Bean
    public ILongHuRemoteHttpApi createILongHuRemoteHttpApi(){
        return getApi(longHuUrl, ILongHuRemoteHttpApi.class);
    }
    /**
     * 增加请求返回""和"null"的处理
     * 1.Integer=>null
     * 2.Double=>null
     * 3.Long=>null
     */
    public static final Gson gson = new GsonBuilder()
            .registerTypeAdapter(Integer.class, new IntegerDefaultAdapter())
            .registerTypeAdapter(Double.class, new DoubleDefaultAdapter())
            .registerTypeAdapter(Long.class, new LongDefaultAdapter()).create();


    /**
     * 定义Retrofit Map集合
     */
    private static Map<String, Retrofit> retrofitMap = new ConcurrentHashMap<>();

    /**
     * 注入托管 tracing
     */
    @Resource
    private HttpTracing httpTracing;

    /**
     * 获取Bean
     *
     * @param url
     * @return
     */
    public Retrofit getRetrofitBean(String url) {
        if (retrofitMap.containsKey(url)) {
            return retrofitMap.get(url);
        }
        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                // 设置callTimeout时需要将其他timeout设置为0才会生效，否则按照其他timeout生效
                                .callTimeout(15, TimeUnit.SECONDS)
                                .connectTimeout(0, TimeUnit.SECONDS)
                                .readTimeout(0, TimeUnit.SECONDS)
                                .writeTimeout(0, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                // 每个url默认50个连接 3分钟过期
                                .connectionPool(new ConnectionPool(50, 3, TimeUnit.MINUTES))
                                .addInterceptor(TracingInterceptor.create(httpTracing))
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        retrofitMap.put(url, retrofit);
        return retrofit;
    }

    /**
     * 特殊处理refund
     * @param url
     * @return
     */
    public Retrofit getRetrofitBeanForHuaRun(String url) {
        if (retrofitMap.containsKey(url)) {
            return retrofitMap.get(url);
        }
        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                // 设置callTimeout时需要将其他timeout设置为0才会生效，否则按照其他timeout生效
                                .callTimeout(35, TimeUnit.SECONDS)
                                .connectTimeout(0, TimeUnit.SECONDS)
                                .readTimeout(0, TimeUnit.SECONDS)
                                .writeTimeout(0, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                // 每个url默认50个连接 3分钟过期
                                .connectionPool(new ConnectionPool(100, 3, TimeUnit.MINUTES))
                                .addInterceptor(TracingInterceptor.create(httpTracing))
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        retrofitMap.put(url, retrofit);
        return retrofit;
    }

    public Retrofit getNoCacheRetrofitBean(String url, Integer connectCount, Integer callTimeOut) {
        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                // 设置完整调用的默认超时。
                                .callTimeout(callTimeOut, TimeUnit.SECONDS)
                                .connectTimeout(callTimeOut, TimeUnit.SECONDS)
                                .readTimeout(callTimeOut, TimeUnit.SECONDS)
                                .writeTimeout(callTimeOut, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                // 连接池 最大空闲个数和保活时间
                                .connectionPool(new ConnectionPool(connectCount, 3, TimeUnit.MINUTES))
                                .addInterceptor(TracingInterceptor.create(httpTracing))
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        return retrofit;
    }

    /**
     * 获取API
     *
     * @param url     地址
     * @param service
     * @param <T>
     * @return
     */
    public <T> T getApi(String url, Class<T> service) {
        return getRetrofitBean(url).create(service);
    }

    public <T> T getApiForHuaRun(String url, Class<T> service) {
        return getRetrofitBeanForHuaRun(url).create(service);
    }

    public <T> T getApiForHuaRunRefund(String url,Integer connectCount, Integer callTimeOut, Class<T> service) {
        return getNoCacheRetrofitBean(url,connectCount,callTimeOut).create(service);
    }

    public <T> T getApiForYinTai99(String url,Integer connectCount, Integer callTimeOut, Class<T> service) {
        return getNoCacheRetrofitBean(url,connectCount,callTimeOut).create(service);
    }


    //------ main 方法使用 ------
    public static <T> T getMainApi(String url, Class<T> service) {
        return getMainRetrofitBean(url).create(service);
    }
    public static Retrofit getMainRetrofitBean(String url) {
        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                // 设置完整调用的默认超时。
                                .callTimeout(30, TimeUnit.SECONDS)
                                .connectTimeout(0, TimeUnit.SECONDS)
                                .readTimeout(0, TimeUnit.SECONDS)
                                .writeTimeout(0, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                // 每个url默认50个连接 3分钟过期
                                .connectionPool(new ConnectionPool(5, 3, TimeUnit.MINUTES))
//                                .addInterceptor(TracingInterceptor.create(httpTracing))
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        return retrofit;
    }

    public static <T> T getXiexinApi(String url, Class<T> service) {
        return getXiexinRetrofitBean(url).create(service);
    }

    public static Retrofit getXiexinRetrofitBean(String url) {
        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                // 设置完整调用的默认超时。
                                .callTimeout(30, TimeUnit.SECONDS)
                                .connectTimeout(0, TimeUnit.SECONDS)
                                .readTimeout(0, TimeUnit.SECONDS)
                                .writeTimeout(0, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                // 每个url默认50个连接 3分钟过期
                                .connectionPool(new ConnectionPool(5, 3, TimeUnit.MINUTES))
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(SimpleXmlConverterFactory.create())
                .build();
        return retrofit;
    }
}
