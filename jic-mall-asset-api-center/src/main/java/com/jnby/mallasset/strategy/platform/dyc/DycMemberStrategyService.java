package com.jnby.mallasset.strategy.platform.dyc;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.MallException;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.remote.dayuecheng.IDayuechengRemoteHttpApi;
import com.jnby.mallasset.remote.mallcoo.MallCooUtils;
import com.jnby.mallasset.remote.mallcoo.entity.*;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.Map;

/**
 * 大悦城会员平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.DA_YUE_CHENG, category = PlatformCategoryTypeEnum.MEMBER)
@AllArgsConstructor
@Slf4j
public class DycMemberStrategyService extends AbstractMemberService {

    private IDayuechengRemoteHttpApi dayuechengRemoteHttpApi;
    private CashierMallMemberLogMapper cashierMallMemberLogMapper;

    @Override
    public ResponseResult<CommonMemberResponse> openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("大悦城猫酷会员注册输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult<CommonMemberResponse> commonResponse = new ResponseResult<>();
        try{
            CashierMallMemberLog cashierMallMemberLog = recordMemberLog(memberRegisterReq, cashierMallAssetStoreRef);
            // 先查询会员是否存在
            ResponseResult<MemberQueryRespEntity> result = getMemberInfo(memberRegisterReq,cashierMallAssetStoreRef);
            CommonMemberResponse commonMemberResponse = new CommonMemberResponse();
            if(result.getCode() == ResultCodeEnum.SUCCESS.getCode()){
                // 会员存在直接返回会员信息
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                // 说明是老会员
                MemberQueryRespEntity memberQueryRespEntity = result.getData();
                commonMemberResponse.setMemberFlag(1);
                commonMemberResponse.setMemberId(memberQueryRespEntity.getOpenUserID());
                commonMemberResponse.setMemberCardNo(memberQueryRespEntity.getMallCardNo());
                commonResponse.setMsg(result.getMsg());
                commonResponse.setData(commonMemberResponse);
                // 更新执行标识
                cashierMallMemberLog.setOpenUserId(memberQueryRespEntity.getOpenUserID());
                cashierMallMemberLog.setIsExecute("Y");
                cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
                return commonResponse;
            }
            String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
            String[] platformPublicKeys = cashierMallAssetStoreRef.getPlatformPublicKey().split("-");
            String publicKey = platformPublicKeys[0];
            String privateKey = platformPublicKeys[1];

            // 新会员进行注册
            MemberCreateCardReqEntity req = new MemberCreateCardReqEntity();
            req.setMobile(memberRegisterReq.getMobile());
            log.info("大悦城猫酷会员注册调用远程接口参数：{}", JSON.toJSONString(req));
            Map<String, String> header = MallCooUtils.getHeaders(platformAppId,publicKey,privateKey, JSON.toJSONString(req));

            Response<BaseMallCooResp<MemberCreateCardRespEntity>> execute = dayuechengRemoteHttpApi.registerMemberInfo(header, req).execute();
            BaseMallCooResp<MemberCreateCardRespEntity> baseDycResp = execute.body();
            commonResponse.setMsg(baseDycResp.getMessage());
            if(baseDycResp.getCode() == 1){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                MemberCreateCardRespEntity memberQueryRespEntity = baseDycResp.getData();
                commonMemberResponse.setMemberFlag(0);
                commonMemberResponse.setMemberId(memberQueryRespEntity.getOpenUserID());
                // 获取会员卡号
                ResponseResult<MemberQueryRespEntity> memberInfo = getMemberInfo(memberRegisterReq,cashierMallAssetStoreRef);
                if(memberInfo != null && memberInfo.getData() != null){
                    commonMemberResponse.setMemberCardNo(memberInfo.getData().getMallCardNo());
                }
                commonResponse.setMsg(baseDycResp.getMessage());
                commonResponse.setData(commonMemberResponse);
            }else {
                commonResponse.setCode(baseDycResp.getCode());
            }
            log.info("大悦城猫酷注册会员返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 大悦城猫酷会员注册异常",memberRegisterReq.getMobile(), e);
        }
        return commonResponse;
    }

    private CashierMallMemberLog recordMemberLog(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef mallAssetStoreRef) {
        String storeId = memberRegisterReq.getStoreId();
        String mobile = memberRegisterReq.getMobile();
        CashierMallMemberLog cashierMallMemberLog = new CashierMallMemberLog();
        cashierMallMemberLog.setMallPlatform(mallAssetStoreRef.getApiPlatform());
        cashierMallMemberLog.setMallId(mallAssetStoreRef.getPlatformMallId());
        cashierMallMemberLog.setMallStoreId(mallAssetStoreRef.getPlatformMallStoreId());
        cashierMallMemberLog.setBjStoreId(storeId);
        cashierMallMemberLog.setMobile(mobile);
        cashierMallMemberLog.setIsExecute("N");
        cashierMallMemberLogMapper.insertEntity(cashierMallMemberLog);
        return cashierMallMemberLog;
    }


    @Override
    public ResponseResult<MemberQueryRespEntity> getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("大悦城会员查询输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult<MemberQueryRespEntity> commonResponse = new ResponseResult<>();
        try{
            String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
            String[] platformPublicKey = cashierMallAssetStoreRef.getPlatformPublicKey().split("-");
            String publicKey = platformPublicKey[0];
            String privateKey = platformPublicKey[1];

            MemberQueryReqEntity reqEntity = MemberQueryReqEntity.builder().mobile(memberRegisterReq.getMobile()).build();
            Map<String, String> header = MallCooUtils.getHeaders(platformAppId,publicKey,privateKey, JSON.toJSONString(reqEntity));
            log.info("大悦城猫酷会员查询调用远程接口入参，headers:[{}]--------------req:[{}]", JSON.toJSONString(header), JSON.toJSONString(reqEntity));

            Response<BaseMallCooResp<MemberQueryRespEntity>> execute = dayuechengRemoteHttpApi.getUnionMemberInfo(header, reqEntity).execute();
            if (!execute.isSuccessful()) {
                throw new MallException(SystemErrorEnum.MEMBER_INTERFACE_API_ERROR);
            }
            BaseMallCooResp<MemberQueryRespEntity> body = execute.body();
            log.info("大悦城猫酷会员查询调用远程接口响应：{}", JSON.toJSONString(body));
            if (!body.isSuccess()) {
                throw new MallException(body.getMessage());
            }
            MemberQueryRespEntity data = body.getData();
            if(body.getCode() == 1){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setMsg(body.getMessage());
                commonResponse.setData(data);
            }else {
                commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                commonResponse.setMsg(body.getMessage());
                commonResponse.setData(null);
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 大悦城猫酷会员查询异常",memberRegisterReq.getMobile(), e);
        }
        return commonResponse;
    }
}
