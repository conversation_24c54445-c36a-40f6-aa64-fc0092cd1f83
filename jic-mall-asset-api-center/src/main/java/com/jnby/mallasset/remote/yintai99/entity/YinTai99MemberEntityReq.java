package com.jnby.mallasset.remote.yintai99.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class YinTai99MemberEntityReq implements Serializable {
    @ApiModelProperty("APIKEY")
    private String apiKey;
    @ApiModelProperty("签名")
    private String signature;
    @ApiModelProperty("手机号")
    private String mobileNumber;
    @ApiModelProperty("媒体类型WECHAT – 微信")
    private String mediaType;
    @ApiModelProperty("公众号")
    private String mediaId;
    @ApiModelProperty("商场编号")
    private String mallId;
}
