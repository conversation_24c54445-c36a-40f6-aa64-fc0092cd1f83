package com.jnby.mallasset.remote.kkmall.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DrcMemberRegisterReqEntity.java
 * 文件简介: 会员请求
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class KkMallMemberRespEntity implements Serializable {
    @ApiModelProperty(value = "会员编号")
    private String memberCode;
    @ApiModelProperty(value = "会员级别id")
    private String levelId;
    @ApiModelProperty(value = "会员级别编号")
    private String levelCode;
    @ApiModelProperty(value = "会员级别名称")
    private String levelName;
    @ApiModelProperty(value = "会员级别图片")
    private String levelCover;
    @ApiModelProperty(value = "昵称")
    private String nickName;
    @ApiModelProperty(value = "会员id")
    private String cusotmerId;
    @ApiModelProperty(value = "会员手机号")
    private String mobileNo;
}
