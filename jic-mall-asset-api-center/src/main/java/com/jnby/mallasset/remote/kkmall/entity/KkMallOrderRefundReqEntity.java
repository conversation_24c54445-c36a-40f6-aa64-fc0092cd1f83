package com.jnby.mallasset.remote.kkmall.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 文件名: com.jnby.mallasset.remote.kkmall.entity-KkMallOrderReqEntity.java
 * 文件简介: KKMALL退货单请求
 *
 * <AUTHOR>
 * @date 2024/8/1 15:52
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class KkMallOrderRefundReqEntity implements Serializable {
    @ApiModelProperty(value = "原始小票号", required = true)
    private String sourceReceiptNo;
    @ApiModelProperty(value = "交易时间", required = true)
    private String transTime;
    @ApiModelProperty(value = "退货小票号", required = true)
    private String returnReceiptNo;
    @ApiModelProperty(value = "来源 1 POS机，2.后台手工补录，3.扫码积分 6 微信商圈 7 支付宝商圈 9 其他", required = true)
    private int source;
    @ApiModelProperty(value = "退货时间", required = true)
    private String returnTime;
    @ApiModelProperty(value = "退货金额", required = true)
    private BigDecimal returnAmount;
}
