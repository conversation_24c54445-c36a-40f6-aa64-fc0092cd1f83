package com.jnby.mallasset.api.dto.asset;

import com.google.common.base.Preconditions;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("支付配置入参")
public class PayConfigReqDto {

    @ApiModelProperty(value = "伯俊门店CODE。与vid2选1必填")
    private String storeId;

    @ApiModelProperty(value = "VID。与storeId2选1必填")
    private String vid;

    @ApiModelProperty(value = "业务渠道：1=微商城、2=BOX、3=POS+、4=复购计划、5=POS+线上（离店）", required = true)
    private Integer businessType;

    @ApiModelProperty(value = "支付方式：不传返回全部,需要指定传。1=微信直连、2=收钱吧-线上支付、3=支付宝直连、4=电银、5=华润自研、6=银联-B扫C、7=收钱吧-轻POS支付、8=银联-轻POS支付、9=收钱吧-B扫C")
    private Integer payChannel;


    public void check() {
        if (StringUtils.isBlank(vid)) {
            Preconditions.checkArgument(StringUtils.isNotBlank(storeId), "伯俊门店CODE和VID 2选1必填");
        }
        if (StringUtils.isBlank(storeId)) {
            Preconditions.checkArgument(StringUtils.isNotBlank(vid), "伯俊门店CODE和VID 2选1必填");
        }
        Preconditions.checkArgument(businessType != null, "业务渠道必填");
    }
}
