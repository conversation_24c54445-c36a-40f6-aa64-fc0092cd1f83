package com.jnby.mallasset.remote.huarun.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class CarVoucherRule {
    @ApiModelProperty("卡券要发放的数量")
    @JSONField(ordinal = 1)
    private int voucherCount;
    @ApiModelProperty("是否可以多次领取")
    @JSONField(ordinal = 2)
    private boolean voucherReceive;
}
