package com.jnby.mallasset.remote.k11;


import com.jnby.mallasset.remote.k11.entity.BaseElevenResultResp;
import com.jnby.mallasset.remote.k11.entity.MemberRegisterEntity;
import com.jnby.mallasset.remote.xiexin.entity.vip.XiexinVipResponseSoapEnvelope;
import com.jnby.mallasset.remote.xiexin.entity.vip.XiexinVipSoapEnvelope;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.Map;

/**
 * 文件名: com.jnby.mallasset.remote.k11-KElevenRemoteHttpApi.java
 * 文件简介: K11 平台
 *
 * <AUTHOR>
 * @date 2025/8/6 17:52
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface IKelevenRemoteHttpApi {

    @POST("/v1/external/mcreate")
    @FormUrlEncoded
    Call<String> registerMember(@FieldMap Map<String,Object> paramMap);

    @GET("/v1/external/get-member-basic-info")
    Call<String> getMember(@QueryMap Map<String,Object> paramMap);

    @POST("/v1/external/bind-kdp")
    @FormUrlEncoded
    Call<String> bindKdp(@FieldMap Map<String,Object> paramMap);

    @POST("/v1/kpos/consume-order")
    @FormUrlEncoded
    Call<String> consumeOrder(@FieldMap Map<String,Object> paramMap);

    @POST("/v1/kpos/return-order")
    @FormUrlEncoded
    Call<String> returnOrder(@FieldMap Map<String,Object> paramMap);

}
