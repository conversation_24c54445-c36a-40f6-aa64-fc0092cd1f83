package com.jnby.mallasset.remote.guangHuan.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-BaseDrcResp.java
 * 文件简介: 光环基础返回对象
 *
 * <AUTHOR>
 * @date 2024/7/23 14:00
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class BaseGuangHuanResp<T> implements Serializable {
    @ApiModelProperty(value = "返回值：0000=成功，其它=失败，其中 3004=认证失败")
    private String status;
    @ApiModelProperty(value = "返回消息")
    private String message;
    @ApiModelProperty("响应数据")
    private T data;

}
