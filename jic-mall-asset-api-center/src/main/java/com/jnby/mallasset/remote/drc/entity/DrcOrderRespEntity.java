package com.jnby.mallasset.remote.drc.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DrcMemberRegisterReqEntity.java
 * 文件简介: 订单消费响应请求
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class DrcOrderRespEntity implements Serializable {
    @ApiModelProperty(value = "会员号")
    private String member_code;
    @ApiModelProperty(value = "店铺号")
    private String store_code;
    @ApiModelProperty(value = "获得积分")
    private String bonus_sales;
    @ApiModelProperty(value = "当前积分")
    private String current_bonus;
    @ApiModelProperty("订单号")
    private String ordNum;
    @ApiModelProperty("业务ID，唯一值")
    private String exchangeId;
    @ApiModelProperty("外部单号")
    private String exchangeNo;
}
