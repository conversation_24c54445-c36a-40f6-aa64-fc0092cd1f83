package com.jnby.mallasset.enums;

/**
 * 支付平台：1=微信直连、2=收钱吧-线上支付、3=支付宝直连、4=电银、5=华润自研、6=银联-B扫C、7=收钱吧-轻POS支付、8=银联-轻POS支付、9=收钱吧-B扫C
 */
public enum PayChannelEnum {
    WX_PAY(1, "微信直连"),
    SQB_ONLINE(2, "收钱吧-线上支付"),
    ALI_PAY(3, "支付宝直连"),
    DIAN_YIN(4, "电银"),
    HUA_RUN(5, "华润自研"),
    YIN_LIAN_B2C(6, "银联-B扫C"),
    SQB_POS(7, "收钱吧-轻POS支付"),
    YIN_LIAN_POS(8, "银联-轻POS支付"),
    SQB_B2C(9, "收钱吧-B扫C"),
    ;

    /**
     * 编号
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;

    PayChannelEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
