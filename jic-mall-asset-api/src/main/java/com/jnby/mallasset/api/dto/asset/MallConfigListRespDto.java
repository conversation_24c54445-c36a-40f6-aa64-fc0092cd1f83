package com.jnby.mallasset.api.dto.asset;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class MallConfigListRespDto {
    @ApiModelProperty(value = "伯俊CODE")
    private String storeId;
    @ApiModelProperty(value = "商场ID")
    private String platformMallId;
    @ApiModelProperty(value = "商场名称")
    private String mallName;
    @ApiModelProperty(value = "商场门店ID")
    private String platformMallStoreId;
    @ApiModelProperty(value = "商场门店名称")
    private String mallStoreName;
    @ApiModelProperty(value = "门店收款方式：1=自收银、自退款，2=商场收银、自退款，3=商场收银、商场退款，4=华润自研")
    private Integer collectionType;
    @ApiModelProperty(value = "支付方式列表")
    private List<PayConfigRespDto> payConfigList;
}
