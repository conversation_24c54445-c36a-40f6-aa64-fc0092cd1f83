package com.jnby.mallasset.strategy.factory;

import com.jnby.mallasset.strategy.category.AbstractCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CouponServiceFactory extends MallServiceFactory {
    /**
     * 接口路由
     *
     * @param platform 平台
     * @param category 类目
     * @return AbstractCouponService
     */
    @Override
    public AbstractCouponService router(PlatformTypeEnum platform, PlatformCategoryTypeEnum category) {
        if (platform == null) {
            throw new RuntimeException("platform mast be not null");
        }
        if (SERVICE_MAP.isEmpty()) {
            throw new RuntimeException("SERVICE_MAP is empty");
        }
        AbstractCouponService server = (AbstractCouponService) SERVICE_MAP.get(getMapName(platform, category));
        if (server == null) {
            throw new RuntimeException("server is null");
        }
        return server;

    }

}
