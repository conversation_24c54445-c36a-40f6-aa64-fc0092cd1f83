package com.jnby.mallasset.remote.mallcoo.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@ApiModel("批量发券入参")
@Data
public class CouponBatchSendReqEntity implements Serializable {
    @JSONField(ordinal = 1, name = "UserList")
    private List<UseInfoList> UserList;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class UseInfoList implements Serializable {
        /**
         * {
         * "BussinessID": null,
         * "TraceID": "6d025700bb79458ca73ae5051cf626ab",
         * "PICMID": "jBBn1JzXBMf1rjE4GjGjrMjFF8W5InJFSqwmB04DjXc=",
         * "Mobile": "18611111111",
         * "InternationalMobileCode":"",
         * }
         */
        @ApiModelProperty("追踪ID【不可重复】(保持请求的的唯一性，防止重复提交)【格式：自定义编号+AppID】")
        @JSONField(ordinal = 1, name = "TraceID")
        private String TraceID;
        @ApiModelProperty("投放ID【由猫酷提供】")
        @JSONField(ordinal = 2, name = "PICMID")
        private String PICMID;
        @ApiModelProperty("手机号码")
        @JSONField(ordinal = 3, name = "Mobile")
        private String Mobile;
    }
}
