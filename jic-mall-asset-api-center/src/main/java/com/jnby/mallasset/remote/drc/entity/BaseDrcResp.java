package com.jnby.mallasset.remote.drc.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-BaseDrcResp.java
 * 文件简介: 大融城基础返回对象
 *
 * <AUTHOR>
 * @date 2024/7/23 14:00
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class BaseDrcResp<T> implements Serializable {
    @ApiModelProperty(value = "结果")
    private int code;
    @ApiModelProperty(value = "返回消息")
    private String msg;
    @ApiModelProperty("响应数据")
    private T result;

    public boolean isSuccess(){
        if (this.code == 200) {
            return true;
        }
        return false;
    }
}
