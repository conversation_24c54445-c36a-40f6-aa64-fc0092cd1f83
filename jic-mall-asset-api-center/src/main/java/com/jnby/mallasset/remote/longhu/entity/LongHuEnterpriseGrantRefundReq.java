package com.jnby.mallasset.remote.longhu.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class LongHuEnterpriseGrantRefundReq {

    @ApiModelProperty(value = "退款请求单号")
    private String requestNo;

    @ApiModelProperty(value = "发放原请求单号")
    private String origRequestNo;

    @ApiModelProperty(value = "退款/取消(1-退款，2-取消（不传默认退款）)")
    private Integer actionType;

    @ApiModelProperty(value = "企业编号")
    private String enterpriseNo;

    @ApiModelProperty(value = "业务系统ID")
    private String appId;

    @ApiModelProperty(value = "撤回金额 (撤回金额精确到小数点1位单位：珑珠)")
    private BigDecimal refundAmount;

}
