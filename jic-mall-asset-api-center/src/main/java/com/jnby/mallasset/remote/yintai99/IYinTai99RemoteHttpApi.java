package com.jnby.mallasset.remote.yintai99;


import com.jnby.mallasset.remote.yintai99.entity.YinTai99MemberEntityReq;
import com.jnby.mallasset.remote.yintai99.entity.YinTai99MemberEntityResp;
import com.jnby.mallasset.remote.yintai99.entity.YinTai99PosEntityReq;
import com.jnby.mallasset.remote.yintai99.entity.YinTai99PosEntityResp;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * 文件名: com.jnby.mallasset.remote.yintai99-IYinTai99RemoteHttpApi.java
 * 文件简介: 银泰-in99
 *
 * <AUTHOR>
 * @date 2025/4/27 17:24
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface IYinTai99RemoteHttpApi {


    /**
     * 功能描述: /PosService65,/SalesTrans
     * 使用场景:
     *
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.yintai99.entity.YinTai99PosEntityResp>
     * <AUTHOR>
     * @date 2025/4/27 18:03
     */
    @POST("rest/salestransaction/lite")
    Call<YinTai99PosEntityResp> posOrder( @Body YinTai99PosEntityReq req);

    @POST("rest/eccrm65api/search")
    Call<YinTai99MemberEntityResp> searchMember(@Body YinTai99MemberEntityReq req);

    @POST("rest/eccrm65api/register")
    Call<YinTai99MemberEntityResp> registerMember(@Body YinTai99MemberEntityReq req);

}
