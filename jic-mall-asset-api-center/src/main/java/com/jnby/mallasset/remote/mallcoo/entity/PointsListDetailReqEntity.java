package com.jnby.mallasset.remote.mallcoo.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Builder
@ApiModel("积分明细入参")
@Data
public class PointsListDetailReqEntity implements Serializable {
    @ApiModelProperty("用户手机号")
    @JSONField(ordinal=1, name = "Mobile")
    private String Mobile;
    @ApiModelProperty("每页数量（默认10）")
    @JSONField(ordinal=2, name = "PageSize")
    private Integer PageSize;
    @ApiModelProperty("当前页码（默认1）")
    @JSONField(ordinal=3, name = "PageIndex")
    private Integer PageIndex;
}
