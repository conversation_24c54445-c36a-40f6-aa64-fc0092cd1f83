package com.jnby.mallasset.remote.xiexin.entity.order;

import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

@Data
@Root(name = "SaleShopItemResponse" )
@Namespace(reference = "http://tempuri.org/")
public class XiexinOrderPosResponseResult {

    @Element(name = "SaleShopItemResult" , required = false)
    private boolean saleShopItemResult;

    @Element(name = "msg" , required = false)
    private String msg;

    @Element(name = "storePoint" , required = false)
    private Double storePoint;
}
