package com.jnby.mallasset.convert;

import com.alibaba.nacos.common.utils.UuidUtils;
import com.google.common.collect.Lists;
import com.jnby.mallasset.api.dto.asset.AssetOperateReqDto;
import com.jnby.mallasset.api.dto.coupon.CouponInfoRespDto;
import com.jnby.mallasset.api.dto.coupon.CouponOperationRespDto;
import com.jnby.mallasset.remote.mallcoo.entity.CouponBatchReturnReqEntity;
import com.jnby.mallasset.remote.mallcoo.entity.CouponBatchReturnRespEntity;
import com.jnby.mallasset.remote.mallcoo.entity.CouponBatchUseReqEntity;
import com.jnby.mallasset.remote.mallcoo.entity.CouponCanUseListRespEntity;
import com.jnby.mallasset.strategy.context.CouponContext;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(config = ConvertConfig.class)
public interface CouponConvertor {

    CouponConvertor INSTANCE = Mappers.getMapper(CouponConvertor.class);

    /**
     * 优惠券使用码 列表转换猫酷列表实体
     */
    default List<CouponBatchUseReqEntity.UseInfoList> mallCooCouponNoList2UseList(List<String> couponNoList) {
        if (CollectionUtils.isEmpty(couponNoList)) {
            return null;
        }
        return couponNoList.stream()
                .map(CouponBatchUseReqEntity.UseInfoList::new)
                .collect(Collectors.toList());
    }

    /**
     * 优惠券使用码 列表转换猫酷列表实体
     */
    default List<CouponBatchReturnReqEntity.ReturnCoupon> mallCooCouponNoList2ReturnList(List<String> couponNoList) {
        if (CollectionUtils.isEmpty(couponNoList)) {
            return null;
        }
        List<CouponBatchReturnReqEntity.ReturnCoupon> rs = Lists.newArrayList();
        for (String couponNo : couponNoList) {
            CouponBatchReturnReqEntity.ReturnCoupon returnCoupon = new CouponBatchReturnReqEntity.ReturnCoupon(couponNo, UuidUtils.generateUuid());
            rs.add(returnCoupon);
        }
        return rs;
    }


    /**
     * 猫酷优惠券转本地实体
     */
    List<CouponInfoRespDto> mallCooEntityList2DtoList(List<CouponCanUseListRespEntity.CouponInfo> couponInfoList);

    @Mapping(target = "couponTemplateNo", source = "couponRuleNo")
    @Mapping(target = "couponNo", source = "VCode")
    @Mapping(target = "threshold", source = "deductible", numberFormat = "#0.00", defaultValue = "0.00")
    @Mapping(target = "reduceMoney", source = "reduceMoney", numberFormat = "#0.00", defaultValue = "0.00")
    CouponInfoRespDto couponInfoToCouponInfoRespDto(CouponCanUseListRespEntity.CouponInfo couponInfo);

    List<CouponOperationRespDto> mallCooReturnCouponList2Dto(List<CouponBatchReturnRespEntity.ReturnCoupon> returnCouponList);

    default CouponOperationRespDto returnCouponToCouponOperationRespDto(CouponBatchReturnRespEntity.ReturnCoupon returnCoupon) {
        if (returnCoupon == null) {
            return null;
        }
        return CouponOperationRespDto.builder()
                .couponNo(returnCoupon.getVCode())
                .traceId(returnCoupon.getTraceID())
                .success(returnCoupon.getIsSuccess())
                .errorMsg(returnCoupon.getMsg())
                .build();
    }

    CouponContext reqDto2Context(AssetOperateReqDto req);
}
