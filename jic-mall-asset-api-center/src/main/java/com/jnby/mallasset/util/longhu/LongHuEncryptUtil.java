package com.jnby.mallasset.util.longhu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jnby.mallasset.remote.longhu.entity.LongHuEnterpriseEncryptResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.NoSuchPaddingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;
import java.util.UUID;



/**
 * <AUTHOR>
 * @since 2021/11/26
 */
@RestController
@Slf4j
public class LongHuEncryptUtil {

    public static LongHuEnterpriseEncryptResp encrypt(JSONObject jsonObject, String serverPublicKey, String appPriavteKey) throws Exception {
        log.info("龙湖请求参数参数加密开始 原始数据 {}", jsonObject.toJSONString());
        if (StringUtils.isBlank(serverPublicKey)) {
            log.error("response 加密失败, server共钥获取失败");
            throw new Exception("网关系统异常");
        }

        // 原始参数生成签名，并添加签名到原始参数
        // 原始参数转JSON字符串
        String originalParamJsonStr = JSON.toJSONString(jsonObject, SerializerFeature.MapSortField);

        String sign;
        try {
            //我们私钥加签名
            sign = RSAUtils.sign(originalParamJsonStr, appPriavteKey);
        } catch (NoSuchAlgorithmException | SignatureException | InvalidKeyException | InvalidKeySpecException e) {
            log.error("response RSA 签名失败：{}", e.getMessage(), e);
            throw new Exception("网关系统异常");
        }

        //生成的签名sign添加到原始参数中
        jsonObject.put("sign", sign);

        //加签名后的参数转JSON串
        String originalParamSignJsonStr = JSON.toJSONString(jsonObject, SerializerFeature.MapSortField);

        // 加签后的参数AES加密
        //随机生成AES密码
        String aesKey = UUID.randomUUID().toString();
        //加签后的参数AES加密
        String encryptData = AESUtil.encrypt(originalParamSignJsonStr, aesKey);

        // 随机AES密码进行RSA加密
        String encryptKey = null;
        try {
            encryptKey = RSAUtils.publicEncrypt(aesKey, serverPublicKey);
        } catch (NoSuchAlgorithmException | InvalidKeyException | NoSuchPaddingException | InvalidKeySpecException e) {
            log.error("response RSA 加密失败：{}", e.getMessage());
            throw new Exception("网关系统异常");
        }
        // 最后生成机密后的参数
        LongHuEnterpriseEncryptResp longHuEnterpriseEncryptResp = new LongHuEnterpriseEncryptResp();
        longHuEnterpriseEncryptResp.setEncryptKey(encryptKey);
        longHuEnterpriseEncryptResp.setEncryptData(encryptData);
        return longHuEnterpriseEncryptResp;
    }

    public static LongHuEnterpriseEncryptResp decrypt(LongHuEnterpriseEncryptResp encryptResp, String serverPublicKey, String appPriavteKey) throws Exception {
        // 获取加密返回值encryptKey和encryptData*d

        String encryptKey = encryptResp.getEncryptKey();
        String encryptData = encryptResp.getEncryptData();
        // 解密得到AES的key 我们自己的私钥 外部私钥解密
        String aesKey;
        try {
            aesKey = RSAUtils.privateDecrypt(encryptKey, appPriavteKey);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException | InvalidKeySpecException e) {
            log.error("request RSA 解密失败：{}", e.getMessage());
            throw new Exception("网关系统异常");
        }

        // aes的key解密加密回参
        String originalWalletResponseStr = AESUtil.decrypt(encryptData, aesKey);
        JSONObject originalWalletResponseObj = JSON.parseObject(originalWalletResponseStr);
        // 回参验签
        String sign = originalWalletResponseObj.getString("sign");
        if (StringUtils.isBlank(sign)) {
            throw new Exception("签名为空，请检查请求参数");
        }
        originalWalletResponseObj.remove("sign");
        String originalWalletResponseNoSignStr = JSON.toJSONString(originalWalletResponseObj, SerializerFeature.MapSortField);

        // 商户的公钥 需要去商户获取拿到进行验证 此公钥是通过商户获取
        String publicKey = serverPublicKey;
        //钱包公钥验签
        boolean signVerify = false;
        try {
            signVerify = RSAUtils.verify(originalWalletResponseNoSignStr, publicKey, sign);
        } catch (NoSuchAlgorithmException | SignatureException | InvalidKeyException | InvalidKeySpecException e) {
            log.error("request RSA 验证失败：{}", e.getMessage());
            throw new Exception("网关系统异常");
        }

        if (signVerify) {
            return JSONObject.parseObject(originalWalletResponseStr, LongHuEnterpriseEncryptResp.class);
        } else {
            log.error("originRequest:{}, request RSA验证结果: false.", originalWalletResponseStr);
            throw new Exception("网关签名验证未通过");
        }
    }
}
