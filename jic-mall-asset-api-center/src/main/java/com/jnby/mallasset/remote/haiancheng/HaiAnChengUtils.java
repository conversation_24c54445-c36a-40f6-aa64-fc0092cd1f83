package com.jnby.mallasset.remote.haiancheng;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.jnby.mallasset.config.retrofit.RetrofitConfig;
import com.jnby.mallasset.remote.haiancheng.entity.*;
import com.jnby.mallasset.util.BASE64Utils;
import com.jnby.mallasset.util.Md5Util;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import retrofit2.Response;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
public class HaiAnChengUtils {

    public static void main1(String[] args) {
        String appId = "5350514299";
        String appSecret = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDD3py7CTdrKNDT+aBVeeb0sDsIcDvBBxG161dT9zlsRYeoeN664jexGSYD9WatMPUSXmYInjoJzoxVKYOgcTH9wZ5ii3fyAGGmxjWONbnApQqz9VhsBkAEMTV3B5tSxE+XKyOt2wUY62mwsxxDEPDdx/F0ZnqJ5lAg8G24JVSomwIDAQAB";
        String requestId = UUID.randomUUID().toString();
        try {
            IHacRemoteHttpApi api = RetrofitConfig.getMainApi("https://crm-hac-test.tcc-cloud.com", IHacRemoteHttpApi.class);
            Map<String, String> httpHeaders = new HashMap<>();
            httpHeaders.put("x-app-id", appId);
            httpHeaders.put("x-request-id", requestId); // 请求ID, 不同请求需要不一样
            String dateTimestamp = System.currentTimeMillis() + "";
            httpHeaders.put("x-timestamp", dateTimestamp);  // 时间戳

            Map<String, Object> paramMap = new TreeMap<>();
            paramMap.put("mallId",1);
            paramMap.put("mobile","15820584615");
            httpHeaders.put("x-sign", getMD5SignString(appSecret, requestId, dateTimestamp, paramMap));  // 根据参数计算签名
            System.out.println(JSON.toJSONString(httpHeaders));
            HacMemberReqEntity req = new HacMemberReqEntity();
            req.setMallId(1);
            req.setMobile("15820584615");
            String paramStr = JSON.toJSONString(req);
            String base64Param = BASE64Utils.encode(paramStr);
            System.out.println(base64Param);
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), base64Param);
            Response<BaseHacResp<HacMemberRespEntity>> response = api.queryMember(httpHeaders,requestBody).execute();
            BaseHacResp<HacMemberRespEntity> body = response.body();

//            Response<BaseHacResp<HacMemberRespEntity>> response = api.registerForMember(httpHeaders,requestBody).execute();
//            BaseHacResp<HacMemberRespEntity> body = response.body();

            System.out.println(JSON.toJSONString(body));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void main2(String[] args) {
        String appId = "5350514299";
        String appSecret = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDD3py7CTdrKNDT+aBVeeb0sDsIcDvBBxG161dT9zlsRYeoeN664jexGSYD9WatMPUSXmYInjoJzoxVKYOgcTH9wZ5ii3fyAGGmxjWONbnApQqz9VhsBkAEMTV3B5tSxE+XKyOt2wUY62mwsxxDEPDdx/F0ZnqJ5lAg8G24JVSomwIDAQAB";
        String requestId = UUID.randomUUID().toString();
        try {
            IHacRemoteHttpApi api = RetrofitConfig.getMainApi("https://crm-hac-test.tcc-cloud.com", IHacRemoteHttpApi.class);
            Map<String, String> httpHeaders = new HashMap<>();
            httpHeaders.put("x-app-id", appId);
            httpHeaders.put("x-request-id", requestId); // 请求ID, 不同请求需要不一样
            String dateTimestamp = System.currentTimeMillis() + "";
            httpHeaders.put("x-timestamp", dateTimestamp);  // 时间戳

            Map<String, Object> paramMap = new TreeMap<>();
            paramMap.put("memberId","1878682398382161921");
            paramMap.put("thirdPartyOrderNo","RE2025021300011");
            paramMap.put("totalPaidAmount",new BigDecimal(100));
            paramMap.put("SkuCode","00103154");
            paramMap.put("storeId",1003753);
            paramMap.put("totalBuyQty",1);
            httpHeaders.put("x-sign", getMD5SignString(appSecret, requestId, dateTimestamp, paramMap));  // 根据参数计算签名
            System.out.println("httpHeaders ==============" + JSON.toJSONString(httpHeaders));

            HacOrderCreateReqEntity req = new HacOrderCreateReqEntity();
            req.setMemberId("1878682398382161921");
            req.setThirdPartyOrderNo("RE2025021300011");
            req.setTotalPaidAmount(new BigDecimal(100));
            req.setSkuCode("00103154");
            req.setStoreId(1003753);
            req.setTotalBuyQty(1);
            String paramStr = JSON.toJSONString(req);
            String base64Param = BASE64Utils.encode(paramStr);
            System.out.println("base64Param ===========" + base64Param);
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), base64Param);
            Response<BaseHacResp<HacOrderRespEntity>> response = api.orderCreate(httpHeaders,requestBody).execute();
            BaseHacResp<HacOrderRespEntity> body = response.body();

            System.out.println(JSON.toJSONString(body));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        String appId = "5350514299";
        String appSecret = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDD3py7CTdrKNDT+aBVeeb0sDsIcDvBBxG161dT9zlsRYeoeN664jexGSYD9WatMPUSXmYInjoJzoxVKYOgcTH9wZ5ii3fyAGGmxjWONbnApQqz9VhsBkAEMTV3B5tSxE+XKyOt2wUY62mwsxxDEPDdx/F0ZnqJ5lAg8G24JVSomwIDAQAB";
        String requestId = UUID.randomUUID().toString();
        try {
            IHacRemoteHttpApi api = RetrofitConfig.getMainApi("https://crm-hac-test.tcc-cloud.com", IHacRemoteHttpApi.class);
            Map<String, String> httpHeaders = new HashMap<>();
            httpHeaders.put("x-app-id", appId);
            httpHeaders.put("x-request-id", requestId); // 请求ID, 不同请求需要不一样
            String dateTimestamp = System.currentTimeMillis() + "";
            httpHeaders.put("x-timestamp", dateTimestamp);  // 时间戳

            Map<String, Object> paramMap = new TreeMap<>();
            paramMap.put("thirdPartyOrderNo","RE2025021300018-0005");
            paramMap.put("originalOrderNo","250121000010033");
            paramMap.put("totalReturnAmount",new BigDecimal(100));
            paramMap.put("storeId",1003753);
            paramMap.put("totalReturnQty",1);
            httpHeaders.put("x-sign", getMD5SignString(appSecret, requestId, dateTimestamp, paramMap));  // 根据参数计算签名
            System.out.println("httpHeaders ==============" + JSON.toJSONString(httpHeaders));

            HacOrderRefundReqEntity req = new HacOrderRefundReqEntity();
            req.setThirdPartyOrderNo("RE2025021300018-0005");
            req.setOriginalOrderNo("250121000010033");
            req.setStoreId(1003753);
            req.setTotalReturnQty(1);
            req.setTotalReturnAmount(new BigDecimal(100));
            String paramStr = JSON.toJSONString(req);
            String base64Param = BASE64Utils.encode(paramStr);
            System.out.println("base64Param ===========" + base64Param);
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), base64Param);
            Response<BaseHacResp<HacOrderRespEntity>> response = api.orderRefund(httpHeaders,requestBody).execute();
            BaseHacResp<HacOrderRespEntity> body = response.body();

            System.out.println(JSON.toJSONString(body));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    private static String getMD5SignString(String appSecret, String requestId, String dateTimestamp, Map<String, Object> params) {
        String str = appSecret + ":" + requestId + ":" + dateTimestamp + ":" + JSON.toJSONString(params);
        return Md5Util.getMd5Hash(str); // 进行MD5
    }
}
