package com.jnby.mallasset.module.model;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.sql.Blob;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.incrementer.OracleKeyGenerator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: CodeGenerator
 * @Date: 2024-07-15 13:35:45
 * @Description: 商场资产规则配置
 */
@Data
@TableName("CASHIER_MALL_ASSET_CONFIG")
@ApiModel(value="CashierMallAssetConfig对象", description="商场资产规则配置")
public class CashierMallAssetConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID")
    private Long id;
    @ApiModelProperty(value = "0未删除、1已删除")
    @TableField("IS_DELETE")
    private Boolean isDelete;
    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "商场名称")
    @TableField("MALL_NAME")
    private String mallName;

    @ApiModelProperty(value = "平台商场ID")
    @TableField("PLATFORM_MALL_ID")
    private String platformMallId;
    @ApiModelProperty(value = "平台APP_ID")
    @TableField("PLATFORM_APP_ID")
    private String platformAppId;
    @ApiModelProperty(value = "平台公钥")
    @TableField("PLATFORM_PUBLIC_KEY")
    private String platformPublicKey;
    @ApiModelProperty(value = "平台私钥")
    @TableField("PLATFORM_PRIVATE_KEY")
    private String platformPrivateKey;

    @ApiModelProperty(value = "积分增加类型: 1=无感积分、2=订单手动积分")
    @TableField("POINTS_INCREMENT_TYPE")
    private Integer pointsIncrementType;
    @ApiModelProperty(value = "是否有积分抵扣")
    @TableField("POINTS_CAN_USE")
    private Boolean pointsCanUse;
    @ApiModelProperty(value = "积分名称")
    @TableField("POINTS_NAME")
    private String pointsName;
    @ApiModelProperty(value = "是否使用商场券")
    @TableField("COUPON_CAN_USE")
    private Boolean couponCanUse;
    @ApiModelProperty(value = "使用规则")
    @TableField("USE_RULE_CONTENT")
    private String useRuleContent;
    @ApiModelProperty(value = "使用规则：0=没有，1=有")
    @TableField("HAS_USE_RULE")
    private Boolean hasUseRule;
    @ApiModelProperty(value = "隐私协议：0=无，1=有")
    @TableField("HAS_PRIVACY")
    private Boolean hasPrivacy;
    @ApiModelProperty(value = "隐私内容")
    @TableField("PRIVACY_CONTENT")
    private String privacyContent;
    @ApiModelProperty(value = "积分换算比例。1积分换算多少元。例如0.01=1积分兑换0.01元")
    @TableField("POINTS_DEDUCTION_SCALE")
    private BigDecimal pointsDeductionScale;
    @ApiModelProperty(value = "单订单积分抵扣起扣门槛")
    @TableField("POINTS_DEDUCTION_THRESHOLD")
    private Long pointsDeductionThreshold;
    @ApiModelProperty(value = "单订单积分抵扣上限")
    @TableField("POINTS_DEDUCTION_UPPER_LIMIT")
    private Long pointsDeductionUpperLimit;
    @ApiModelProperty(value = "单订单积分抵扣阶梯单位。100代表积分每满100才可以抵扣计算")
    @TableField("POINTS_DEDUCTION_LADDER")
    private Long pointsDeductionLadder;
    @ApiModelProperty(value = "对接API平台：1=猫酷，2=海鼎")
    @TableField("API_PLATFORM")
    private Long apiPlatform;
    @ApiModelProperty(value = "是否需要手机验证")
    @TableField("HAS_PHONE_VERIFICATION")
    private Boolean hasPhoneVerification;
    @ApiModelProperty(value = "是否静默开卡")
    @TableField("HAS_OPEN_CARD_DEFAULT")
    private Boolean hasOpenCardDefault;
    @ApiModelProperty(value = "是否外跳开卡")
    @TableField("HAS_OPEN_CARD_JUMP_OUT")
    private Boolean hasOpenCardJumpOut;
    @ApiModelProperty(value = "外跳开卡地址")
    @TableField("OPEN_CARD_JUMP_URL")
    private String openCardJumpUrl;
    @ApiModelProperty(value = "开卡提示文案")
    @TableField("OPEN_CARD_SUB_TITLE")
    private String openCardSubTitle;
    @ApiModelProperty(value = "开卡开关是否显示")
    @TableField("HAS_OPEN_CARD_BUTTON")
    private Boolean hasOpenCardButton;
    @ApiModelProperty(value = "外跳开卡appId")
    @TableField("JUMP_OUT_APP_ID")
    private String jumpOutAppId;;
    @ApiModelProperty(value = "商场LOGO")
    @TableField("MALL_LOGO_URL")
    private String mallLogoUrl;
    @ApiModelProperty(value = "商场品牌名称")
    @TableField("MALL_BRAND_NAME")
    private String mallBrandName;
    @ApiModelProperty(value = "是否有联域协议")
    @TableField("HAS_LINK_AGREEMENT")
    private Boolean hasLinkAgreement;
    @ApiModelProperty(value = "联域协议内容")
    @TableField("LINK_AGREEMENT_CONTENT")
    private String linkAgreementContent;
    @ApiModelProperty(value = "券资产使用规则")
    @TableField("COUPON_USE_RULE")
    private String couponUseRule;
    @ApiModelProperty(value = "联域协议标题")
    @TableField("LINK_AGREEMENT_TITLE")
    private String linkAgreementTitle;
    @ApiModelProperty(value = "使用规则标题")
    @TableField("USE_RULE_TITLE")
    private String useRuleTitle;
    @ApiModelProperty(value = "隐私协议标题")
    @TableField("PRIVACY_TITLE")
    private String privacyTitle;
    @ApiModelProperty(value = "券资产使用规则标题")
    @TableField("COUPON_USE_RULE_TITLE")
    private String couponUseRuleTitle;

    @ApiModelProperty(value = "是否能用停车券")
    @TableField("HAS_USE_PARKING_COUPON")
    private Boolean hasUseParkingCoupon;
    @ApiModelProperty(value = "停车券模版编号")
    @TableField("PARKING_COUPON_TEMPLATE_NO")
    private String parkingCouponTemplateNo;
    @ApiModelProperty(value = "停车券金额（元）")
    @TableField("PARKING_COUPON_AMOUNT")
    private BigDecimal parkingCouponAmount;
}
