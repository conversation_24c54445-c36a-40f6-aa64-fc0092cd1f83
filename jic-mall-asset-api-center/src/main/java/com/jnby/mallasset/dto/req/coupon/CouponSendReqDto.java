package com.jnby.mallasset.dto.req.coupon;

import com.google.common.base.Preconditions;
import com.jnby.mallasset.api.dto.BaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Builder
@ApiModel("发券入参")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CouponSendReqDto extends BaseReq implements Serializable {
    @ApiModelProperty("类型:1-停车券")
    private Integer type;
    @ApiModelProperty("模版号。停车券使用固定模版，此处可不传，预留给其他类型")
    private String templateNo;
    @ApiModelProperty("业务幂等ID")
    private String bizId;

    @Override
    public void check() {
        super.check();
        Preconditions.checkArgument(type != null || type == 1, "类型不能为空");
//        Preconditions.checkArgument(StringUtils.isNotBlank(couponTemplateNo), "券号不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(bizId), "业务幂等ID不能为空");
    }
}
