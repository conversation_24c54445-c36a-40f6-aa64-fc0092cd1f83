package com.jnby.mallasset.remote.longhu.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LongHuEnterpriseGrantResp {

    @ApiModelProperty(value = "业务系统ID")
    private String appId;

    @ApiModelProperty(value = "发放请求号")
    private String requestNo;

    @ApiModelProperty(value = "手机号")
    private String phoneNo;

    @ApiModelProperty(value = "发放状态 0-处理中 1-发放成功 2-发放失败 3-未注册待发放")
    private Integer status;

}
