#dubbo:
#  registry:
#    address: spring-cloud://localhost
#  consumer:
#    timeout: 3000
#    check: false
#    retries: 0
#  cloud:
#    subscribed-services: zlt-provider-dubbo



hystrix:
  command:
    default:
      execution:
        isolation:
          strategy: SEMAPHORE
          thread:
            timeoutInMilliseconds: 5000

ribbon:
  ReadTimeout: 10000  #ribbon读取超时时间，接口处理时间，不包括建立连接时间
  ConnectTimeout: 3000 #ribbon请求连接时间
  OkToRetryOnAllOperations: false #网关默认开启重试，此属性设置为false 只对GET请求重试，保证幂等性
  MaxAutoRetries: 1  #Max number of retries on the same server (excluding the first try)
  MaxAutoRetriesNextServer: 1 #Max number of next servers to retry (excluding the first server)
  ServerListRefreshInterval: 3000 # refresh the server list from the source

#shiro:
#  enabled: true

logging:
  level:
    com.alibaba.nacos.client.naming: WARN
    com.alibaba.nacos.client.config.impl: WARN 