package com.jnby.mallasset.strategy.platform.mallcoo;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.req.order.*;
import com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.module.service.IOrderBizService;
import com.jnby.mallasset.remote.dayuecheng.IDayuechengRemoteHttpApi;
import com.jnby.mallasset.remote.dingding.IDingDingRemoteHttpApi;
import com.jnby.mallasset.remote.dingding.entity.DingDingMsg;
import com.jnby.mallasset.remote.dingding.entity.DingDingResponse;
import com.jnby.mallasset.remote.kaide.entity.BaseOrderResponseEntity;
import com.jnby.mallasset.remote.kkmall.entity.SalesItem;
import com.jnby.mallasset.remote.kkmall.entity.SalesTender;
import com.jnby.mallasset.remote.kkmall.entity.SalesTotal;
import com.jnby.mallasset.remote.kkmall.entity.TransHeader;
import com.jnby.mallasset.remote.mallcoo.IMallCooPosRemoteHttpApi;
import com.jnby.mallasset.remote.mallcoo.IMallCooRemoteHttpApi;
import com.jnby.mallasset.remote.mallcoo.MallCooUtils;
import com.jnby.mallasset.remote.mallcoo.entity.*;
import com.jnby.mallasset.remote.mallcoo.entity.pos.GuangHuanArticleItems;
import com.jnby.mallasset.remote.mallcoo.entity.pos.GuangHuanUploadPosReq;
import com.jnby.mallasset.remote.mallcoo.entity.pos.GuangHuanUploadPosResp;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.DateUtil;
import com.jnby.mallasset.util.JsonUtil;
import com.jnby.mallasset.util.Md5Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 猫酷平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.MALL_COO, category = PlatformCategoryTypeEnum.ORDER)
@Slf4j
public class MallcooOrderStrategyService extends AbstractOrderService {

    @Autowired
    private IMallCooRemoteHttpApi mallCooRemoteHttpApi;
    @Autowired
    private CashierMallOrderLogMapper cashierMallOrderLogMapper;
    @Autowired
    private IDingDingRemoteHttpApi dingDingRemoteHttpApi;
    @Autowired
    private CashierMallMemberLogMapper cashierMallMemberLogMapper;
    @Autowired
    private MallcooMemberStrategyService mallcooMemberStrategyService;
    @Autowired
    private IOrderBizService orderBizService;
    @Autowired
    private IMallCooPosRemoteHttpApi mallCooPosRemoteHttpApi;
    @Autowired
    private IDayuechengRemoteHttpApi dayuechengRemoteHttpApi;

    @Override
    public ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("猫酷订单消费输入参数：{}", JSON.toJSONString(orderConsumeReq));
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = getCashierMallOrderLog(orderConsumeReq, orderConsumeReq.getStoreId(), cashierMallAssetStoreRef);
            if(cashierMallAssetStoreRef.getAllRefund() != null && cashierMallAssetStoreRef.getAllRefund().equals("Y")){
                // 处理颐堤港业务
                return handleYiDiGangBusiness(orderConsumeReq, cashierMallAssetStoreRef,cashierMallOrderLog);
            } else {
                // 处理其他商场业务
                return handleOtherMallBusiness(orderConsumeReq, cashierMallAssetStoreRef, cashierMallOrderLog);
            }
        }catch (Exception e){
            ResponseResult commonResponse = new ResponseResult();
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 猫酷订单消费加积分失败",orderConsumeReq.getOrdNum(),e);
            return commonResponse;
        }
    }

    private ResponseResult handleOtherMallBusiness(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef, CashierMallOrderLog cashierMallOrderLog) throws Exception {
        ResponseResult commonResponse = new ResponseResult();
        String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
        String platformPublicKey = cashierMallAssetStoreRef.getPlatformPublicKey();
        String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();

        PointsPlusByOrderReqEntity req = initPointsPlusByOrderReqEntity(orderConsumeReq, cashierMallAssetStoreRef);
        double promotionAmount = req.getPromotionAmount() == null ? 0.00 : req.getPromotionAmount();
        cashierMallOrderLog.setPromotionAmount(new BigDecimal(promotionAmount));
        log.info("猫酷订单消费调用远程接口参数：{}", JSON.toJSONString(req));
        Map<String, String> header = MallCooUtils.getHeaders(platformAppId, platformPublicKey, platformPrivateKey,JSON.toJSONString(req));
        Response<BaseMallCooResp<PointsPlusByOrderRespEntity>> execute;
        // 上海大悦城后面临时换了环境而做的调整
        if(orderConsumeReq.getStoreId() != null && (orderConsumeReq.getStoreId().equals("JDA22109") ||
                orderConsumeReq.getStoreId().equals("5DA26340") || orderConsumeReq.getStoreId().equals("9DA26327"))){
            execute = dayuechengRemoteHttpApi.pointsPlusByOrder(header, req).execute();
        }else{
            execute = mallCooRemoteHttpApi.pointsPlusByOrder(header, req).execute();
        }
        BaseMallCooResp<PointsPlusByOrderRespEntity> body = execute.body();

        if(body.getCode() == 1){
            commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
            PointsPlusByOrderRespEntity pointsPlusByOrderRespEntity = body.getData();
            pointsPlusByOrderRespEntity.setOrdNum(orderConsumeReq.getOrdNum());
            pointsPlusByOrderRespEntity.setExchangeId(orderConsumeReq.getOrdNum());
            pointsPlusByOrderRespEntity.setExchangeNo(orderConsumeReq.getOrdNum());
            commonResponse.setData(pointsPlusByOrderRespEntity);

            cashierMallOrderLog.setIsExecute("Y");
            cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
            // POS上传结算系统
            uploadOrderToSystem(orderConsumeReq, cashierMallAssetStoreRef, cashierMallOrderLog);
        }else {
            commonResponse.setCode(body.getCode());
        }
        commonResponse.setMsg(body.getMessage());
        log.info("猫酷订单消费返回参数：{}", JSON.toJSONString(commonResponse));
        return commonResponse;
    }

    private ResponseResult handleYiDiGangBusiness(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef,CashierMallOrderLog cashierMallOrderLog) throws Exception {
        ResponseResult commonResponse = new ResponseResult();
        String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
        String platformPublicKey = cashierMallAssetStoreRef.getPlatformPublicKey();
        String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
        String memberTel = orderConsumeReq.getMemberTel();
        String storeId = orderConsumeReq.getStoreId();

        CashierMallMemberLog cashierMallMemberLog = cashierMallMemberLogMapper.selectCashierMallMemberLog(memberTel, storeId);
        if(cashierMallMemberLog != null && cashierMallMemberLog.getHasChosePrivacy() != null && cashierMallMemberLog.getHasChosePrivacy()){
            MemberRegisterReq memberRegisterReq = new MemberRegisterReq();
            memberRegisterReq.setStoreId(storeId);
            memberRegisterReq.setMobile(memberTel);
            ResponseResult result = mallcooMemberStrategyService.openCard(memberRegisterReq, cashierMallAssetStoreRef);
            if(result.getCode() == ResultCodeEnum.SUCCESS.getCode()){
                // 开卡成功推送订单
                PointsPlusByOrderReqEntity req = initPointsPlusByOrderReqEntity(orderConsumeReq, cashierMallAssetStoreRef);
                // 判断支付金额是否0金额，0金额过滤直接返回成功
                ResponseResult responseResult = filterZeroOrder(orderConsumeReq, req, cashierMallOrderLog);
                if (responseResult != null){
                    return responseResult;
                }
                double promotionAmount = req.getPromotionAmount() == null ? 0.00 : req.getPromotionAmount();
                cashierMallOrderLog.setPromotionAmount(new BigDecimal(promotionAmount));
                log.info("猫酷订单消费调用远程接口参数：{}", JSON.toJSONString(req));
                Map<String, String> header = MallCooUtils.getHeaders(platformAppId, platformPublicKey, platformPrivateKey,JSON.toJSONString(req));
                Response<BaseMallCooResp<PointsPlusByOrderRespEntity>> execute = mallCooRemoteHttpApi.pointsPlusByOrder(header, req).execute();
                BaseMallCooResp<PointsPlusByOrderRespEntity> body = execute.body();
                if(body.getCode() == 1){
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    PointsPlusByOrderRespEntity pointsPlusByOrderRespEntity = body.getData();
                    pointsPlusByOrderRespEntity.setOrdNum(orderConsumeReq.getOrdNum());
                    pointsPlusByOrderRespEntity.setExchangeId(orderConsumeReq.getOrdNum());
                    pointsPlusByOrderRespEntity.setExchangeNo(orderConsumeReq.getOrdNum());
                    commonResponse.setData(pointsPlusByOrderRespEntity);
                    cashierMallOrderLog.setIsExecute("Y");
                    cashierMallOrderLog.setMemberType(1);
                    cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
                    // 会员打标
                    yiDiGangOrderMemberTag(orderConsumeReq.getMemberTel(),orderConsumeReq.getStoreId(), cashierMallAssetStoreRef);
                    // POS订单数据
                    pushYidigangPosOrder(orderConsumeReq);
                }else {
                    commonResponse.setCode(body.getCode());
                }
                commonResponse.setMsg(body.getMessage());
                log.info("猫酷订单消费返回参数：{}", JSON.toJSONString(commonResponse));
                return commonResponse;
            }else {
                commonResponse.setCode(result.getCode());
                commonResponse.setMsg(result.getMsg());
                commonResponse.setData(result.getData());
                log.info("猫酷订单消费返回参数：{}", JSON.toJSONString(commonResponse));
                return commonResponse;
            }
        }else {
            // 没有勾选协议不推订单，直接返回成功，并备注订单没有勾选协议
            cashierMallOrderLog.setIsExecute("Y");
            cashierMallOrderLog.setRemark("协议未勾选");
            cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);

            BaseOrderResponseEntity baseOrderResponse = new BaseOrderResponseEntity();
            baseOrderResponse.setOrdNum(orderConsumeReq.getOrdNum());
            baseOrderResponse.setExchangeId(orderConsumeReq.getOrdNum());
            baseOrderResponse.setExchangeNo(orderConsumeReq.getOrdNum());
            commonResponse.setData(baseOrderResponse);
            commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
            commonResponse.setMsg("协议未勾选");
            log.info("猫酷订单消费返回参数：{}", JSON.toJSONString(commonResponse));
            return commonResponse;
        }
    }

    private void pushYidigangPosOrder(OrderConsumeReq orderConsumeReq) {
        try{
            OrderPosReq orderPosReq = new OrderPosReq();
            orderPosReq.setOrdNum(orderConsumeReq.getOrdNum());
            orderPosReq.setConsumeTime(orderConsumeReq.getOrdFinishTime());
            orderPosReq.setOrdAmount(orderConsumeReq.getOrdAmount());
            orderPosReq.setStoreId(orderConsumeReq.getStoreId());
            orderBizService.posYiDiGangOrder(orderPosReq);
        }catch (Exception e){
            log.error("颐堤港POS订单数据失败：",e);
            pushDingErrorMsg(orderConsumeReq.getStoreId(), e);
        }
    }

    private ResponseResult filterZeroOrder(OrderConsumeReq orderConsumeReq, PointsPlusByOrderReqEntity req, CashierMallOrderLog cashierMallOrderLog) {
        if(req.getPayAmount() == 0.00){
            ResponseResult commonResponse = new ResponseResult();
            cashierMallOrderLog.setIsExecute("Y");
            cashierMallOrderLog.setRemark("0金额订单");
            cashierMallOrderLog.setIsZero("Y");
            cashierMallOrderLog.setMemberType(1);
            cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);

            BaseOrderResponseEntity baseOrderResponse = new BaseOrderResponseEntity();
            baseOrderResponse.setOrdNum(orderConsumeReq.getOrdNum());
            baseOrderResponse.setExchangeId(orderConsumeReq.getOrdNum());
            baseOrderResponse.setExchangeNo(orderConsumeReq.getOrdNum());
            commonResponse.setData(baseOrderResponse);
            commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
            commonResponse.setMsg("0金额订单");
            log.info("猫酷订单消费返回参数：{}", JSON.toJSONString(commonResponse));
            return commonResponse;
        }
        return null;
    }

    private void uploadOrderToSystem(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef mallAssetStoreRef, CashierMallOrderLog cashierMallOrderLog) {
        try{
            // 猫酷COCOPARK上传结算系统
            if(orderConsumeReq.getStoreId().equals("5DA52132")){
                CocoparkUploadOrderResp body = posCocoParkData(orderConsumeReq, mallAssetStoreRef);
                cashierMallOrderLog.setRemark("猫酷cocopark订单同步上传结算系统返回参数：" + body.getErrorCode() + body.getErrorMessage());
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
                log.info("猫酷COCOPARK订单同步上传结算系统返回参数：{}", JSON.toJSONString(body));
            }else if(orderConsumeReq.getStoreId().equals("5DA59533")){
                // 猫酷光环上传结算系统
                posGuangHuanOrderData(orderConsumeReq, mallAssetStoreRef);
            }
        }catch (Exception e){
            log.error("门店：{} 上传POS订单数据失败：",orderConsumeReq.getStoreId(),e);
            pushDingErrorMsg(orderConsumeReq.getStoreId(), e);
        }
        
    }

    private void pushDingErrorMsg(String storeId, Exception e) {
        String msg = "POS数据推送异常：\n" + "功能：猫酷POS订单数据" + "\n请求门店: \n" + storeId + "\n请求结果: \n" + e.getMessage();
        DingDingMsg dingDingMsg = new DingDingMsg();
        Map<String,Object> param = new HashMap<>();
        param.put("content",msg);
        dingDingMsg.setText(param);
        dingDingMsg.setMsgtype("text");
        try{
            Response<DingDingResponse> execute = dingDingRemoteHttpApi.pushMsg(dingDingMsg).execute();
            log.info("钉钉推送消息返回结果：{}",execute.body());
        }catch (Exception ex){
            log.error("钉钉推送消息失败：",ex);
        }
    }

    private void posGuangHuanOrderData(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef mallAssetStoreRef) throws IOException {
        GuangHuanUploadPosReq req = new GuangHuanUploadPosReq();
        String integralSkuCode = mallAssetStoreRef.getIntegralSkuCode();
        String[] items = integralSkuCode.split(",");
        String[] blocs = items[0].split("#");
        String bloc = blocs[1];
        String[] accessKeys = items[1].split("#");
        String accessKey = accessKeys[1];
        String[] accessSecrets = items[2].split("#");
        String accessSecret = accessSecrets[1];
        String[] collectionTerminalNos = items[3].split("#");
        String collectionTerminalNo = collectionTerminalNos[1];
        String[] tradeTypes = items[4].split("#");
        String tradeType = tradeTypes[1];

        req.setMember(true);
        req.setCollectionTerminalNo(collectionTerminalNo);
        req.setTicketNumber(orderConsumeReq.getOrdNum());
        req.setOffsetMark(true);
        req.setQty(orderConsumeReq.getTotQty());
        req.setDiscountAmount(BigDecimal.ZERO);
        String dateTime = DateUtil.timeStamp3Date(orderConsumeReq.getOrdFinishTime(),null);
        req.setTradeTime(dateTime);
        req.setTradeType(tradeType);
        req.setTradeAmount(orderConsumeReq.getOrdAmount());
        List<ProductItem> productItemList = orderConsumeReq.getProductItemList();
        List<GuangHuanArticleItems> articleItems = new ArrayList<>();
        productItemList.forEach(productItem -> {
            GuangHuanArticleItems item = new GuangHuanArticleItems();
            item.setName(productItem.getItemName());
            item.setQty(productItem.getItemQty());
            item.setAmount(productItem.getItemPrice());
            item.setPrice(productItem.getItemPrice().divide(new BigDecimal(productItem.getItemQty()),2, RoundingMode.HALF_DOWN));
            articleItems.add(item);
        });
        req.setArticleItems(articleItems);
        long timestamp = System.currentTimeMillis();
        String data = "/pod-open/s/v1/trade/savenew" + bloc + accessKey + timestamp + accessSecret;
        String sign = Md5Util.hex(Md5Util.md5(data));
        String url = "http://*************:9000/pod-open/s/v1/trade/savenew?bloc=" + bloc +"&accessKey=" + accessKey + "&timestamp=" + timestamp + "&sign=" + sign;
        try{
            log.info("光环门店POS订单数据：URL：{},传输参数：{}",url, JsonUtil.toJson(req));
            Response<GuangHuanUploadPosResp> response = mallCooPosRemoteHttpApi.posGuangHuanOrderData(url,req).execute();
            GuangHuanUploadPosResp body = response.body();
            log.info("光环门店POS订单数据返回参数：{}",JsonUtil.toJson(body));
            if(body.getRetCode() != 0){
                pushMsg(url + ":" + JsonUtil.toJson(req), JsonUtil.toJson(body));
            }
        }catch (Exception e){
            log.error("光环门店POS订单数据失败：",e);
            pushMsg(url + ":" + JsonUtil.toJson(req), "推送失败");
        }
    }

    private CocoparkUploadOrderResp posCocoParkData(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef mallAssetStoreRef) throws IOException {
        CocoparkUploadOrderInfo orderInfo = new CocoparkUploadOrderInfo();
        String integralSkuCode = mallAssetStoreRef.getIntegralSkuCode();
        String[] items = integralSkuCode.split(",");
        String[] tillIds = items[1].split("-");
        String tillId = tillIds[1];
        String[] apiKeys = items[4].split("-");
        String apiKey = apiKeys[1];
        String[] mallStoreCodes = items[5].split("-");
        String mallStoreCode = mallStoreCodes[1];
        String dateTime = DateUtil.timeStamp3Date(orderConsumeReq.getOrdFinishTime(),null);
        String day = dateTime.substring(0,10);
        orderInfo.setApiKey(apiKey);
        orderInfo.setSignature("");
        String docKey = day.concat(".").concat(mallStoreCode).concat(".").concat(tillId).concat(".").concat(orderConsumeReq.getOrdNum());
        orderInfo.setDocKey(docKey);
        TransHeader transHeader = new TransHeader();
        transHeader.setTxDate(day);
        transHeader.setLedgerDatetime(dateTime);
        transHeader.setStoreCode(mallStoreCode);
        transHeader.setTillId(tillId);
        transHeader.setDocNo(orderConsumeReq.getOrdNum());
        transHeader.setTxAttrib("");
        transHeader.setVoidDocNo("");
        orderInfo.setTransHeader(transHeader);

        List<ProductItem> productItemList = orderConsumeReq.getProductItemList();
        SalesTotal salesTotal = new SalesTotal();
        String[] cashiers = items[0].split("-");
        String cashier = cashiers[1];
        salesTotal.setCashier(cashier);
        salesTotal.setVipCode("");
        salesTotal.setNetQty(orderConsumeReq.getTotQty());
        salesTotal.setNetAmount(orderConsumeReq.getOrdAmount());
        salesTotal.setExtendParameter("");
        salesTotal.setCalculateVipBonus("");
        orderInfo.setSalesTotal(salesTotal);
        // 商品
        List<SalesItem> salesItem = new ArrayList<>();
        String[] itemCodes = items[2].split("-");
        String itemCode = itemCodes[1];
        for(int i=0; i< productItemList.size();i++){
            ProductItem productItem = productItemList.get(i);
            SalesItem item = new SalesItem();
            item.setSalesLineNumber(i+1);
            List<String> salesman = new ArrayList<>();
            salesman.add(cashier);
            item.setSalesman(salesman);
            item.setItemCode(itemCode);
            item.setItemOrgId("");
            item.setItemLotNum("*");
            item.setSerialNumber("");
            item.setInventoryType(0);
            item.setQty(1);
            item.setItemDiscountLess(0);
            item.setTotalDiscountLess(0);
            item.setNetAmount(productItem.getItemPrice().multiply(new BigDecimal(productItem.getItemQty())));
            item.setSalesItemRemark("");
            item.setExtendParameter("");
            salesItem.add(item);
        }
        orderInfo.setSalesItem(salesItem);
        // 付款
        String[] tenderCodes = items[3].split("-");
        String tenderCode = tenderCodes[1];
        List<PayItem> payItemList = orderConsumeReq.getPayItemList();
        List<SalesTender> salesTender = new ArrayList<>();
        payItemList.forEach(payItem -> {
            if(payItem.getId() == 62 || payItem.getId() == 231){
                SalesTender salesTender1 = new SalesTender();
                salesTender1.setBaseCurrencyCode("RMB");
                salesTender1.setTenderCode(tenderCode);
                salesTender1.setPayAmount(payItem.getPayamount());
                salesTender1.setBaseAmount(payItem.getPayamount());
                salesTender1.setExcessAmount(BigDecimal.ZERO);
                salesTender1.setExtendParameter("");
                salesTender.add(salesTender1);
            }
        });
        orderInfo.setSalesTender(salesTender);
        log.info("猫酷cocopark订单同步上传结算系统传输参数：{}", JSON.toJSONString(orderInfo));
        Response<CocoparkUploadOrderResp> response = mallCooRemoteHttpApi.uploadOrderInfo(orderInfo).execute();
        CocoparkUploadOrderResp body = response.body();
        if(body.getErrorCode() != 0){
            pushMsg(JsonUtil.toJson(orderInfo), JsonUtil.toJson(body));
        }
        return body;
    }

    private void pushMsg(String orderInfo, String body) throws IOException {
        String msg = "POS数据推送异常：\n" + "功能：猫酷POS订单数据" + "\n请求参数: \n" + orderInfo + "\n请求结果: \n" + body;
        DingDingMsg dingDingMsg = new DingDingMsg();
        Map<String,Object> param = new HashMap<>();
        param.put("content",msg);
        dingDingMsg.setText(param);
        dingDingMsg.setMsgtype("text");
        Response<DingDingResponse> execute = dingDingRemoteHttpApi.pushMsg(dingDingMsg).execute();
        log.info("推送消息返回结果：{}",execute.body());
    }

    private void pushDingMemberTagErrorMsg(String storeId, Exception e) {
        String msg = "颐堤港会员打标异常：\n" + "功能：颐堤港会员标识" + "\n门店: \n" + storeId + "\n请求结果: \n" + e.getMessage();
        DingDingMsg dingDingMsg = new DingDingMsg();
        Map<String,Object> param = new HashMap<>();
        param.put("content",msg);
        dingDingMsg.setText(param);
        dingDingMsg.setMsgtype("text");
        try{
            Response<DingDingResponse> execute = dingDingRemoteHttpApi.pushMsg(dingDingMsg).execute();
            log.info("颐堤港会员打标钉钉推送消息返回结果：{}",execute.body());
        }catch (Exception ex){
            log.info("颐堤港会员打标钉钉推送消息失败：",ex);
        }
    }

    private CashierMallOrderLog getCashierMallOrderLog(OrderConsumeReq orderConsumeReq, String storeId, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeNo(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setBjStoreId(storeId);
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(1);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    private PointsPlusByOrderReqEntity initPointsPlusByOrderReqEntity(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        PointsPlusByOrderReqEntity pointsPlusByOrderReqEntity = new PointsPlusByOrderReqEntity();
        if(cashierMallAssetStoreRef.getAllRefund() != null && cashierMallAssetStoreRef.getAllRefund().equals("Y")){
            // 颐堤港
            pointsPlusByOrderReqEntity.setMobile(orderConsumeReq.getMemberTel());
            pointsPlusByOrderReqEntity.setPayAmount(orderConsumeReq.getOrdAmount().doubleValue());
            pointsPlusByOrderReqEntity.setTotalAmount(orderConsumeReq.getOrdAmount().doubleValue());
            pointsPlusByOrderReqEntity.setMcShopID(Long.valueOf(cashierMallAssetStoreRef.getPlatformMallStoreId()));
            pointsPlusByOrderReqEntity.setTradeTime(DateUtil.timeStamp3Date(orderConsumeReq.getOrdFinishTime(),null));
            pointsPlusByOrderReqEntity.setTransID(orderConsumeReq.getOrdNum());
            pointsPlusByOrderReqEntity.setRemark("jnby");
            List<PayItem> payItemList = orderConsumeReq.getPayItemList();
            payItemList.forEach(payItem -> {
                if(payItem.getId() == 251){
                    pointsPlusByOrderReqEntity.setPromotionAmount(payItem.getPayamount().doubleValue());
                    pointsPlusByOrderReqEntity.setTotalAmount(orderConsumeReq.getOrdAmount().add(payItem.getPayamount()).doubleValue());
                }
            });
        }else {
            pointsPlusByOrderReqEntity.setMobile(orderConsumeReq.getMemberTel());
            pointsPlusByOrderReqEntity.setPayAmount(orderConsumeReq.getOrdAmount().doubleValue());
            pointsPlusByOrderReqEntity.setTotalAmount(orderConsumeReq.getOrdAmount().doubleValue());
            pointsPlusByOrderReqEntity.setMcShopID(Long.valueOf(cashierMallAssetStoreRef.getPlatformMallStoreId()));
            pointsPlusByOrderReqEntity.setTradeTime(DateUtil.timeStamp3Date(orderConsumeReq.getOrdFinishTime(),null));
            pointsPlusByOrderReqEntity.setTransID(orderConsumeReq.getOrdNum());
            pointsPlusByOrderReqEntity.setRemark("JNBY线上");
            pointsPlusByOrderReqEntity.setTradeSerialNo(orderConsumeReq.getOrdPayNo());
        }
        return pointsPlusByOrderReqEntity;
    }

    @Override
    public ResponseResult pushRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("猫酷订单退货输入参数：{}", JSON.toJSONString(orderRefundReq));
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderRefundReq, orderRefundReq.getStoreId(), cashierMallAssetStoreRef);

            if(cashierMallAssetStoreRef.getAllRefund() != null && cashierMallAssetStoreRef.getAllRefund().equals("Y")){
                // 颐堤港退款
                return handleYiDiGangRefundBusiness(orderRefundReq, cashierMallAssetStoreRef, cashierMallOrderLog);
            }else {
                // 其他商场退款
                return handleOtherMallRefundBusiness(orderRefundReq, cashierMallAssetStoreRef,cashierMallOrderLog);
            }
        }catch (Exception e){
            ResponseResult commonResponse = new ResponseResult();
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 猫酷订单售后退积分失败",orderRefundReq.getOrdNum(),e);
            return commonResponse;
        }
    }

    private ResponseResult handleOtherMallRefundBusiness(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef, CashierMallOrderLog cashierMallOrderLog) throws Exception {
        ResponseResult commonResponse = new ResponseResult();
        String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
        String platformPublicKey = cashierMallAssetStoreRef.getPlatformPublicKey();
        String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();

        PointsDeductionByRefundReqEntity req = initPointsDeductionByRefundReqEntity(orderRefundReq, cashierMallAssetStoreRef);
        log.info("猫酷订单退货调用远程接口参数：{}", JSON.toJSONString(req));
        Map<String, String> header = MallCooUtils.getHeaders(platformAppId, platformPublicKey, platformPrivateKey,JSON.toJSONString(req));
        Response<BaseMallCooResp<PointsDeductionByRefundRespEntity>> execute;
        // 上海大悦城后面临时换了环境而做的调整
        if(orderRefundReq.getStoreId() != null && (orderRefundReq.getStoreId().equals("JDA22109") ||
                orderRefundReq.getStoreId().equals("5DA26340") || orderRefundReq.getStoreId().equals("9DA26327"))){
            execute = dayuechengRemoteHttpApi.pointsDeductionByRefund(header, req).execute();
        }else{
            execute = mallCooRemoteHttpApi.pointsDeductionByRefund(header, req).execute();
        }
        BaseMallCooResp<PointsDeductionByRefundRespEntity> body = execute.body();

        if(body.getCode() == 1){
            commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
            PointsDeductionByRefundRespEntity pointsDeductionByRefundRespEntity = body.getData();
            pointsDeductionByRefundRespEntity.setOrdNum(orderRefundReq.getOrdNum());
            pointsDeductionByRefundRespEntity.setExchangeId(orderRefundReq.getRefundNo());
            pointsDeductionByRefundRespEntity.setExchangeNo(orderRefundReq.getRefundNo());
            commonResponse.setData(pointsDeductionByRefundRespEntity);

            cashierMallOrderLog.setIsExecute("Y");
            cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
            // POS商场订单数据
            posMallRefundOrderData(orderRefundReq, cashierMallAssetStoreRef, cashierMallOrderLog);
        }else {
            commonResponse.setCode(body.getCode());
        }
        commonResponse.setMsg(body.getMessage());
        log.info("猫酷订单退货返回参数：{}", JSON.toJSONString(commonResponse));
        return commonResponse;
    }

    private ResponseResult handleYiDiGangRefundBusiness(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef,CashierMallOrderLog cashierMallOrderLog) throws Exception {
        ResponseResult commonResponse = new ResponseResult();
        String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
        String platformPublicKey = cashierMallAssetStoreRef.getPlatformPublicKey();
        String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
        String ordNum = orderRefundReq.getOrdNum();
        CashierMallOrderLog orderLog = cashierMallOrderLogMapper.selectOrderLogByOrderNumAndExchangeId(ordNum, ordNum);
        // 说明是勾选协议的订单过去的
        if(orderLog != null && orderLog.getMemberType() != null && orderLog.getMemberType() == 1){
            // 判断是否0金额
            ResponseResult result = checkZeroRefundOrder(orderRefundReq, orderLog, cashierMallOrderLog);
            if (result != null) {
                return result;
            }
            PointsDeductionByRefundReqEntity req = initPointsDeductionByRefundReqEntity(orderRefundReq, cashierMallAssetStoreRef);
            log.info("猫酷订单退货调用远程接口参数：{}", JSON.toJSONString(req));
            Map<String, String> header = MallCooUtils.getHeaders(platformAppId, platformPublicKey, platformPrivateKey,JSON.toJSONString(req));
            Response<BaseMallCooResp<PointsDeductionByRefundRespEntity>> execute = mallCooRemoteHttpApi.pointsDeductionByRefund(header, req).execute();
            BaseMallCooResp<PointsDeductionByRefundRespEntity> body = execute.body();
            if(body.getCode() == 1){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                PointsDeductionByRefundRespEntity pointsDeductionByRefundRespEntity = body.getData();
                pointsDeductionByRefundRespEntity.setOrdNum(orderRefundReq.getOrdNum());
                pointsDeductionByRefundRespEntity.setExchangeId(orderRefundReq.getRefundNo());
                pointsDeductionByRefundRespEntity.setExchangeNo(orderRefundReq.getRefundNo());
                commonResponse.setData(pointsDeductionByRefundRespEntity);

                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
                // 颐堤港会员打标
                yiDiGangOrderMemberTag(orderRefundReq.getMemberTel(),orderRefundReq.getStoreId(), cashierMallAssetStoreRef);
                // POS订单数据
                posYidingangRefundOrderData(orderRefundReq);
            }else {
                commonResponse.setCode(body.getCode());
            }
            commonResponse.setMsg(body.getMessage());
            log.info("猫酷订单退货返回参数：{}", JSON.toJSONString(commonResponse));
        }else {
            BaseOrderResponseEntity baseOrderResponse = new BaseOrderResponseEntity();
            baseOrderResponse.setOrdNum(orderRefundReq.getOrdNum());
            baseOrderResponse.setExchangeId(orderRefundReq.getRefundNo());
            baseOrderResponse.setExchangeNo(orderRefundReq.getRefundNo());
            commonResponse.setData(baseOrderResponse);
            commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
            commonResponse.setMsg("协议未勾选");

            cashierMallOrderLog.setIsExecute("Y");
            cashierMallOrderLog.setRemark("协议未勾选");
            cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            log.info("猫酷订单退货返回参数：{}", JSON.toJSONString(commonResponse));
        }
        return commonResponse;
    }

    private void posYidingangRefundOrderData(OrderRefundReq orderRefundReq) {
        try{
            OrderPosReq orderPosReq = new OrderPosReq();
            orderPosReq.setStoreId(orderRefundReq.getStoreId());
            orderPosReq.setOrdNum(orderRefundReq.getRefundNo());
            orderPosReq.setConsumeTime(orderRefundReq.getRefundTime());
            orderPosReq.setOrdAmount(orderRefundReq.getRefundAmount().multiply(new BigDecimal(-1)));
            orderBizService.posYiDiGangOrder(orderPosReq);
        }catch (Exception e){
            log.error("颐堤港POS订单数据失败：",e);
            pushDingErrorMsg(orderRefundReq.getStoreId(), e);
        }
    }

    private ResponseResult checkZeroRefundOrder(OrderRefundReq orderRefundReq, CashierMallOrderLog orderLog, CashierMallOrderLog cashierMallOrderLog) {
        if(orderLog.getIsZero() != null && orderLog.getIsZero().equals("Y")){
            ResponseResult commonResponse = new ResponseResult();
            BaseOrderResponseEntity baseOrderResponse = new BaseOrderResponseEntity();
            baseOrderResponse.setOrdNum(orderRefundReq.getOrdNum());
            baseOrderResponse.setExchangeId(orderRefundReq.getRefundNo());
            baseOrderResponse.setExchangeNo(orderRefundReq.getRefundNo());
            commonResponse.setData(baseOrderResponse);
            commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
            commonResponse.setMsg("0金额订单");

            cashierMallOrderLog.setIsExecute("Y");
            cashierMallOrderLog.setRemark("0金额订单");
            cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            log.info("猫酷订单退货返回参数：{}", JSON.toJSONString(commonResponse));
            return commonResponse;
        }
        return null;
    }

    private void posMallRefundOrderData(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef,CashierMallOrderLog cashierMallOrderLog) {
        try{
            if(orderRefundReq.getStoreId().equals("5DA52132")){
                // 颐堤港pos单据数据
                posCocoParkRefundOrderData(orderRefundReq, assetStoreRef, cashierMallOrderLog);
            }else if(orderRefundReq.getStoreId().equals("5DA59533")){
                // 光环pos单据数据
                posGuangHuanRefundOrderData(orderRefundReq, assetStoreRef);
            }
        }catch (Exception e){
            log.error("门店：{} 上传POS订单数据失败：",orderRefundReq.getStoreId(),e);
            pushDingErrorMsg(orderRefundReq.getStoreId(), e);
        }
    }

    private void posGuangHuanRefundOrderData(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef) {
        GuangHuanUploadPosReq req = new GuangHuanUploadPosReq();
        String integralSkuCode = assetStoreRef.getIntegralSkuCode();
        String[] items = integralSkuCode.split(",");
        String[] blocs = items[0].split("#");
        String bloc = blocs[1];
        String[] accessKeys = items[1].split("#");
        String accessKey = accessKeys[1];
        String[] accessSecrets = items[2].split("#");
        String accessSecret = accessSecrets[1];
        String[] collectionTerminalNos = items[3].split("#");
        String collectionTerminalNo = collectionTerminalNos[1];
        String[] tradeTypes = items[5].split("#");
        String tradeType = tradeTypes[1];

        req.setMember(true);
        req.setCollectionTerminalNo(collectionTerminalNo);
        req.setTicketNumber(orderRefundReq.getRefundNo());
        req.setOffsetMark(true);
        req.setDiscountAmount(BigDecimal.ZERO);
        String dateTime = DateUtil.timeStamp3Date(orderRefundReq.getRefundTime(),null);
        req.setTradeTime(dateTime);
        req.setTradeType(tradeType);
        req.setTradeAmount(orderRefundReq.getRefundAmount().multiply(new BigDecimal(-1)));
        List<ProductItem> productItemList = orderRefundReq.getProductItemList();
        List<GuangHuanArticleItems> articleItems = new ArrayList<>();
        int qty = 0;
        for (ProductItem productItem : productItemList) {
            GuangHuanArticleItems item = new GuangHuanArticleItems();
            item.setName(productItem.getItemName());
            item.setQty(productItem.getItemQty());
            item.setAmount(productItem.getItemPrice().multiply(new BigDecimal(-1)));
            item.setPrice(productItem.getItemPrice().divide(new BigDecimal(productItem.getItemQty()),2, RoundingMode.HALF_DOWN));
            articleItems.add(item);
            qty += productItem.getItemQty();
        }
        req.setQty(qty);
        req.setArticleItems(articleItems);
        long timestamp = System.currentTimeMillis();
        String data = "/pod-open/s/v1/trade/savenew" + bloc + accessKey + timestamp + accessSecret;
        String sign = Md5Util.hex(Md5Util.md5(data));
        String url = "http://*************:9000/pod-open/s/v1/trade/savenew?bloc=" + bloc +"&accessKey=" + accessKey + "&timestamp=" + timestamp + "&sign=" + sign;
        try{
            log.info("光环门店POS退货订单数据：URL：{},传输参数：{}",url, JsonUtil.toJson(req));
            Response<GuangHuanUploadPosResp> response = mallCooPosRemoteHttpApi.posGuangHuanOrderData(url,req).execute();
            GuangHuanUploadPosResp body = response.body();
            log.info("光环门店POS退货订单数据返回参数：{}",JsonUtil.toJson(body));
            if(body.getRetCode() != 0){
                pushMsg(url + ":" + JsonUtil.toJson(req), JsonUtil.toJson(body));
            }
        }catch (Exception e){
            log.error("光环门店POS退货订单数据失败：",e);
            try{
                pushMsg(url + ":" + JsonUtil.toJson(req), "推送失败");
            }catch (Exception ex){
                log.error("光环门店推送订单消息失败：",ex);
            }
        }
    }

    private void posCocoParkRefundOrderData(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef, CashierMallOrderLog cashierMallOrderLog) throws IOException {
        String refundNo = orderRefundReq.getRefundNo();
        CocoparkUploadOrderInfo orderInfo = new CocoparkUploadOrderInfo();
        String integralSkuCode = assetStoreRef.getIntegralSkuCode();
        String[] items = integralSkuCode.split(",");
        String[] tillIds = items[1].split("-");
        String tillId = tillIds[1];
        String[] apiKeys = items[4].split("-");
        String apiKey = apiKeys[1];
        String[] mallStoreCodes = items[5].split("-");
        String mallStoreCode = mallStoreCodes[1];
        String dateTime = DateUtil.timeStamp3Date(orderRefundReq.getRefundTime(),null);
        String day = dateTime.substring(0,10);

        orderInfo.setApiKey(apiKey);
        orderInfo.setSignature("");
        String docKey = day.concat(".").concat(mallStoreCode).concat(".").concat(tillId).concat(".").concat(refundNo);
        orderInfo.setDocKey(docKey);
        TransHeader transHeader = new TransHeader();
        transHeader.setTxDate(day);
        transHeader.setLedgerDatetime(dateTime);
        transHeader.setStoreCode(mallStoreCode);
        transHeader.setTillId(tillId);
        transHeader.setDocNo(refundNo);
        transHeader.setTxAttrib("");
        transHeader.setVoidDocNo("");
        orderInfo.setTransHeader(transHeader);

        List<ProductItem> productItemList = orderRefundReq.getProductItemList();
        SalesTotal salesTotal = new SalesTotal();
        String[] cashiers = items[0].split("-");
        String cashier = cashiers[1];
        salesTotal.setCashier(cashier);
        salesTotal.setVipCode("");
        salesTotal.setNetQty(-productItemList.size());
        salesTotal.setNetAmount(orderRefundReq.getRefundAmount().abs().multiply(new BigDecimal(-1)));
        salesTotal.setExtendParameter("");
        salesTotal.setCalculateVipBonus("");
        orderInfo.setSalesTotal(salesTotal);
        // 商品
        List<SalesItem> salesItem = new ArrayList<>();
        String[] itemCodes = items[2].split("-");
        String itemCode = itemCodes[1];
        for(int i=0;i < productItemList.size(); i++){
            ProductItem productItem = productItemList.get(i);
            SalesItem item = new SalesItem();
            item.setSalesLineNumber(i+1);
            List<String> salesman = new ArrayList<>();
            salesman.add(cashier);
            item.setSalesman(salesman);
            item.setItemCode(itemCode);
            item.setItemOrgId("");
            item.setItemLotNum("*");
            item.setSerialNumber("");
            item.setInventoryType(0);
            item.setQty(productItem.getItemQty());
            item.setItemDiscountLess(0);
            item.setTotalDiscountLess(0);
            item.setNetAmount(productItem.getItemPrice().abs().multiply(new BigDecimal(-1)));
            item.setSalesItemRemark("");
            item.setExtendParameter("");
            salesItem.add(item);
        }
        orderInfo.setSalesItem(salesItem);
        // 付款
        String[] tenderCodes = items[3].split("-");
        String tenderCode = tenderCodes[1];
        List<PayItem> payItemList = orderRefundReq.getPayItemList();
        List<SalesTender> salesTender = new ArrayList<>();
        payItemList.forEach(payItem -> {
            if(payItem.getId() == 62 || payItem.getId() == 231){
                SalesTender salesTender1 = new SalesTender();
                salesTender1.setBaseCurrencyCode("RMB");
                salesTender1.setTenderCode(tenderCode);
                salesTender1.setPayAmount(payItem.getPayamount());
                salesTender1.setBaseAmount(payItem.getPayamount());
                salesTender1.setExcessAmount(BigDecimal.ZERO);
                salesTender1.setExtendParameter("");
                salesTender.add(salesTender1);
            }
        });
        orderInfo.setSalesTender(salesTender);
        log.info("猫酷COCOPARK订单退货上传结算系统传输参数：{}", JSON.toJSONString(orderInfo));
        Response<CocoparkUploadOrderResp> response = mallCooRemoteHttpApi.uploadOrderInfo(orderInfo).execute();
        CocoparkUploadOrderResp resp = response.body();
        if(resp.getErrorCode() != 0){
            pushMsg(JsonUtil.toJson(orderInfo), JsonUtil.toJson(resp));
        }
        cashierMallOrderLog.setRemark("猫酷COCOPARK订单售后上传结算系统返回参数：" + resp.getErrorCode() + resp.getErrorMessage());
        cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
        log.info("猫酷COCOPARK订单退货上传结算系统返回参数：{}", JSON.toJSONString(resp));
    }

    private void yiDiGangOrderMemberTag(String memberTel,String storeId, CashierMallAssetStoreRef assetStoreRef) {
        try{
            MemberTagReqEntity memberTagReqEntity = new MemberTagReqEntity();
            memberTagReqEntity.setMobile(memberTel);
            List<Long> tagIDList = new ArrayList<>();
            tagIDList.add(22154L);
            memberTagReqEntity.setTagIDList(tagIDList);
            memberTagReqEntity.setDesc("jnby");
            Map<String, String> header = MallCooUtils.getHeaders(assetStoreRef.getPlatformAppId(),assetStoreRef.getPlatformPublicKey(),assetStoreRef.getPlatformPrivateKey(), JSON.toJSONString(memberTagReqEntity));
            Response<BaseMallCooResp> baseMallCooRespResponse = mallCooRemoteHttpApi.memberTag(header,memberTagReqEntity).execute();
            BaseMallCooResp baseMallCooResp = baseMallCooRespResponse.body();
            if(baseMallCooResp.getCode() != 1){
                pushDingDingMsg(header, memberTagReqEntity, baseMallCooResp);
            }
            log.info("颐堤港会员打标结果：{}",JSON.toJSONString(baseMallCooRespResponse.body()));
        }catch (Exception e){
            log.error("颐堤港会员打标失败：",e);
            pushDingMemberTagErrorMsg(storeId, e);
        }
    }

    private void pushDingDingMsg(Map<String, String> header, MemberTagReqEntity memberTagReqEntity, BaseMallCooResp baseMallCooResp) throws IOException {
        String msg = "数据推送异常：\n" + "功能：颐堤港会员打标" + "\n请求参数: \n" + JSON.toJSON(header) + "," + JSON.toJSON(memberTagReqEntity) + "\n请求结果: \n" + JSON.toJSON(baseMallCooResp);
        DingDingMsg dingDingMsg = new DingDingMsg();
        Map<String,Object> param = new HashMap<>();
        param.put("content",msg);
        dingDingMsg.setText(param);
        dingDingMsg.setMsgtype("text");
        Response<DingDingResponse> execute = dingDingRemoteHttpApi.pushMsg(dingDingMsg).execute();
        log.info("推送消息返回结果：{}",execute.body());
    }

    @Override
    public ResponseResult prepareRefundOrder(OrderRefundReq OrderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderRefundReq OrderRefundReq, String storeId, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(OrderRefundReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(OrderRefundReq.getRefundNo());
        cashierMallOrderLog.setExchangeNo(OrderRefundReq.getRefundNo());
        cashierMallOrderLog.setBjStoreId(storeId);
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(2);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    private PointsDeductionByRefundReqEntity initPointsDeductionByRefundReqEntity(OrderRefundReq OrderRefundReq, CashierMallAssetStoreRef assetStoreRef) {
        PointsDeductionByRefundReqEntity pointsDeductionByRefundReqEntity = new PointsDeductionByRefundReqEntity();
        if(assetStoreRef.getAllRefund() != null && assetStoreRef.getAllRefund().equals("Y")){
            // 颐堤港
            String tradeTime = DateUtil.timeStamp3Date(OrderRefundReq.getOrdFinishTime(),null);
            pointsDeductionByRefundReqEntity.setAmount(OrderRefundReq.getOrdAmount().doubleValue());
            pointsDeductionByRefundReqEntity.setMcShopID(Long.valueOf(assetStoreRef.getPlatformMallStoreId()));
            pointsDeductionByRefundReqEntity.setReturnAmount(OrderRefundReq.getRefundAmount().doubleValue());
            pointsDeductionByRefundReqEntity.setReturnTradeID(OrderRefundReq.getOrdNum());
            pointsDeductionByRefundReqEntity.setTradeTime(tradeTime);
        }else {
            String tradeTime = DateUtil.timeStamp3Date(OrderRefundReq.getOrdFinishTime(),null);
            pointsDeductionByRefundReqEntity.setAmount(OrderRefundReq.getOrdAmount().doubleValue());
            pointsDeductionByRefundReqEntity.setMcShopID(Long.valueOf(assetStoreRef.getPlatformMallStoreId()));
            pointsDeductionByRefundReqEntity.setReturnAmount(OrderRefundReq.getRefundAmount().doubleValue());
            pointsDeductionByRefundReqEntity.setReturnTradeID(OrderRefundReq.getOrdNum());
            pointsDeductionByRefundReqEntity.setTradeTime(tradeTime);
            pointsDeductionByRefundReqEntity.setTradeSerialNo(OrderRefundReq.getOrdPayNo());
        }
        return pointsDeductionByRefundReqEntity;
    }
}
