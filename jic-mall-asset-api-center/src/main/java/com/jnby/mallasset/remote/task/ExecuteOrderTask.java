package com.jnby.mallasset.remote.task;


import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.dto.req.OrderCallBackRequest;
import com.jnby.mallasset.dto.res.OrderCallBackResponse;
import com.jnby.mallasset.dto.res.Query;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.remote.callback.IOrderCallbackRemoteHttpApi;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
@AllArgsConstructor
public class ExecuteOrderTask {

    private CashierMallOrderLogMapper cashierMallOrderLogMapper;
    private IOrderCallbackRemoteHttpApi orderCallbackRemoteHttpApi;

    public String execute(){
        log.info("订单回调定时任务开始执行");
        try{
            List<CashierMallOrderLog> cashierMallOrderLogList = cashierMallOrderLogMapper.selectMallOrderLogList();
            log.info("订单回调定时任务执行[{}]条数据",cashierMallOrderLogList.size());
            if(!cashierMallOrderLogList.isEmpty()){
                for(CashierMallOrderLog cashierMallOrderLog : cashierMallOrderLogList){
                    OrderCallBackRequest orderCallBackRequest = new OrderCallBackRequest();
                    orderCallBackRequest.setResult(1);
                    Integer mallPlatform = cashierMallOrderLog.getMallPlatform();
                    String bjStoreId = cashierMallOrderLog.getBjStoreId();
                    if(mallPlatform != null && mallPlatform.equals(PlatformTypeEnum.KAI_DE.getCode())
                        || bjStoreId.equals("2DA00129") || bjStoreId.equals("5DA00141") || bjStoreId.equals("9DA00111")){
                        String remark = cashierMallOrderLog.getRemark();
                        orderCallBackRequest.setMsg(remark != null ? remark : ResultCodeEnum.SUCCESS.getMsg());
                    }else {
                        orderCallBackRequest.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                    }
                    orderCallBackRequest.setReturnTime(DateUtil.formatDateTime(new Date()));
                    Query query = new Query();
                    query.setOrdNum(cashierMallOrderLog.getOrdNum());
                    query.setExchangeId(cashierMallOrderLog.getExchangeId());
                    query.setExchangeNo(cashierMallOrderLog.getExchangeNo());
                    query.setStoreCode(cashierMallOrderLog.getBjStoreId());
                    orderCallBackRequest.setQuery(query);
                    // 消费
                    if(cashierMallOrderLog.getType() == 1){
                        Response<OrderCallBackResponse> execute = orderCallbackRemoteHttpApi.orderCallback(orderCallBackRequest).execute();
                        OrderCallBackResponse response = execute.body();
                        log.info("回调返回结果：{}",JSON.toJSONString(response));
                        if(response.getCode() == 1){
                            // 更新为已回调
                            cashierMallOrderLog.setIsCallback("Y");
                            cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
                        }
                    }else if(cashierMallOrderLog.getType() == 2){
                        // 售后
                        Response<OrderCallBackResponse> result = orderCallbackRemoteHttpApi.orderRefundCallback(orderCallBackRequest).execute();
                        OrderCallBackResponse response = result.body();
                        log.info("回调返回结果：{}",JSON.toJSONString(response));
                        if(response.getCode() == 1){
                            // 更新为已回调
                            cashierMallOrderLog.setIsCallback("Y");
                            cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
                        }
                    }else if(cashierMallOrderLog.getType() == 3){
                        // 预退货
                        Response<OrderCallBackResponse> result = orderCallbackRemoteHttpApi.orderRefundCallback(orderCallBackRequest).execute();
                        OrderCallBackResponse response = result.body();
                        log.info("回调返回结果：{}",JSON.toJSONString(response));
                        if(response.getCode() == 1){
                            // 更新为已回调
                            cashierMallOrderLog.setIsCallback("Y");
                            cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
                        }
                    }
                }
                return "success";
            }
            log.info("订单回调定时任务执行结束");
            return "none";
        }catch (Exception e) {
            log.error("订单回调定时任务执行失败",e);
            return "fail";
        }
    }
}
