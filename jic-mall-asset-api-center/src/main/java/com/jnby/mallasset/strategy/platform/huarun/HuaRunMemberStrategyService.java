package com.jnby.mallasset.strategy.platform.huarun;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.api.AssetController;
import com.jnby.mallasset.api.dto.asset.AssetUseRespDto;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.coupon.CouponSendReqDto;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetConfig;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.module.service.IAssetBizService;
import com.jnby.mallasset.remote.huarun.IHuaRunRemoteHttpApi;
import com.jnby.mallasset.remote.huarun.entity.BaseHuaRunResp;
import com.jnby.mallasset.remote.huarun.entity.MemberRegisterReqEntity;
import com.jnby.mallasset.remote.huarun.entity.MemberRegisterRespEntity;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 华润会员平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.HUA_RUN, category = PlatformCategoryTypeEnum.MEMBER)
@Slf4j
public class HuaRunMemberStrategyService extends AbstractMemberService {

    @Autowired
    private IHuaRunRemoteHttpApi huaRunRemoteHttpApi;
    @Autowired
    private CashierMallMemberLogMapper cashierMallMemberLogMapper;
    @Autowired
    private HuaRunLoginService huaRunLoginStrategyService;
    @Autowired
    private IAssetBizService assetBizService;
    @Autowired
    private AssetController assetController;

    @Override
    public ResponseResult openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("华润会员注册输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            String storeId = memberRegisterReq.getStoreId();
            String mobile = memberRegisterReq.getMobile();
            // 记录流水
            CashierMallMemberLog cashierMallMemberLog = recordCashierMallMemberLog(cashierMallAssetStoreRef, storeId, mobile);

            String userToken = huaRunLoginStrategyService.getUserToken(storeId);
            Map<String, String> header = new HashMap<>();
            header.put("Authorization","Bearer " + userToken);

            MemberRegisterReqEntity req = new MemberRegisterReqEntity();
            req.setMemberSource("微信");
            req.setMemberSex(memberRegisterReq.getSex() == 1 ? "男" : "女");
            req.setMemberTel(mobile);
            req.setMemberName(memberRegisterReq.getNickName());
            log.info("华润会员注册调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseHuaRunResp<MemberRegisterRespEntity>> execute = huaRunRemoteHttpApi.memberRegister(header,req).execute();
            BaseHuaRunResp<MemberRegisterRespEntity> body = execute.body();
            // 注册成功
            CommonMemberResponse commonMemberResponse = new CommonMemberResponse();
            if(body.getResult() == 1){
                int memberFlag = body.getQuery().getMemberFlag();
                commonMemberResponse.setMemberFlag(memberFlag);
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                // 设置执行标识
                cashierMallMemberLog.setIsExecute("Y");
                cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
            }else {
                // 老会员
                if(body.getResult() == 104){
                    commonMemberResponse.setMemberFlag(1);
                }else {
                    commonMemberResponse.setMemberFlag(2);
                }
                commonResponse.setCode(body.getResult());
            }
            commonResponse.setData(commonMemberResponse);
            commonResponse.setMsg(body.getMsg());
            log.info("华润会员注册返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 华润会员注册开卡失败",memberRegisterReq.getMobile(),e);
        }
        // 判断沈万的新会员发券
        if (memberRegisterReq.getIsNew() != null && Objects.equals("Y", memberRegisterReq.getIsNew())){
            sendCarVoucher(memberRegisterReq, cashierMallAssetStoreRef);
        }
        return commonResponse;
    }

    private void sendCarVoucher(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        try{
            if(cashierMallAssetStoreRef.getHasUseParkingCoupon() != null && cashierMallAssetStoreRef.getHasUseParkingCoupon()){
                if(memberRegisterReq.getIsNew() != null && memberRegisterReq.getIsNew().equals("Y")){
                    log.info("[{}]门店新会员[{}]发放停车券",memberRegisterReq.getStoreId(),memberRegisterReq.getMobile());
                    CouponSendReqDto couponSendReqDto = new CouponSendReqDto();
                    couponSendReqDto.setBizId(StringUtil.randomUUID());
                    couponSendReqDto.setCustomerId(memberRegisterReq.getMobile());
                    couponSendReqDto.setStoreId(memberRegisterReq.getStoreId());
                    couponSendReqDto.setType(1);
//                    ResponseResult<AssetUseRespDto> res = assetBizService.sendCoupon(couponSendReqDto);
                    ResponseResult<AssetUseRespDto> res = assetController.sendCoupon(couponSendReqDto);
                    log.info("[{}]门店新会员[{}]发放停车券返回参数：{}",memberRegisterReq.getStoreId(),memberRegisterReq.getMobile(), JSONObject.toJSONString(res));
                }
            }
        }catch (Exception e){
            log.error("[{}][{}]发放停车券失败", memberRegisterReq.getStoreId(),memberRegisterReq.getMobile(),e);
        }
    }

    private CashierMallMemberLog recordCashierMallMemberLog(CashierMallAssetStoreRef cashierMallAssetStoreRef, String storeId, String mobile) {
        CashierMallMemberLog cashierMallMemberLog = new CashierMallMemberLog();
        cashierMallMemberLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallMemberLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallMemberLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallMemberLog.setBjStoreId(storeId);
        cashierMallMemberLog.setMobile(mobile);
        cashierMallMemberLog.setIsExecute("N");
        cashierMallMemberLogMapper.insertEntity(cashierMallMemberLog);
        return cashierMallMemberLog;
    }

    @Override
    public ResponseResult getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }
}
