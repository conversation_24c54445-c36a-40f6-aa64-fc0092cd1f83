package com.jnby.mallasset.api.dto.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("会员回参")
@Data
public class MemberInfoRespDto implements Serializable {
    @ApiModelProperty("商场会员")
    private Boolean hasMallMember;
    @ApiModelProperty("勾选过开卡和隐私政策")
    private Boolean hasChosePrivacy;
}
