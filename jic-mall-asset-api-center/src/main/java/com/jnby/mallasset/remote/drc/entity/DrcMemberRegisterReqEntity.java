package com.jnby.mallasset.remote.drc.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DrcMemberRegisterReqEntity.java
 * 文件简介: 会员注册请求
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class DrcMemberRegisterReqEntity implements Serializable {
    @ApiModelProperty(value = "商场ID", required = true)
    private String mall_id;
    @ApiModelProperty(value = "会员手机号", required = true)
    private String member_phone;
    @ApiModelProperty(value = "数据来源", required = true)
    private String data_source;
    @ApiModelProperty(value = "操作人", required = true)
    private String operators;
}
