package com.jnby.mallasset.remote.yintai.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * {
 *     "vipId": "1828612448236625922",
 *     "mobile": "15546341352",
 *     "name": "微**户",
 *     "registerTime": "2024-08-28T09:55:56",
 *     "score": 0,
 *     "levelName": "普卡",
 *     "levelNum": "1"
 * }
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class YinTaiMemberRespEntity {
    @ApiModelProperty(value = "会员手机号")
    private String mobile;
    @ApiModelProperty(value = "会员卡号")
    private String vipId;
    @ApiModelProperty(value = "会员姓名")
    private String name;
    @ApiModelProperty(value = "注册时间")
    private String registerTime;
    @ApiModelProperty(value = "积分")
    private int score;
    @ApiModelProperty(value = "会员等级")
    private String levelName;
    @ApiModelProperty(value = "会员等级")
    private String levelNum;
}
