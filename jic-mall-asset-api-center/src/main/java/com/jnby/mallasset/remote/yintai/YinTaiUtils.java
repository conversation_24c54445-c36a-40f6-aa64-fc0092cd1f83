package com.jnby.mallasset.remote.yintai;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.gitee.sop.sdk.client.OpenClient;
import com.gitee.sop.sdk.request.CommonRequest;
import com.gitee.sop.sdk.response.CommonResponse;
import com.jnby.mallasset.remote.yintai.entity.TransPaying;
import com.jnby.mallasset.remote.yintai.entity.TransProduct;
import com.jnby.mallasset.remote.yintai.entity.YinTaiBaseRespEntity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

public class YinTaiUtils {

    public final static String yinTaiUrl = "https://testopen.yintaiblt.com";
    public final static String platformAppId = "202407151262461247379472384";
    public final static String platformPrivateKey = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDSDbDG9SBLxTW+BHOoYHge3N7q7Hc8AXw3mr+eZpwA1ZvxmXxAQnr4crTJXBReZwo8SwUuGV3VRBbnHdstSry7RtfNtLe/aHcwoY7h58/cKQiOJR61BY+ZE3GqNX+7SmCfWVGV6LnEf+DlTARQWRkadDmsgbTT4V580hbWi9aNAS/0N9f54YqeS4OPglAmyQzO4eDr7QWbfJC0PSBJJ5iruC07eRvNvNP4kScJ5BpQ1noMxTjtA0Hwi+IaM79xU6cqxiaJdWEh53Nu/zLkBUudQSdUQEl5To6hFXgNsF7wZ1s/xyEA8JRaHNSfcnJRmBHpqrUH39P9hTkR4Ap73PcJAgMBAAECggEBAMWzcwXfoxUNuYZuBDmfCo/pzLoJ9DN7JmHjeaTp6Lz0UAE2N/b3W2xJ8hRvYRB1JPIYnfiEkYdvvaqrtCXN6RLPG8CV7jfuZ/hSpvvCxiXuTQoUr/ErGbu2zE2qat1ppMV2OM7Cq03iGldWk9zX70OxzrKppfuia99JkYs/NKGins+siwqwO/k2KAqzhIENadkjhcG9CSN/tsqrZZVCNEkyHKo2UFaUFcCWfgQ/WNhRqZlQYNGsVnnJ+bu5kY4I0YHDw4oLZ3MWRuq614+OKRx6N3ohDD++u8x2h0eXcfoktQnwAlS20owOuVVeHI65nmwQg50cDWQ6dg4TZZQr/XECgYEA7WswmaEiWkHhBJtcxvkaXbHWRXVGK+R/3lDlqWetAcH2CPvHicFDmhXqjVX6UFNrglzOzj19ALqLgQPJoUFVPnPQ5vu/2nmjGXny+uITllJpIiDBZ3qYDf6QOW+l6ri4I6eaBP6IhKPQBMvA4mQlUFf9H/97P9iCnqFroyFg+QUCgYEA4n45RZigWYvXI+GTeaVVn8rm6lNTX28Gx95AJr/Hmn0thYq0k8AyzaK8Yv/YQPqhg776cBKMfe/ZD85daa+1O9PI1twuqmjgjv5GVWPFWxaPzl3YxpM60H0xF0wf0j5BbxbEWg6Lm3F7zKKPolwG37Whx7m8Cw1iJjJgpEOsFTUCgYBwhDM1K8NPCReuCj8u4RFYxBYrNsf5t0HOR4KABW1dfuGuIjzPkTMLjEVrlAqcFNzHnFo6LbPXK5E/pF7jOEZA4bxoK3kh/jGIul2n/oqP3CCNFv7xnoz/XNxXDA2SrtY9t4lrwoFKcyP7keCoqhNw/zIr/vUXLfHBYfjUB9agTQKBgQCQAn3b2I2sIhQrBlTEtfFZZqOoPk72kRuYnaHggXQisJDvJiBnCX/D7EMoF1CjEDj+EcIe3ExkDsrPGAGhH012utQqeVlalNCjWt3RkVx87D2Hk3gv2pE82z0JeSeKgHcOcUVrlXAC6Ac1N+9n5megelZhLaF4zYuoicOU7kpOOQKBgQCL6Q1lDyzTjHcEbq+4ooMvpWnnMr5YnITwyn2YV/Q/8dDfoVhxCqzuHmkkbpyEM/ONaNjyyLW/TGYGde1GA7Dt9VsbwXDFwWlDGryufvWYz3Ll/f0alxewS2VgjhiQ3qM7xypKoPL+XxjbVD2s6tMsJp8taxAOAwuMAB19y+HiSQ==";
    public final static String platformPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0g2wxvUgS8U1vgRzqGB4Htze6ux3PAF8N5q/nmacANWb8Zl8QEJ6+HK0yVwUXmcKPEsFLhld1UQW5x3bLUq8u0bXzbS3v2h3MKGO4efP3CkIjiUetQWPmRNxqjV/u0pgn1lRlei5xH/g5UwEUFkZGnQ5rIG00+FefNIW1ovWjQEv9DfX+eGKnkuDj4JQJskMzuHg6+0Fm3yQtD0gSSeYq7gtO3kbzbzT+JEnCeQaUNZ6DMU47QNB8IviGjO/cVOnKsYmiXVhIedzbv8y5AVLnUEnVEBJeU6OoRV4DbBe8GdbP8chAPCUWhzUn3JyUZgR6aq1B9/T/YU5EeAKe9z3CQIDAQAB";
    public static void main1(String[] args) {

        OpenClient client = new OpenClient(yinTaiUrl, platformAppId, platformPrivateKey,platformPublicKey);

        Map<String, Object> bizModel = new TreeMap<>();
        String platformMallId = "28";
        String thirdCounterId = "JDA26302";
        String tradeTime = "";
        bizModel.put("billType", 1L);
        bizModel.put("channel", platformMallId);
        bizModel.put("thirdCounterId", thirdCounterId);
        bizModel.put("requestId", platformMallId + "-" + IdUtil.simpleUUID());
        bizModel.put("orderNo", "");
        bizModel.put("billTime", "");
        bizModel.put("productNum", 1);
        bizModel.put("allAmt", "");
        bizModel.put("billAmt", "");
        bizModel.put("netName", "wechat");
        bizModel.put("netAmt", "");
        bizModel.put("vipPhone", "");

        List<TransPaying> payingListList = new ArrayList<>();
        TransPaying transPaying = new TransPaying();
        transPaying.setSort(1);
        transPaying.setPayTime(tradeTime);
        transPaying.setPayAmt(BigDecimal.ZERO);
        transPaying.setIsScore(0);
        transPaying.setPayName("wechat");
        transPaying.setPayTypeId("W001");
        transPaying.setTraceNo("");
        payingListList.add(transPaying);
        bizModel.put("transPayingList", payingListList);

        List<TransProduct> transProductList = new ArrayList<>();
        TransProduct transProduct = new TransProduct();
        transProduct.setSort(1);
        transProduct.setProductNum(1);
        transProduct.setProductId("613601");
        transProduct.setProductName("1HZ0865001");
        transProduct.setUnitPrice(BigDecimal.ZERO);
        transProduct.setRealAmt(BigDecimal.ZERO);
        transProductList.add(transProduct);
        bizModel.put("transProductList", transProductList);

        CommonRequest request = new CommonRequest("order.third.syn");
        request.setBizModel(bizModel);
        CommonResponse response = client.execute(request);
        YinTaiBaseRespEntity resp = JSONObject.parseObject(response.getBody(), YinTaiBaseRespEntity.class);
        System.out.println("输出结果：" + JSONObject.toJSONString(resp));
    }

}
