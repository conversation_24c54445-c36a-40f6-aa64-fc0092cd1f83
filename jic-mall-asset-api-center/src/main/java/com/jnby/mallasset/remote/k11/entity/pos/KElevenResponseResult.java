package com.jnby.mallasset.remote.k11.entity.pos;

import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

@Data
@Root(name = "PostSalesResponse" )
@Namespace(reference = "http://tempuri.org/")
public class KElevenResponseResult {


    @Element(name = "PostSalesResult" , required = false)
    private KElevenPostSalesResult result;
}
