package com.jnby.mallasset.strategy.category;

import com.jnby.mallasset.api.dto.coupon.CouponInfoRespDto;
import com.jnby.mallasset.api.dto.coupon.CouponOperationRespDto;
import com.jnby.mallasset.strategy.context.CouponContext;
import com.jnby.mallasset.strategy.context.UserContext;

import java.util.List;

/**
 * 优惠券模版接口
 */
public interface ICouponAssetTemplate {
    void checkUser(UserContext user);

    List<CouponInfoRespDto> doList(UserContext user);

    void preCheckUseCoupon(UserContext user, CouponContext req);

    void doUseCoupon(UserContext user, CouponContext req);

    void preCheckReturnCoupon(UserContext user, CouponContext req);

    void doReturnCoupon(UserContext user, CouponContext req);

    void preCheckSendCoupon(UserContext user, CouponContext req);

    void doSendCoupon(UserContext user, CouponContext req);
}
