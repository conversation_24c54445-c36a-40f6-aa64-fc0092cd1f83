apiVersion: apps/v1
kind: Deployment
metadata:
  name: jic-mall-asset-api-center-prod
  namespace: box
  labels:
    web: jic-mall-asset-api-center-prod
spec:
  replicas: {api-replicas}
  selector:
    matchLabels:
      web: jic-mall-asset-api-center-prod
  template:
    metadata:
      labels:
        web: jic-mall-asset-api-center-prod
    spec:
      containers:
      - name: jic-mall-asset-api-center-prod
        image: jnbyharbor.jnby.com/box-group/jic-mall-asset/jic-mall-asset-api-center:latest
        imagePullPolicy: "Always"
        ports:
        - containerPort: 9601
        env:
        - name: profiles
          value: prod
        lifecycle:
          preStop:
            exec:
              command:
                - sh
                - c
                - "sleep 5"
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 20
          successThreshold: 1
          tcpSocket:
            port: 9601
          timeoutSeconds: 1
        readinessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 20
          successThreshold: 1
          tcpSocket:
            port: 9601
          timeoutSeconds: 1
      restartPolicy: Always
      imagePullSecrets:
      - name: box-group

---
apiVersion: v1
kind: Service
metadata:
  name: jic-mall-asset-api-center-prod
  namespace: box
  labels:
    web: jic-mall-asset-api-center-prod
spec:
  type: ClusterIP
  ports:
    - port: 9601
  selector:
    web: jic-mall-asset-api-center-prod

