package com.jnby.mallasset.remote.xiexin.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BaseXiexinResp<T> implements Serializable {
    @ApiModelProperty(value = "结果")
    private Integer code;
    @ApiModelProperty(value = "返回消息")
    private String message;
    @ApiModelProperty(value = "返回时间")
    private T data;

    public boolean isSuccess(){
        if (this.code == 0) {
            return true;
        }
        return false;
    }
}
