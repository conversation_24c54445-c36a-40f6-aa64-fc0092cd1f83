package com.jnby.mallasset.remote.longhu;


import com.jnby.mallasset.remote.longhu.entity.*;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.HeaderMap;
import retrofit2.http.POST;

import java.util.Map;

public interface ILongHuRemoteHttpApi {


    /**
     * 功能描述: 发放龙湖珑珠
     * 使用场景:
     *
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.longhu.entity.LongHuPointsGrantResp>
     * <AUTHOR>
     * @date 2025/6/9 18:03
     */
    @POST("enterprise/grant/single")
    Call<LongHuEnterpriseEncryptResp> enterpriseGrant(@HeaderMap Map<String, String> headers, @Body LongHuEnterpriseEncryptResp req);

    /**
     * 功能描述: 发放龙湖珑珠结果查询
     * 使用场景:
     *
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.longhu.entity.LongHuPointsGrantResp>
     * <AUTHOR>
     * @date 2025/6/9 18:03
     */
    @POST("enterprise/grant/queryResult")
    Call<LongHuEnterpriseEncryptResp> enterpriseGrantQuery(@HeaderMap Map<String, String> headers, @Body LongHuEnterpriseEncryptResp req);

    /**
     * 功能描述: 发放龙湖珑珠退回
     * 使用场景:
     *
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.longhu.entity.LongHuPointsGrantResp>
     * <AUTHOR>
     * @date 2025/6/9 18:03
     */
    @POST("enterprise/grant/refund")
    Call<LongHuEnterpriseEncryptResp> enterpriseGrantRefund(@HeaderMap Map<String, String> headers, @Body LongHuEnterpriseEncryptResp req);

    /**
     * 功能描述: 发放龙湖珑珠退回结果查询
     * 使用场景:
     *
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.longhu.entity.LongHuPointsGrantResp>
     * <AUTHOR>
     * @date 2025/6/9 18:03
     */
    @POST("enterprise/grant/refundQuery")
    Call<LongHuEnterpriseEncryptResp> enterpriseGrantRefundQuery(@HeaderMap Map<String, String> headers, @Body LongHuEnterpriseEncryptResp req);


}
