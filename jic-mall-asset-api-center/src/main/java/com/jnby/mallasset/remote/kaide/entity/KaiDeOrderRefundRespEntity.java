package com.jnby.mallasset.remote.kaide.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DrcMemberRegisterReqEntity.java
 * 文件简介: 会员注册请求
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class KaiDeOrderRefundRespEntity implements Serializable {
    @ApiModelProperty(value = "交易标识")
    private String identifier;
    @ApiModelProperty(value = "单据编号")
    private String receiptNo;
}
