package com.jnby.mallasset.remote.kaide.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 文件名: com.jnby.mallasset.remote.kaide.entity-KaiDeOrderReqEntity.java
 * 文件简介: 凯德订单请求
 *
 * <AUTHOR>
 * @date 2024/7/26 16:54
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class KaiDeOrderReqEntity implements Serializable {
    @ApiModelProperty(value = "会员卡号", required = true)
    private String memberNo;
    @ApiModelProperty(value = "店铺编号", required = true)
    private String shop;
    @ApiModelProperty(value = "交易日期", required = true)
    private String transTime;
    @ApiModelProperty(value = "单据编号", required = true)
    private String receiptNo;
    @ApiModelProperty(value = "应付金额", required = true)
    private BigDecimal amount;
    @ApiModelProperty(value = "实付金额", required = true)
    private BigDecimal amountReal;
    @ApiModelProperty(value = "渠道码", required = true)
    private String methodCode;
    @ApiModelProperty(value = "备注", required = true)
    private String remark;
    @ApiModelProperty(value = "付款方式", required = true)
    private List<Payment> payments;
}
