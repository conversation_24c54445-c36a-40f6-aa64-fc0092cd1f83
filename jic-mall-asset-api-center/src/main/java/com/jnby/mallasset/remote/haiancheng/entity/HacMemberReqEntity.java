package com.jnby.mallasset.remote.haiancheng.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.haiancheng.entity-HacMemberReqEntity.java
 * 文件简介: 会员请求参数
 *
 * <AUTHOR>
 * @date 2025/1/13 9:49
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class HacMemberReqEntity implements Serializable {
    @ApiModelProperty(value = "门店ID", required = true)
    private Integer mallId;
    @ApiModelProperty(value = "会员手机号", required = true)
    private String mobile;
}
