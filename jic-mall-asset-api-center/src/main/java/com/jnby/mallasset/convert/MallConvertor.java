package com.jnby.mallasset.convert;

import com.jnby.mallasset.api.dto.asset.MallConfigListRespDto;
import com.jnby.mallasset.api.dto.asset.MallConfigRespDto;
import com.jnby.mallasset.api.dto.asset.PayConfigRespDto;
import com.jnby.mallasset.api.dto.points.PointsInfoRespDto;
import com.jnby.mallasset.module.model.CashierMallAssetConfig;
import com.jnby.mallasset.module.model.CashierMallStoreConfig;
import com.jnby.mallasset.module.model.CashierPayConfig;
import com.jnby.mallasset.module.model.CashierPaymentConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(config = ConvertConfig.class)
public interface MallConvertor {

    MallConvertor INSTANCE = Mappers.getMapper(MallConvertor.class);

    @Mappings({
            @Mapping(target = "platformMallId", source = "storeConfig.platformMallId"),
            @Mapping(target = "platformMallStoreId", source = "storeConfig.platformMallStoreId"),
            @Mapping(target = "mallStoreName", source = "storeConfig.mallStoreName"),
            @Mapping(target = "pointsDeductionScale", source = "storeConfig.pointsDeductionScale"),
            @Mapping(target = "pointsDeductionThreshold", source = "storeConfig.pointsDeductionThreshold"),
            @Mapping(target = "pointsDeductionUpperLimit", source = "storeConfig.pointsDeductionUpperLimit"),
            @Mapping(target = "pointsDeductionLadder", source = "storeConfig.pointsDeductionLadder"),
            @Mapping(target = "payChannel", source = "storeConfig.payChannel"),
            @Mapping(target = "mchId", source = "storeConfig.mchId"),
            @Mapping(target = "storeId", source = "storeConfig.bjStoreId"),
            @Mapping(target = "hasLinkCashier", source = "storeConfig.hasLinkCashier"),
            @Mapping(target = "platformAppId", source = "storeConfig.platformAppId"),
            @Mapping(target = "integralSkuCode", source = "storeConfig.integralSkuCode"),
            @Mapping(target = "mallName", source = "storeConfig.mallName")
    })
    MallConfigRespDto storeConfig2RespDto(CashierMallStoreConfig storeConfig, CashierMallAssetConfig mallConfig);

    PointsInfoRespDto storeConfig2PointsRespDto(CashierMallStoreConfig storeConfig);

    List<PayConfigRespDto> payConfig2Dto(List<CashierPaymentConfig> cashierPaymentConfigs);

    List<PayConfigRespDto> payConfig2DtoV2(List<CashierPayConfig> cashierPaymentConfigs);

    MallConfigListRespDto mallConfigRespDto2MallConfigListRespDto(MallConfigRespDto data);
}
