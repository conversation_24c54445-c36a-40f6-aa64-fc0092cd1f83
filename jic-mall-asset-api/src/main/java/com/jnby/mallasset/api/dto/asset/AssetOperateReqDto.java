package com.jnby.mallasset.api.dto.asset;

import com.google.common.base.Preconditions;
import com.jnby.mallasset.api.dto.BaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("资产使用入参")
@Data
public class AssetOperateReqDto extends BaseReq implements Serializable {
    @ApiModelProperty("积分。与券号二选一必填")
    private Integer points;
    @ApiModelProperty("券号列表。与积分二选一必填")
    private List<String> couponNoList;
    @ApiModelProperty(value = "订单号。也是业务幂等ID", required = true)
    private String orderNo;
    @Override
    public void check() {
        super.check();
        Preconditions.checkArgument(StringUtils.isNotBlank(orderNo), "订单号不能为空");
        if (Objects.isNull(points)) {
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(couponNoList), "积分和券号不能同时为空");
        }
        if (CollectionUtils.isEmpty(couponNoList)) {
            Preconditions.checkArgument((Objects.nonNull(points) && points > 0), "积分和券号不能同时为空");
        }
    }
}
