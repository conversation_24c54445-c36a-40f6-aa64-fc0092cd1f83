package com.jnby.mallasset.remote.xiexin.entity.order;

import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

@Data
@Root(name = "SaleBillCentResponse" )
@Namespace(reference = "http://tempuri.org/")
public class XiexinOrderResponseResult {

    @Element(name = "SaleBillCentResult" , required = false)
    private boolean saleBillCentResult;

    @Element(name = "msg" , required = false)
    private String msg;

    @Element(name = "GainCent" , required = false)
    private Double gainCent;
}
