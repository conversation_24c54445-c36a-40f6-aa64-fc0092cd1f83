package com.jnby.mallasset.api.dto.asset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("资产使用回参")
@Data
public class AssetUseRespDto implements Serializable {
    @ApiModelProperty("业务ID")
    private String bizId;
}
