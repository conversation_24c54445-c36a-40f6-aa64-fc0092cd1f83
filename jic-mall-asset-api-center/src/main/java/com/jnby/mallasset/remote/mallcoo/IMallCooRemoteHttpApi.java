package com.jnby.mallasset.remote.mallcoo;


import com.jnby.mallasset.remote.mallcoo.entity.*;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.HeaderMap;
import retrofit2.http.POST;

import java.util.List;
import java.util.Map;


/**
 * 猫酷平台
 * 注意点：
 * 1、入参实体字段都加上 @JSONField(ordinal=x)，让其序列化的时候按照一个顺序，否则容易验签失败
 * 2、入参实体字段首字母要么都小写，因为验签的时候JSON.toJSonString默认是将首字母小写的，如果要大写的话，则需要配置 @JSONField(name = "Xxx")
 * 3、返回实体字段首字母直接大写好了，否则获取不到值。
 * 入参参考 {@link PointsListDetailReqEntity}
 * 回参参考 {@link PointsListDetailRespEntity}
 */
public interface IMallCooRemoteHttpApi {

    /**
     * 获取会员积分明细V2
     *
     * @param headers:通过工具HeaderUtils生成
     * @param req                       入参
     * @doc <a>https://docs.mallcoo.cn/support/Doc/Detail?docType=5&docID=529</a>
     */
    @POST("/User/Score/v2/Get/Records/")
    Call<BaseMallCooResp<PointsListDetailRespEntity>> listScoreRecords(@HeaderMap Map<String, String> headers, @Body PointsListDetailReqEntity req);

    /**
     * 开卡
     */
    @POST("/User/MallCard/v1/Open/ByMobile/")
    Call<BaseMallCooResp<MemberCreateCardRespEntity>> createCard(@HeaderMap Map<String, String> headers, @Body MemberCreateCardReqEntity req);

    /**
     * 订单加积分
     */
    @POST("/User/Score/V2/Consume/Plus/ByMobile/")
    Call<BaseMallCooResp<PointsPlusByOrderRespEntity>> pointsPlusByOrder(@HeaderMap Map<String, String> headers, @Body PointsPlusByOrderReqEntity req);

    /**
     * 退款减积分
     */
    @POST("/Return/v1/Execute/")
    Call<BaseMallCooResp<PointsDeductionByRefundRespEntity>> pointsDeductionByRefund(@HeaderMap Map<String, String> headers, @Body PointsDeductionByRefundReqEntity req);

    /**
     * 根据手机号扣减会员积分
     */
    @POST("/User/Score/v1/Subtract/ByMobile/")
    Call<BaseMallCooResp> subtractPointsByMobile(@HeaderMap Map<String, String> headers, @Body PointsOperationByMobileReqEntity req);

    /**
     * 查询积分是否扣减成功
     */
    @POST("/User/Score/v1/Check/ByTransID")
    Call<BaseMallCooResp> checkSubtractPointsById(@HeaderMap Map<String, String> headers, @Body PointsOperationByMobileReqEntity req);

    /**
     * 根据手机号增加会员积分
     */
    @POST("/User/Score/v1/Plus/ByMobile/")
    Call<BaseMallCooResp> plusPointsByMobile(@HeaderMap Map<String, String> headers, @Body PointsOperationByMobileReqEntity req);

    /**
     * 通过手机号码查询可用券接口
     */
    @POST("/Coupon/v1/GetCanUse/ByMobile/")
    Call<BaseMallCooResp<CouponCanUseListRespEntity>> listCanUseCouponByMobile(@HeaderMap Map<String, String> headers, @Body CouponCanUseListReqEntity req);


    /**
     * 券核销检查
     */
    @POST("/Coupon/Code/v1/Check/ByVCode/Standard")
    Call<BaseMallCooResp<CouponCheckRespEntity>> checkCoupon(@HeaderMap Map<String, String> headers, @Body CouponCheckReqEntity req);

    /**
     * 券核销V2
     */
    @POST("/Coupon/Code/v2/BatchUse/ByVCodes/Standard/")
    Call<BaseMallCooResp<CouponBatchUseRespEntity>> batchUseCouponByVCodes(@HeaderMap Map<String, String> headers, @Body CouponBatchUseReqEntity req);


    /**
     * 券撤销核销V2
     */
    @POST("/Coupon/Code/v2/CancelUse/")
    Call<BaseMallCooResp<CouponBatchReturnRespEntity>> batchReturnCouponByVCodes(@HeaderMap Map<String, String> headers, @Body CouponBatchReturnReqEntity req);

    /**
     * 查询会员
     */
    @POST("/User/AdvancedInfo/v1/Get/ByMobile/")
    Call<BaseMallCooResp<MemberCreateCardRespEntity>> getMember(@HeaderMap Map<String, String> headers, @Body MemberCreateCardReqEntity req);

    /**
     * 查询会员
     */
    @POST("/User/AdvancedInfo/v1/Get/ByMobile/")
    Call<BaseMallCooResp<MemberQueryRespEntity>> getMember2(@HeaderMap Map<String, String> headers, @Body MemberQueryReqEntity req);

    /**
     * 会员打标
     */
    @POST("/User/BaseInfo/v1/AddTag/")
    Call<BaseMallCooResp> memberTag(@HeaderMap Map<String, String> headers, @Body MemberTagReqEntity req);

    /**
     * 功能描述: 上传订单结算系统
     * 使用场景:
     *
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.kkmall.entity.KkMallUploadOrderResp>
     * <AUTHOR>
     * @date 2024/9/6 18:24
     */
    @POST("http://papp-01.g-cre.com:8185/PosService65_XH/rest/salestransaction/lite")
    Call<CocoparkUploadOrderResp> uploadOrderInfo(@Body CocoparkUploadOrderInfo req);

    /**
     * 发券
     * <a>https://docs.mallcoo.cn/support/Doc/Detail?docType=5&docID=545</a>
     */
    @POST("/Coupon/v2/Send/ByMobile/")
    Call<BaseMallCooResp<List<CouponBatchSendRespEntity>>> sendCouponByMobile(@HeaderMap Map<String, String> headers, @Body CouponBatchSendReqEntity req);

}
