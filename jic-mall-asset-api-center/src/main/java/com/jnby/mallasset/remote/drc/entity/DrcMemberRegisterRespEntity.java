package com.jnby.mallasset.remote.drc.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DrcMemberRegisterRespEntity.java
 * 文件简介: 会员注册返回结果
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class DrcMemberRegisterRespEntity implements Serializable {
    @ApiModelProperty(value = "商场ID")
    private String mall_id;
    @ApiModelProperty(value = "会员手机号")
    private String member_phone;
    @ApiModelProperty(value = "姓名")
    private String member_name;
    @ApiModelProperty(value = "昵称")
    private String member_nickname;
    @ApiModelProperty(value = "性别")
    private String gender;
    @ApiModelProperty(value = "生日")
    private String birthday;
    @ApiModelProperty(value = "等级信息")
    private GradeInfo grade_info;
    @ApiModelProperty(value = "数据来源：API")
    private String data_source;
    @ApiModelProperty(value = "操作人：(B端修改传入B端用户名，C端修改则传入appid)")
    private String operators;
    @ApiModelProperty(value = "会员号(注册时为空，修改时必填)")
    private String member_code;
}
