package com.jnby.mallasset.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

@Data
@ApiModel("订单加积分请求入参")
public class OrderPlusPointsReq {

    @ApiModelProperty(value = "伯俊门店Id", required = true)
    private String storeId;

    @ApiModelProperty(value = "消费者手机号", required = true)
    private String phone;

    @ApiModelProperty(value = "订单详情", required = true)
    private Order order;

    public void check() {
        if (StringUtils.isBlank(storeId)) {
            throw new IllegalArgumentException("门店Id不能为空");
        }
        if (StringUtils.isBlank(phone)) {
            throw new IllegalArgumentException("消费者手机号不能为空");
        }
    }

    @Data
    public class Order{
        @ApiModelProperty(value = "订单号", required = true)
        private String orderNo;

        @ApiModelProperty(value = "积分数", required = true)
        private Integer points;

        @ApiModelProperty(value = "支付金额（参与积分的部分）。单位：元，保留2位小数", required = true)
        private String payAmount;

        @ApiModelProperty(value = "交易时间戳。格式：yyyy-MM-dd HH:mm:ss", required = true)
        private String tradeTime;

        @ApiModelProperty(value = "订单总金额=支付金额+优惠金额。单位：元，保留2位小数", required = true)
        private String totalAmount;
    }
}
