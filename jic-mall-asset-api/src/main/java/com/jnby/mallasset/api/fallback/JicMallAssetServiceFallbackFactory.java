package com.jnby.mallasset.api.fallback;

import com.jnby.common.ResponseResult;
import com.jnby.mallasset.api.IMallAssetApi;
import com.jnby.mallasset.api.dto.BaseReq;
import com.jnby.mallasset.api.dto.asset.*;
import com.jnby.mallasset.api.dto.coupon.CouponInfoRespDto;
import com.jnby.mallasset.api.dto.points.PointsInfoRespDto;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class JicMallAssetServiceFallbackFactory implements FallbackFactory<IMallAssetApi> {

    @Override
    public IMallAssetApi create(Throwable throwable) {
        return new JicMallAssetServiceWithFallback() {

            @Override
            public ResponseResult<MallConfigRespDto> getMallConfig(MallConfigReqDto req, String auth) {
                return null;
            }

            @Override
            public ResponseResult<List<MallConfigListRespDto>> listMallConfigForBox(MallConfigListReqDto req, String auth) {
                return null;
            }

            @Override
            public ResponseResult<List<CouponInfoRespDto>> listCanUseCoupon(BaseReq req, String auth) {
                log.error("调用优惠券接口异常", throwable);
                return null;
            }

            @Override
            public ResponseResult<PointsInfoRespDto> getPoints(BaseReq req, String auth) {
                return null;
            }

            @Override
            public ResponseResult<AssetUseRespDto> useAsset(AssetOperateReqDto req, String auth) {
                return null;
            }

            @Override
            public ResponseResult<AssetUseRespDto> returnAsset(AssetOperateReqDto req, String auth) {
                return null;
            }

            @Override
            public ResponseResult<List<PayConfigRespDto>> listPayConfig(PayConfigReqDto req, String auth) {
                return null;
            }
        };

    }


}
