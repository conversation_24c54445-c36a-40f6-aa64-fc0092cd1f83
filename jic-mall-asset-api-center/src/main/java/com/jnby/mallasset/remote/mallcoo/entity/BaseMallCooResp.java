package com.jnby.mallasset.remote.mallcoo.entity;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BaseMallCooResp<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "状态码", required = true)
    private int Code;

    @ApiModelProperty(value = "返回消息", required = true)
    private String Message;

    @ApiModelProperty("响应数据")
    private T Data;

    public boolean isSuccess(){
        if (this.Code == 1) {
            return true;
        }
        return false;
    }
}
