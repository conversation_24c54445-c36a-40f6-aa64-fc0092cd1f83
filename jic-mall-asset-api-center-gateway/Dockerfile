FROM jnbyharbor.jnby.com/base-images/baseimagejdk8:v1.3

MAINTAINER box-group

RUN mkdir -p /jic-mall-asset/jic-mall-asset-api-center-gateway

WORKDIR /jic-mall-asset/jic-mall-asset-api-center-gateway

EXPOSE 9611

COPY target/jic-mall-asset-api-center-gateway.jar jic-mall-asset-api-center-gateway.jar

ENV TZ='Asia/Shanghai'

ENTRYPOINT ["java", "-Xmx4g", "-Xms4g","-XX:NewRatio=3","-Xss512k", "-Xmn2g","-XX:SurvivorRatio=2", "-XX:MaxMetaspaceSize=192m", "-XX:MetaspaceSize=192m", "-XX:+UseParallelGC","-Dreactor.netty.pool.leasingStrategy=lifo", "-jar", "jic-mall-asset-api-center-gateway.jar"]

CMD ["--spring.profiles.active=prod"]

