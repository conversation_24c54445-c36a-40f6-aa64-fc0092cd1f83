package com.jnby.mallasset.strategy.category;

import com.jnby.mallasset.strategy.context.PointContext;
import com.jnby.mallasset.strategy.context.UserContext;

public abstract class AbstractPointsService extends AbstractMallService implements IBasePointsService, IPointsAssetTemplate {
    @Override
    public Boolean usePoints(UserContext user, PointContext req) {
        checkUser(user);
        preCheckUsePoints(user, req);
        doUsePoints(user, req);
        return true;
    }

    @Override
    public Boolean returnPoints(UserContext user, PointContext req) {
        checkUser(user);
        preCheckReturnPoints(user, req);
        doReturnPoints(user, req);
        return true;
    }

    @Override
    public Long getPoints(UserContext user) {
        return getPoints(user);
    }
}
