package com.jnby.mallasset.strategy.category;

import com.jnby.mallasset.api.dto.coupon.CouponInfoRespDto;
import com.jnby.mallasset.api.dto.coupon.CouponOperationRespDto;
import com.jnby.mallasset.strategy.context.CouponContext;
import com.jnby.mallasset.strategy.context.UserContext;

import java.util.List;

public interface IBaseCouponService {
    /**
     * 获取可用券列表，默认最大返回 100 个
     */
    List<CouponInfoRespDto> getCanUseCouponList(UserContext user);

    /**
     * 使用券
     */
    Boolean useCoupon(UserContext user, CouponContext req);

    /**
     * 返还券
     */
    Boolean returnCoupon(UserContext user, CouponContext req);

    /**
     * 发券
     */
    Boolean sendCoupon(UserContext user, CouponContext req);
}
