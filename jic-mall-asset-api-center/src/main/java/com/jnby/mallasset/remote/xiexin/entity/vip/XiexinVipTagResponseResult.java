package com.jnby.mallasset.remote.xiexin.entity.vip;

import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

import java.util.List;

@Data
@Root(name = "GetVipLableListResponse" )
@Namespace(reference = "http://tempuri.org/")
public class XiexinVipTagResponseResult {

    @Element(name = "GetVipLableListResult" , required = false)
    private boolean getVipCardResult;

    @Element(name = "msg" , required = false)
    private String msg;

    @Element(name = "Lableitem" , required = false)
    private XiexinVipTagItem lableitem;

}
