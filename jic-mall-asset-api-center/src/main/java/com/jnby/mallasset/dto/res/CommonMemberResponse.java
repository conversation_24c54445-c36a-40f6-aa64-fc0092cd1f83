package com.jnby.mallasset.dto.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("会员返回结果")
public class CommonMemberResponse {

    @ApiModelProperty(value = "会员标识：0-新会员；1-老会员;2-未知")
    private int memberFlag;
    @ApiModelProperty(value = "会员唯一值")
    private String memberId;
    @ApiModelProperty(value = "会员卡号")
    private String memberCardNo;
}
