package com.jnby.mallasset.remote.yintai.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class YinTaiBaseRespEntity implements Serializable {
    @ApiModelProperty(value = "返回码")
    private String code;
    @ApiModelProperty(value = "返回信息")
    private String msg;
    @ApiModelProperty(value = "是否成功返回")
    private boolean isSuccess;
}
