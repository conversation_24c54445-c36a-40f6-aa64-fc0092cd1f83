package com.jnby.mallasset.strategy.category;

import com.jnby.mallasset.strategy.context.PointContext;
import com.jnby.mallasset.strategy.context.UserContext;

/**
 * 积分模版接口
 */
public interface IPointsAssetTemplate {

    Long getPoints(UserContext user);

    void checkUser(UserContext user);

    void preCheckUsePoints(UserContext user, PointContext req);

    void doUsePoints(UserContext user, PointContext req);

    void preCheckReturnPoints(UserContext user, PointContext req);

    void doReturnPoints(UserContext user, PointContext req);
}
