package com.jnby.mallasset.remote.yintai99.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class YinTai99PosEntityReq implements Serializable {
    @ApiModelProperty("APIKEY")
    private String apiKey;
    @ApiModelProperty("签名")
    private String signature;
    @ApiModelProperty("外部单号")
    private String bonusCalcMethod;
    @ApiModelProperty("唯一值")
    private String docKey;

    private TransHeaderEntity transHeader;

    private SalesTotalEntity salesTotal;

    private List<SalesItemEntity> salesItem;

    private List<SalesTenderEntity> salesTender;

}
