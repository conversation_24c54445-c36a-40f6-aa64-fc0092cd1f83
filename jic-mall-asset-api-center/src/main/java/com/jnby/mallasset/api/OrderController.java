package com.jnby.mallasset.api;

import com.jnby.common.ResponseResult;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderPosReq;
import com.jnby.mallasset.dto.req.order.OrderPosResult;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.module.service.IOrderBizService;
import com.jnby.mallasset.remote.huarun.entity.BaseHuaRunResp;
import com.jnby.mallasset.remote.huarun.entity.SynchronizationOrderRespEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/order")
@RestController
@AllArgsConstructor
@Api(tags = "订单接口")
public class OrderController {

    private IOrderBizService orderBizService;

    @PostMapping("/addScore")
    @ApiOperation(value = "消费加积分")
    public ResponseResult consumeAddScore(@RequestBody OrderConsumeReq orderConsumeReq) {
        return orderBizService.consumeAddScore(orderConsumeReq);
    }

    @PostMapping("/returnScore")
    @ApiOperation(value = "退货退积分")
    public ResponseResult consumeReturnScore(@RequestBody OrderRefundReq OrderRefundReq) {
        return orderBizService.consumeReturnScore(OrderRefundReq);
    }

    @PostMapping("/prepareRefund")
    @ApiOperation(value = "预退货")
    public ResponseResult consumePrepareReturn(@RequestBody OrderRefundReq OrderRefundReq) {
        return orderBizService.consumePrepareReturn(OrderRefundReq);
    }

    @PostMapping("/shopRefund")
    @ApiOperation(value = "自收银退货")
    public ResponseResult shopRefundOrd(@RequestBody OrderRefundReq OrderRefundReq) {
        return orderBizService.shopPrepareReturn(OrderRefundReq);
    }

    @PostMapping("/callback")
    @ApiOperation(value = "订单回调")
    public String callback(@RequestBody BaseHuaRunResp<SynchronizationOrderRespEntity> req) {
        return orderBizService.callback(req);
    }

    @PostMapping("/pos")
    @ApiOperation(value = "pos订单数据")
    public OrderPosResult posOrder(@RequestBody OrderPosReq req) {
        return orderBizService.posYiDiGangOrder(req);
    }
}
