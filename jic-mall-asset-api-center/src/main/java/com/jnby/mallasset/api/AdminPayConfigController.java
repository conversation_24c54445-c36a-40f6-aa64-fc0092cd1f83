package com.jnby.mallasset.api;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.api.dto.BaseReq;
import com.jnby.mallasset.api.dto.asset.*;
import com.jnby.mallasset.api.dto.coupon.CouponInfoRespDto;
import com.jnby.mallasset.api.dto.member.MemberInfoRespDto;
import com.jnby.mallasset.api.dto.points.PointsInfoRespDto;
import com.jnby.mallasset.config.exception.MallException;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.convert.MallConvertor;
import com.jnby.mallasset.dto.req.coupon.CouponSendReqDto;
import com.jnby.mallasset.module.service.IAssetBizService;
import com.jnby.mallasset.util.RedissonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RequestMapping("/admin/pay/config/api")
@RestController
@Slf4j
@Api(tags = "管理员支付配置")
public class AdminPayConfigController {
}
