package com.jnby.mallasset.remote.huarun.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 文件名: com.jnby.mallasset.remote.huarun.entity-OrdItem.java
 * 文件简介: 商品信息
 *
 * <AUTHOR>
 * @date 2024/7/20 9:31
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class OrdItem {

    @ApiModelProperty("商品售出单价，单位(分)，累计值一定要跟ordAmount保持相等")
    @JSONField(ordinal = 1)
    private Integer itemPrice;
    @ApiModelProperty("商品ID")
    @JSONField(ordinal = 2)
    private String itemId;
    @ApiModelProperty("商品数量")
    @JSONField(ordinal = 4)
    private int itemCount;
    @ApiModelProperty("商品名称")
    @JSONField(ordinal = 5)
    private String itemName;
    @JSONField(ordinal = 6)
    @ApiModelProperty("虚拟货号")
    private String itemArticleNo;
}
