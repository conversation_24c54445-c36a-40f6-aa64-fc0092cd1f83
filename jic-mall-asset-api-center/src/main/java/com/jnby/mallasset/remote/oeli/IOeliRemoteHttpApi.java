package com.jnby.mallasset.remote.oeli;


import com.jnby.mallasset.remote.huarun.entity.*;
import com.jnby.mallasset.remote.mallcoo.entity.*;
import com.jnby.mallasset.remote.oeli.entity.BaseIOeliResp;
import com.jnby.mallasset.remote.oeli.entity.MemberInfoRespEntity;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.Map;


/**
 * 天目里
 */
public interface IOeliRemoteHttpApi {

    /**
     * 功能描述: 用户登录
     * 使用场景:
     *
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.huarun.entity.BaseHuaRunResp<com.jnby.mallasset.remote.huarun.entity.UserLoginRespEntity>>
     * <AUTHOR>
     * @date 2024/7/19 20:28
     */
    @POST("service-third/openapi/jnby/user-login")
    Call<BaseHuaRunResp<UserLoginRespEntity>> userLogin(@Body UserLoginReqEntity req);

    /**
     * 功能描述: 订单同步
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooResp>
     * <AUTHOR>
     * @date 2024/7/20 10:01
     */
    @POST("service-third/openapi/jnby/order-synchronization")
    Call<BaseHuaRunResp<SynchronizationOrderRespEntity>> synchronizationOrder(@HeaderMap Map<String, String> headers, @Body SynchronizationOrderReqEntity req);

    /**
     * 功能描述: 退货退款
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooResp>
     * <AUTHOR>
     * @date 2024/7/20 10:01
     */
    @POST("service-third/openapi/jnby/refund-ord")
    Call<BaseHuaRunResp<RefundOrderRespEntity>> refundOrder(@HeaderMap Map<String, String> headers, @Body RefundOrderReqEntity req);

    /**
     * 获取会员
     * @param mobile
     * @return
     */
    @GET("service-third/openapi/jnby/member/check")
    Call<BaseIOeliResp<MemberInfoRespEntity>> getMember(@Query("mobile") String mobile);

}
