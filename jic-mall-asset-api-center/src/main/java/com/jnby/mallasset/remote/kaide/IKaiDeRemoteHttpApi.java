package com.jnby.mallasset.remote.kaide;


import com.jnby.mallasset.remote.kaide.entity.*;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.Map;


/**
 * 文件名: com.jnby.mallasset.remote.kaide-IKaiDeRemoteHttpApi.java
 * 文件简介: 凯德平台
 *
 * <AUTHOR>
 * @date 2024/7/31 16:08
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface IKaiDeRemoteHttpApi {


    /**
     * 功能描述: 订单同步
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.drc.entity.BaseDrcResp<com.jnby.mallasset.remote.drc.entity.DrcMemberRegisterRespEntity>>
     * <AUTHOR>
     * @date 2024/7/24 13:31
     */
    @POST("/NewCrm/api/Transaction20/Register")
    Call<BaseKaiDeResp<KaiDeOrderRespEntity>> orderRegister(@HeaderMap Map<String, String> headers, @Body KaiDeOrderReqEntity req);

    /**
     * 功能描述: 订单售后
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.drc.entity.BaseDrcResp<com.jnby.mallasset.remote.drc.entity.DrcMemberRegisterRespEntity>>
     * <AUTHOR>
     * @date 2024/7/24 13:31
     */
    @POST("/NewCrm/api/Transaction20/Refund")
    Call<BaseKaiDeResp<KaiDeOrderRefundRespEntity>> orderRefund(@HeaderMap Map<String, String> headers, @Body KaiDeOrderRefundReqEntity req);

    /**
     * 功能描述: 会员查询
     * 使用场景:
     *
     * @param headers
     * @param mobile
     * @return retrofit2.Call<com.jnby.mallasset.remote.drc.entity.BaseDrcResp<com.jnby.mallasset.remote.drc.entity.DrcMemberRegisterRespEntity>>
     * <AUTHOR>
     * @date 2024/7/24 13:31
     */
    @GET("/NewCrm/api/Profile20/GetMemberInfo")
    Call<BaseKaiDeResp<KaiDeMemberRespEntity>> getMemberInfo(@HeaderMap Map<String, String> headers, @Query("mobile") String mobile);
}
