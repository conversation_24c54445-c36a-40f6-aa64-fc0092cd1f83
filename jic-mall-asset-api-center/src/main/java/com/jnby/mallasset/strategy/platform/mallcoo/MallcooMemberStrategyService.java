package com.jnby.mallasset.strategy.platform.mallcoo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.api.AssetController;
import com.jnby.mallasset.api.dto.asset.AssetUseRespDto;
import com.jnby.mallasset.config.exception.MallException;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.coupon.CouponSendReqDto;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.module.service.IAssetBizService;
import com.jnby.mallasset.remote.dayuecheng.IDayuechengRemoteHttpApi;
import com.jnby.mallasset.remote.dingding.IDingDingRemoteHttpApi;
import com.jnby.mallasset.remote.dingding.entity.DingDingMsg;
import com.jnby.mallasset.remote.dingding.entity.DingDingResponse;
import com.jnby.mallasset.remote.mallcoo.IMallCooRemoteHttpApi;
import com.jnby.mallasset.remote.mallcoo.MallCooUtils;
import com.jnby.mallasset.remote.mallcoo.entity.*;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 猫酷平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.MALL_COO, category = PlatformCategoryTypeEnum.MEMBER)
@Slf4j
public class MallcooMemberStrategyService extends AbstractMemberService {

    @Autowired
    private IMallCooRemoteHttpApi mallCooRemoteHttpApi;
    @Autowired
    private CashierMallMemberLogMapper cashierMallMemberLogMapper;
    @Autowired
    private IDingDingRemoteHttpApi dingDingRemoteHttpApi;
    @Autowired
    private IAssetBizService assetBizService;
    @Autowired
    private AssetController assetController;
    @Autowired
    private IDayuechengRemoteHttpApi dayuechengRemoteHttpApi;


    @Override
    public ResponseResult openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("猫酷会员注册输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            String storeId = memberRegisterReq.getStoreId();
            String mobile = memberRegisterReq.getMobile();
            // 记录流水
            CashierMallMemberLog cashierMallMemberLog = recordMemberLog(cashierMallAssetStoreRef, storeId, mobile);

            String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
            String platformPublicKey = cashierMallAssetStoreRef.getPlatformPublicKey();
            String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
            MemberCreateCardReqEntity req = new MemberCreateCardReqEntity();
            req.setMobile(mobile);
            req.setDataSource(4);
            log.info("猫酷会员注册调用远程接口参数：{}", JSON.toJSONString(req));
            Map<String, String> header = MallCooUtils.getHeaders(platformAppId,platformPublicKey,platformPrivateKey, JSON.toJSONString(req));
            // 查询会员是否存在
            CommonMemberResponse commonMemberResponse = new CommonMemberResponse();
            // 上海大悦城后面临时换了环境而做的调整
            Response<BaseMallCooResp<MemberCreateCardRespEntity>> response;
            if(memberRegisterReq.getStoreId() != null && (memberRegisterReq.getStoreId().equals("JDA22109")
                || memberRegisterReq.getStoreId().equals("5DA26340") || memberRegisterReq.getStoreId().equals("9DA26327")
                    || memberRegisterReq.getStoreId().equals("5DA22132") || memberRegisterReq.getStoreId().equals("9DA22119"))){
                response = dayuechengRemoteHttpApi.getMember(header, req).execute();
            }else {
                response = mallCooRemoteHttpApi.getMember(header, req).execute();
            }
            BaseMallCooResp<MemberCreateCardRespEntity> respEntity = response.body();
            if(respEntity.getCode() == 1 && respEntity.getData() != null){
                // 说明会员已存在
                MemberCreateCardRespEntity memberCreateCardRespEntity = respEntity.getData();
                commonMemberResponse.setMemberFlag(1);
                commonMemberResponse.setMemberId(memberCreateCardRespEntity.getOpenUserID());
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setData(commonMemberResponse);
                commonResponse.setMsg(respEntity.getMessage());
                // 设置执行标识
                cashierMallMemberLog.setOpenUserId(memberCreateCardRespEntity.getOpenUserID());
                cashierMallMemberLog.setIsExecute("Y");
                cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
                if (memberRegisterReq.getIsNew() != null && Objects.equals("Y", memberRegisterReq.getIsNew())) {
                    sendCarVoucher(memberRegisterReq, cashierMallAssetStoreRef);
                }
                return commonResponse;
            }
            // 上海大悦城后面临时换了环境而做的调整
            Response<BaseMallCooResp<MemberCreateCardRespEntity>> execute;
            if(memberRegisterReq.getStoreId() != null && (memberRegisterReq.getStoreId().equals("JDA22109")
                    || memberRegisterReq.getStoreId().equals("5DA26340") || memberRegisterReq.getStoreId().equals("9DA26327")
                    || memberRegisterReq.getStoreId().equals("5DA22132") || memberRegisterReq.getStoreId().equals("9DA22119"))){
                execute = dayuechengRemoteHttpApi.createCard(header, req).execute();
            }else{
                execute = mallCooRemoteHttpApi.createCard(header, req).execute();
            }
            BaseMallCooResp<MemberCreateCardRespEntity> body = execute.body();
            // 注册成功
            if(body.getCode() == 1){
                commonMemberResponse.setMemberFlag(0);
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                // 设置执行标识
                String OpenUserID = body.getData().getOpenUserID();
                cashierMallMemberLog.setOpenUserId(OpenUserID);
                cashierMallMemberLog.setIsExecute("Y");
                cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);

                // 颐堤港门店会员打标
                memberTag(storeId, mobile, cashierMallAssetStoreRef);
            }else {
                // 老会员
                if(body.getCode() == 307){
                    commonMemberResponse.setMemberFlag(1);
                }else {
                    commonMemberResponse.setMemberFlag(2);
                }
                commonResponse.setCode(body.getCode());
            }
            if(body.getData() != null){
                commonMemberResponse.setMemberId(body.getData().getOpenUserID());
            }
            commonResponse.setMsg(body.getMessage());
            commonResponse.setData(commonMemberResponse);
            log.info("猫酷会员注册返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 猫酷会员注册开卡失败",memberRegisterReq.getMobile(),e);
        }

        if (memberRegisterReq.getIsNew() != null && Objects.equals("Y", memberRegisterReq.getIsNew())) {
            sendCarVoucher(memberRegisterReq, cashierMallAssetStoreRef);
        }
        return commonResponse;
    }

    private void sendCarVoucher(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        try{
            if(cashierMallAssetStoreRef.getHasUseParkingCoupon() != null && cashierMallAssetStoreRef.getHasUseParkingCoupon()){
                if(memberRegisterReq.getIsNew() != null && memberRegisterReq.getIsNew().equals("Y")){
                    log.info("[{}]门店新会员[{}]发放停车券", memberRegisterReq.getStoreId(), memberRegisterReq.getMobile());
                    CouponSendReqDto couponSendReqDto = new CouponSendReqDto();
                    couponSendReqDto.setBizId(StringUtil.randomUUID());
                    couponSendReqDto.setCustomerId(memberRegisterReq.getMobile());
                    couponSendReqDto.setStoreId(memberRegisterReq.getStoreId());
                    couponSendReqDto.setType(1);
//                    ResponseResult<AssetUseRespDto> res = assetBizService.sendCoupon(couponSendReqDto);
                    ResponseResult<AssetUseRespDto> res = assetController.sendCoupon(couponSendReqDto);
                    log.info("[{}]门店新会员[{}]发放停车券返回参数：{}", memberRegisterReq.getStoreId(), memberRegisterReq.getMobile(), JSONObject.toJSONString(res));
                }
            }
        }catch (Exception e){
            log.error("[{}][{}]发放停车券失败", memberRegisterReq.getStoreId(), memberRegisterReq.getMobile(),e);
        }
    }

    private void memberTag(String storeId, String mobile,CashierMallAssetStoreRef assetStoreRef) throws Exception {
        if(storeId.equals("2DA00129") || storeId.equals("5DA00141") || storeId.equals("9DA00111")){
            MemberTagReqEntity memberTagReqEntity = new MemberTagReqEntity();
            memberTagReqEntity.setMobile(mobile);
            List<Long> tagIDList = new ArrayList<>();
            tagIDList.add(22154L);
            memberTagReqEntity.setTagIDList(tagIDList);
            memberTagReqEntity.setDesc("jnby");
            Map<String, String> header = MallCooUtils.getHeaders(assetStoreRef.getPlatformAppId(),assetStoreRef.getPlatformPublicKey(),assetStoreRef.getPlatformPrivateKey(), JSON.toJSONString(memberTagReqEntity));
            Response<BaseMallCooResp> baseMallCooRespResponse = mallCooRemoteHttpApi.memberTag(header,memberTagReqEntity).execute();
            BaseMallCooResp baseMallCooResp = baseMallCooRespResponse.body();
            if(baseMallCooResp.getCode() != 1){
                pushDingDingMsg(header, memberTagReqEntity, baseMallCooResp);
            }
            log.info("颐堤港会员打标结果：{}",JSON.toJSONString(baseMallCooRespResponse.body()));
        }
    }

    private void pushDingDingMsg(Map<String, String> header, MemberTagReqEntity memberTagReqEntity, BaseMallCooResp baseMallCooResp) throws IOException {
        String msg = "数据推送异常：\n" + "功能：颐堤港会员打标" + "\n请求参数: \n" + JSON.toJSON(header) + "," + JSON.toJSON(memberTagReqEntity) + "\n请求结果: \n" + JSON.toJSON(baseMallCooResp);
        DingDingMsg dingDingMsg = new DingDingMsg();
        Map<String,Object> param = new HashMap<>();
        param.put("content",msg);
        dingDingMsg.setText(param);
        dingDingMsg.setMsgtype("text");
        Response<DingDingResponse> execute = dingDingRemoteHttpApi.pushMsg(dingDingMsg).execute();
        log.info("推送消息返回结果：{}",execute.body());
    }

    private CashierMallMemberLog recordMemberLog(CashierMallAssetStoreRef cashierMallAssetStoreRef, String storeId, String mobile) {
        CashierMallMemberLog cashierMallCooMemberLog = new CashierMallMemberLog();
        cashierMallCooMemberLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallCooMemberLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallCooMemberLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallCooMemberLog.setBjStoreId(storeId);
        cashierMallCooMemberLog.setMobile(mobile);
        cashierMallCooMemberLog.setIsExecute("N");
        cashierMallMemberLogMapper.insertEntity(cashierMallCooMemberLog);
        return cashierMallCooMemberLog;
    }

    @Override
    public ResponseResult getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
        String platformPublicKey = cashierMallAssetStoreRef.getPlatformPublicKey();
        String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
        try {
            MemberQueryReqEntity reqEntity = MemberQueryReqEntity.builder().mobile(memberRegisterReq.getCustomerId()).build();
            Map<String, String> header = MallCooUtils.getHeaders(platformAppId,platformPublicKey,platformPrivateKey, JSON.toJSONString(reqEntity));
            log.info("猫酷会员查询调用远程接口入参，headers:[{}]--------------req:[{}]", JSON.toJSONString(header), JSON.toJSONString(reqEntity));

            Response<BaseMallCooResp<MemberQueryRespEntity>> execute;
            if(memberRegisterReq.getStoreId() != null && (memberRegisterReq.getStoreId().equals("JDA22109")
                    || memberRegisterReq.getStoreId().equals("5DA26340") || memberRegisterReq.getStoreId().equals("9DA26327")
                    || memberRegisterReq.getStoreId().equals("5DA22132") || memberRegisterReq.getStoreId().equals("9DA22119"))){
                execute = dayuechengRemoteHttpApi.getMember2(header, reqEntity).execute();
            }else{
                execute = mallCooRemoteHttpApi.getMember2(header, reqEntity).execute();
            }

            boolean successful = execute.isSuccessful();
            if (!successful) {
                throw new MallException(SystemErrorEnum.MEMBER_INTERFACE_API_ERROR);
            }
            BaseMallCooResp<MemberQueryRespEntity> body = execute.body();
            log.info("猫酷会员查询调用远程接口响应：{}", JSON.toJSONString(body));
            if (!body.isSuccess()) {
                throw new MallException(body.getMessage());
            }
            MemberQueryRespEntity data = body.getData();
            if (data == null) {
                throw new MallException(SystemErrorEnum.MEMBER_NOT_EXIST_ERROR);
            }
            return ResponseResult.success(data);
        } catch (IOException e) {
            log.error("猫酷会员查询超时", e);
            throw new MallException(SystemErrorEnum.MEMBER_INTERFACE_TIMEOUT_ERROR);
        } catch (MallException e) {
            log.error("猫酷会员查询已知异常", e);
            throw e;
        } catch (Exception e) {
            log.error("猫酷会员查询异常", e);
            throw new MallException(SystemErrorEnum.MEMBER_UNKNOWN_ERROR);
        }
    }
}
