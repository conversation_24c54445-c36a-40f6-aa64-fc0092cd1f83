package com.jnby.mallasset.remote.drc.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-BaseDrcResp.java
 * 文件简介: 大融城基础返回对象
 *
 * <AUTHOR>
 * @date 2024/7/23 14:00
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class BaseDrcDataResp<T> implements Serializable {
    @ApiModelProperty(value = "是否成功")
    private boolean success;
    @ApiModelProperty(value = "错误码")
    private String errorcode;
    @ApiModelProperty(value = "日志id")
    private String logid;
    @ApiModelProperty(value = "返回消息")
    private String msg;
    @ApiModelProperty("响应数据")
    private T data;

}
