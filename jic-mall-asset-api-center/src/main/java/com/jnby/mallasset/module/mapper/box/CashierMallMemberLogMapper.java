package com.jnby.mallasset.module.mapper.box;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * @Author: CodeGenerator
 * @Date: 2024-07-15 13:35:45
 * @Description: 商场资产规则配置Mapper
 */
@Mapper
public interface CashierMallMemberLogMapper extends BaseMapper<CashierMallMemberLog> {

    void insertEntity(CashierMallMemberLog cashierMallMemberLog);

    void updateInfoByParam(CashierMallMemberLog cashierMallMemberLog);

    /**
     * 获取自增id
     */
    Long getMaxId();

    CashierMallMemberLog selectCashierMallMemberLog(@Param("mobile") String mobile, @Param("storeCode") String storeCode);
}
