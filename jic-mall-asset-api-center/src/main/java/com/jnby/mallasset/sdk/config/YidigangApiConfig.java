package com.jnby.mallasset.sdk.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "yidigang.pos.api")
public class YidigangApiConfig {

    @ApiModelProperty(value = "主机")
    private String host;
    @ApiModelProperty(value = "appid")
    private String appid;
    @ApiModelProperty(value = "秘钥")
    private String secret;
    @ApiModelProperty(value = "版本号")
    private String iv;
    @ApiModelProperty(value = "事件类型")
    private int eventType;
    @ApiModelProperty(value = "猫酷项目ID")
    private String projectId;
    @ApiModelProperty(value = "POS机号")
    private String posNumber;
    @ApiModelProperty(value = "数据来源")
    private String sysSourceCode;
    @ApiModelProperty(value = "商铺ID")
    private String brandId;
}
