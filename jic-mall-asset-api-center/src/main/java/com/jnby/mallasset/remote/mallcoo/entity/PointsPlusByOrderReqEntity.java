package com.jnby.mallasset.remote.mallcoo.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PointsPlusByOrderReqEntity {
    @ApiModelProperty("事物ID（当前应用下不得重复，保证提交的唯一性）")
    @JSONField(ordinal = 1)
    private String transID;
    @ApiModelProperty("该商户在我猫酷系统中的唯一编号,商家提供")
    @JSONField(ordinal = 2)
    private Long mcShopID;
    @ApiModelProperty("支付金额（参与积分的部分）")
    @JSONField(ordinal = 3)
    private Double payAmount;
    @ApiModelProperty("手机号")
    @JSONField(ordinal = 4)
    private String mobile;
    @ApiModelProperty("交易时间")
    @JSONField(ordinal = 5)
    private String tradeTime;
    @ApiModelProperty("订单总金额=支付金额+优惠金额")
    @JSONField(ordinal = 6)
    private Double totalAmount;
    @ApiModelProperty("交易流水号（同一posid下，交易流水号必须不同）")
    @JSONField(ordinal = 7)
    private String tradeSerialNo;
    @ApiModelProperty("备注")
    @JSONField(ordinal = 8)
    private String remark;
    @ApiModelProperty("优惠金额，默认为0")
    @JSONField(ordinal = 9)
    private Double promotionAmount;
}
