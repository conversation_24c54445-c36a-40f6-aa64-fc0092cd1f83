package com.jnby.mallasset.strategy.platform.haiancheng;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.remote.haiancheng.IHacRemoteHttpApi;
import com.jnby.mallasset.remote.haiancheng.entity.*;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.BASE64Utils;
import com.jnby.mallasset.util.Md5Util;
import com.jnby.mallasset.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 海岸城订单平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.HAI_AN_CHENG, category = PlatformCategoryTypeEnum.ORDER)
@Slf4j
@AllArgsConstructor
public class HacOrderStrategyService extends AbstractOrderService {

    private CashierMallOrderLogMapper cashierMallOrderLogMapper;
    private IHacRemoteHttpApi hacRemoteHttpApi;
    private HacMemberStrategyService hacMemberStrategyService;

    @Override
    public ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("海岸城订单同步输入参数：{}", JSON.toJSONString(orderConsumeReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderConsumeReq, cashierMallAssetStoreRef);
            String requestId = StringUtil.randomUUID();
            String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
            String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
            Integer platformMallStoreId = Integer.parseInt(cashierMallAssetStoreRef.getPlatformMallStoreId());
            String ordNum = orderConsumeReq.getOrdNum();
//            String outerMemberId = orderConsumeReq.getOuterMemberId();
            BigDecimal ordAmount = orderConsumeReq.getOrdAmount();
            String integralSkuCode = cashierMallAssetStoreRef.getIntegralSkuCode();
            int totQty = orderConsumeReq.getTotQty();

            // 获取会员信息
            MemberRegisterReq memberRegisterReq = new MemberRegisterReq();
            memberRegisterReq.setMobile(orderConsumeReq.getMemberTel());
            memberRegisterReq.setStoreId(orderConsumeReq.getStoreId());
            ResponseResult<CommonMemberResponse> result = hacMemberStrategyService.openCard(memberRegisterReq,cashierMallAssetStoreRef);
            if(result.getData() == null){
                commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                commonResponse.setMsg(result.getMsg());
                return commonResponse;
            }
            String outerMemberId = result.getData().getMemberId();

            Map<String, String> httpHeaders = new HashMap<>();
            httpHeaders.put("x-app-id", platformAppId);
            httpHeaders.put("x-request-id", requestId); // 请求ID, 不同请求需要不一样
            String dateTimestamp = System.currentTimeMillis() + "";
            httpHeaders.put("x-timestamp", dateTimestamp);  // 时间戳

            Map<String, Object> paramMap = new TreeMap<>();
            paramMap.put("memberId",outerMemberId);
            paramMap.put("thirdPartyOrderNo",ordNum);
            paramMap.put("totalPaidAmount",ordAmount);
            paramMap.put("SkuCode",integralSkuCode);
            paramMap.put("storeId",platformMallStoreId);
            paramMap.put("totalBuyQty",totQty);

            httpHeaders.put("x-sign", getMD5SignString(platformPrivateKey, requestId, dateTimestamp, paramMap));
            log.info("海岸城订单同步header参数：{}",JSON.toJSONString(httpHeaders));

            HacOrderCreateReqEntity req = new HacOrderCreateReqEntity();
            req.setMemberId(outerMemberId);
            req.setStoreId(platformMallStoreId);
            req.setThirdPartyOrderNo(ordNum);
            req.setSkuCode(integralSkuCode);
            req.setTotalPaidAmount(ordAmount);
            req.setTotalBuyQty(totQty);

            String paramStr = JSON.toJSONString(req);
            String base64Param = BASE64Utils.encode(paramStr);
            log.info("海岸城订单同步base64加密结果：{}",base64Param);
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), base64Param);
            Response<BaseHacResp<HacOrderRespEntity>> response = hacRemoteHttpApi.orderCreate(httpHeaders,requestBody).execute();
            BaseHacResp<HacOrderRespEntity> body = response.body();
            log.info("海岸城订单同步返回结果：{}", JSON.toJSONString(body));
            if(body != null && body.getCode().equals("ok")){
                HacOrderRespEntity hacOrderRespEntity = body.getData();
                String orderNo = hacOrderRespEntity.getOrderNo();

                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                HacOrderResponse respEntity = new HacOrderResponse();
                respEntity.setOrdNum(orderConsumeReq.getOrdNum());
                respEntity.setExchangeId(orderNo);
                respEntity.setExchangeNo(orderNo);
                commonResponse.setData(respEntity);
                // 更新返回的参数
                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLog.setUpdateExchangeId(orderNo);
                cashierMallOrderLog.setUpdateExchangeNo(orderNo);
                cashierMallOrderLog.setRemark(ResultCodeEnum.SUCCESS.getMsg());
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 海岸城订单同步失败",orderConsumeReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeNo(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setBjStoreId(orderConsumeReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(1);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult pushRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("海岸城订单退货退款输入参数：{}", JSON.toJSONString(orderRefundReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordMallOrderRefundLog(orderRefundReq, cashierMallAssetStoreRef);

            String requestId = StringUtil.randomUUID();
            String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
            String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
            Integer platformMallStoreId = Integer.parseInt(cashierMallAssetStoreRef.getPlatformMallStoreId());
            String refundNo = orderRefundReq.getRefundNo();
            BigDecimal refundAmount = orderRefundReq.getRefundAmount();
            String exchangeId = orderRefundReq.getExchangeId();
            int qty = orderRefundReq.getProductItemList().size();

            Map<String, String> httpHeaders = new HashMap<>();
            httpHeaders.put("x-app-id", platformAppId);
            httpHeaders.put("x-request-id", requestId); // 请求ID, 不同请求需要不一样
            String dateTimestamp = System.currentTimeMillis() + "";
            httpHeaders.put("x-timestamp", dateTimestamp);  // 时间戳

            Map<String, Object> paramMap = new TreeMap<>();
            paramMap.put("thirdPartyOrderNo",refundNo);
            paramMap.put("originalOrderNo",exchangeId);
            paramMap.put("totalReturnAmount",refundAmount);
            paramMap.put("storeId",platformMallStoreId);
            paramMap.put("totalReturnQty",qty);

            httpHeaders.put("x-sign", getMD5SignString(platformPrivateKey, requestId, dateTimestamp, paramMap));
            log.info("海岸城订单退货退款header参数：{}",JSON.toJSONString(httpHeaders));

            HacOrderRefundReqEntity req = new HacOrderRefundReqEntity();
            req.setStoreId(platformMallStoreId);
            req.setTotalReturnAmount(refundAmount);
            req.setOriginalOrderNo(exchangeId);
            req.setTotalReturnQty(qty);
            req.setThirdPartyOrderNo(refundNo);

            String paramStr = JSON.toJSONString(req);
            String base64Param = BASE64Utils.encode(paramStr);
            log.info("海岸城订单退货退款base64加密结果：{}",base64Param);
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), base64Param);
            Response<BaseHacResp<HacOrderRespEntity>> response = hacRemoteHttpApi.orderRefund(httpHeaders,requestBody).execute();
            BaseHacResp<HacOrderRespEntity> body = response.body();
            log.info("海岸城订单退货退款返回结果：{}", JSON.toJSONString(body));
            if(body != null && body.getCode().equals("ok")){
                HacOrderRespEntity hacOrderRespEntity = body.getData();
                String orderNo = hacOrderRespEntity.getOrderNo();

                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                HacOrderResponse respEntity = new HacOrderResponse();
                respEntity.setOrdNum(orderRefundReq.getOrdNum());
                respEntity.setExchangeId(orderNo);
                respEntity.setExchangeNo(orderNo);
                commonResponse.setData(respEntity);

                // 更新返回的参数
                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLog.setUpdateExchangeId(orderNo);
                cashierMallOrderLog.setUpdateExchangeNo(orderNo);
                cashierMallOrderLog.setRemark(ResultCodeEnum.SUCCESS.getMsg());
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            }else {
                commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                commonResponse.setMsg(body.getMsg());
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 海岸城订单退货退款失败",orderRefundReq.getRefundNo(),e);
        }
        return commonResponse;
    }

    private CashierMallOrderLog recordMallOrderRefundLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderRefundReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setExchangeNo(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setBjStoreId(orderRefundReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(2);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }


    @Override
    public ResponseResult prepareRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    private String getMD5SignString(String appSecret, String requestId, String dateTimestamp, Map<String, Object> params) {
        String str = appSecret + ":" + requestId + ":" + dateTimestamp + ":" + JSON.toJSONString(params);
        return Md5Util.getMd5Hash(str); // 进行MD5
    }
}
