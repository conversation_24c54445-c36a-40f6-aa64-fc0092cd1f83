package com.jnby.mallasset.remote.huarun.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class SynchronizationOrderReqEntity {
    @ApiModelProperty("需要积分的万象会员手机号")
    @JSONField(ordinal = 1)
    private String memberTel;
    @ApiModelProperty("注册成为沈阳万象城的会员姓名或昵称")
    @JSONField(ordinal = 2)
    private String memberName;
    @ApiModelProperty("网商平台订单号，不可重复")
    @JSONField(ordinal = 3)
    private String ordNum;
    @ApiModelProperty("第三方支付平台支付单号，如微信支付单号")
    @JSONField(ordinal = 4)
    private String ordPayNo;
    @ApiModelProperty("订单实付总金额，单位(分)")
    @JSONField(ordinal = 5)
    private Integer ordAmount;
    @ApiModelProperty("下单时间")
    @JSONField(ordinal = 6)
    private Long ordFinishTime;
    @ApiModelProperty("第三方平台退款用户标识，如微信OPENID")
    @JSONField(ordinal = 7)
    private String ordOpenId;
    @ApiModelProperty("订单来源默认2，2为数字化交易平台")
    @JSONField(ordinal = 8)
    private int orderMode;
    @ApiModelProperty("商品信息")
    @JSONField(ordinal = 9)
    private List<OrdItem> ordItem;
    @ApiModelProperty("异步回调地址")
    @JSONField(ordinal = 10)
    private String notifyUrl;
    @ApiModelProperty("付款信息")
    @JSONField(ordinal = 11)
    private List<PayItems> payItem;
    @ApiModelProperty("门店编码")
    @JSONField(ordinal = 12)
    private String storeCode;
}
