package com.jnby.mallasset.remote.dayuecheng.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 文件名: com.jnby.mallasset.remote.dayuecheng.entity-ItemPay.java
 * 文件简介: 付款明细
 *
 * <AUTHOR>
 * @date 2025/1/3 16:46
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */

@Data
public class ItemPay implements Serializable {
    @ApiModelProperty(value = "支付银行")
    private String cardBank;
    @ApiModelProperty(value = "银行卡卡号")
    private String cardNumber;
    @ApiModelProperty(value = "支付方式")
    private String paymentMethod;
    @ApiModelProperty(value = "支付时间")
    private String time;
    @ApiModelProperty(value = "支付金额（应收金额）-正数：销售订单/负数：退货订单")
    private BigDecimal value;
    @ApiModelProperty(value = "实收金额-正数：销售订单/负数：退货订单")
    private BigDecimal payAmt;
    @ApiModelProperty(value = "优惠金额-正数：销售订单/负数：退货订单")
    private BigDecimal discountAmt;
}
