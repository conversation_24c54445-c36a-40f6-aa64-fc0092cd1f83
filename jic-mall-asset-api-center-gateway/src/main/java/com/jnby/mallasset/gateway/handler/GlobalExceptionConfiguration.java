package com.jnby.mallasset.gateway.handler;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jnby.mallasset.gateway.common.ResponseCodeEnum;
import com.jnby.mallasset.gateway.common.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 网关异常通用处理器，只作用在webflux 环境下 , 优先级低于 {@link } 执行
 * <AUTHOR>
 * @version 1.0
 * @date 2/19/21 1:40 PM
 */
@Order(-1)
@Component
@Slf4j
public class GlobalExceptionConfiguration implements ErrorWebExceptionHandler {

    private final ObjectMapper objectMapper;

    public GlobalExceptionConfiguration(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public Mono<Void> handle(ServerWebExchange serverWebExchange, Throwable ex) {
        String url = serverWebExchange.getRequest().getURI().getPath();
        ServerHttpResponse response = serverWebExchange.getResponse();
        log.error("拦截到网关请求异常 path = {} ex = {}", url, ex.getMessage(), ex);
        if (response.isCommitted()) {
            return Mono.error(ex);
        }

        // 区分不通的异常进行区分
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        if (ex instanceof ResponseStatusException) {
            log.error("拦截到网关请求异常状态码 path = {} status = {} ex = {}", url, ((ResponseStatusException) ex).getStatus(), ex.getMessage(), ex);
            response.setStatusCode(HttpStatus.OK);
        }

        ResponseResult responseResult = ResponseResult.tokenError(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getMessage());
        DataBuffer dataBuffer = response.bufferFactory().wrap(JSON.toJSONString(responseResult).getBytes());
        return response
                .writeWith(Flux.just(dataBuffer));
    }
}
