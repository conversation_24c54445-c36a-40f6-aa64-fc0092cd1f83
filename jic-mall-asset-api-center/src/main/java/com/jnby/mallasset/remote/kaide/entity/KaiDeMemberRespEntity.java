package com.jnby.mallasset.remote.kaide.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DrcMemberRegisterReqEntity.java
 * 文件简介: 会员响应
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class KaiDeMemberRespEntity implements Serializable {
    @ApiModelProperty(value = "会员卡号")
    private String memberNo;
    @ApiModelProperty(value = "会员名称")
    private String name;
    @ApiModelProperty(value = "会员性别")
    private String gender;
    @ApiModelProperty(value = "会员状态")
    private String status;
    @ApiModelProperty(value = "会员生日")
    private String birthDay;
    @ApiModelProperty(value = "卡等级名称")
    private String levelName;
    @ApiModelProperty(value = "商场编号")
    private String mallCode;
    @ApiModelProperty(value = "商场名称")
    private String mallName;
    @ApiModelProperty(value = "会员积分")
    private String pointAmount;
    @ApiModelProperty(value = "注册时间")
    private String joinDate;
    @ApiModelProperty(value = "即将过期积分")
    private String nearestExpiringAmount;
    @ApiModelProperty(value = "积分即将过期时间")
    private String nearestExpiringDate;
}
