package com.jnby.mallasset.remote.guangHuan.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class SaveTradeResp {
    @ApiModelProperty("金额（分）, 必须为大于零的整数")
    private int amount;
    @ApiModelProperty("文件标识 key")
    private String fileKey;
    @ApiModelProperty("交易总积分")
    private int point;
    @ApiModelProperty("交易号")
    private String transactionNo;
}
