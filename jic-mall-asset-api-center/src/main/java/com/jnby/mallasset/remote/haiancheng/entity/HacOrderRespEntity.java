package com.jnby.mallasset.remote.haiancheng.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.haiancheng.entity-HacOrderRespEntity.java
 * 文件简介:
 *
 * <AUTHOR>
 * @date 2025/1/13 14:36
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class HacOrderRespEntity implements Serializable {
    @ApiModelProperty(value = "主键ID")
    private String id;
    @ApiModelProperty(value = "积分类型")
    private Integer pointTypeId;
    @ApiModelProperty(value = "积分账户")
    private Integer pointAccountId;
    @ApiModelProperty(value = "会员ID")
    private String memberId;
    @ApiModelProperty("积分")
    private Double point;
    @ApiModelProperty("订单号")
    private String orderNo;
    @ApiModelProperty("原订单号")
    private String originalOrderNo;

}
