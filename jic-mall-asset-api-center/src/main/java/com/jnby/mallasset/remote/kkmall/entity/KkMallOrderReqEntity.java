package com.jnby.mallasset.remote.kkmall.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 文件名: com.jnby.mallasset.remote.kkmall.entity-KkMallOrderReqEntity.java
 * 文件简介: KKMALL订单请求
 *
 * <AUTHOR>
 * @date 2024/8/1 15:52
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class KkMallOrderReqEntity implements Serializable {
    @ApiModelProperty(value = "项目id", required = true)
    private String mallId;
    @ApiModelProperty(value = "店铺编号")
    private String mallCode;
    @ApiModelProperty(value = "店铺id", required = true)
    private String storeId;
    @ApiModelProperty(value = "店铺编号")
    private String storeCode;
    @ApiModelProperty(value = "来源 1 POS机，2.后台手工补录，3.扫码积分 6 微信商圈 7 支付宝商圈 9 其他", required = true)
    private int source;
    @ApiModelProperty(value = "交易时间 格式 yy-mm-dd hh:MM:ss", required = true)
    private String transTime;
    @ApiModelProperty(value = "交易金额", required = true)
    private BigDecimal transAmount;
    @ApiModelProperty(value = "小票号", required = true)
    private String receiptNo;
    @ApiModelProperty(value = "会员id", required = true)
    private String customerId;
    @ApiModelProperty(value = "商品明细")
    private List<ProductItems> items;
}
