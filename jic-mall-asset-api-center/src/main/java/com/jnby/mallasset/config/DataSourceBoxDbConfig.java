package com.jnby.mallasset.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.autoconfigure.SpringBootVFS;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.MybatisXMLLanguageDriver;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * box db config
 * <AUTHOR>
 * @version 1.0
 * @date 1/22/21 4:44 PM
 */
@MapperScan(basePackages = "com.jnby.mallasset.module.mapper.box", sqlSessionFactoryRef = "boxSqlSessionFactoryBean")
@Configuration
public class DataSourceBoxDbConfig {

    @Autowired
    private DataSourceBaseProperties dataSourceBaseProperties;

    @Autowired
    private MybatisPlusProperties properties;


    @Autowired(required = false)
    private Interceptor[] interceptors;

    @Autowired(required = false)
    private DatabaseIdProvider databaseIdProvider;

    @Autowired
    ResourceLoader resourceLoader;


    @Bean("boxDataSourceProperties")
    @Primary
    public DataSourceProperties getDataSourceProperties(){
        DataSourceProperties properties = new DataSourceProperties();
        properties.setUrl(dataSourceBaseProperties.getBoxJdbc());
        properties.setUsername(dataSourceBaseProperties.getBoxUserName());
        properties.setPassword(dataSourceBaseProperties.getBoxPassword());
        properties.setType(DruidDataSource.class);
        return properties;
    }

    @Primary
    @Bean(value = "boxDruidDataSource", initMethod = "init", destroyMethod = "close")
    public DruidDataSource druidDataSource(@Qualifier("boxDataSourceProperties") DataSourceProperties dataSourceProperties){
        DruidDataSource druidDataSource = (DruidDataSource) dataSourceProperties.initializeDataSourceBuilder().build();
        druidDataSource.setInitialSize(dataSourceBaseProperties.getInitialSize());
        druidDataSource.setMaxActive(dataSourceBaseProperties.getMaxActive());
        druidDataSource.setMaxWait(dataSourceBaseProperties.getMaxWait());
        druidDataSource.setMinIdle(dataSourceBaseProperties.getMinIdle());
       // druidDataSource.setRemoveAbandoned(true);
        druidDataSource.setRemoveAbandonedTimeoutMillis(dataSourceBaseProperties.getRemoveAbandonedTimeoutMillis());
        //druidDataSource.setTestOnBorrow(true);
        druidDataSource.setTestWhileIdle(true);
        druidDataSource.setValidationQuery("SELECT 'x' FROM dual");
        druidDataSource.setTimeBetweenEvictionRunsMillis(dataSourceBaseProperties.getTimeBetweenEvictionRunsMillis());
        return druidDataSource;
    }

    @Primary
    @Bean(name = "boxSqlSessionFactoryBean")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("boxDruidDataSource") DruidDataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean mybatisPlus = new MybatisSqlSessionFactoryBean();
        mybatisPlus.setDataSource(dataSource);
        mybatisPlus.setVfs(SpringBootVFS.class);

        String configLocation = this.properties.getConfigLocation();
        if (StringUtils.isNotBlank(configLocation)) {
            mybatisPlus.setConfigLocation(this.resourceLoader.getResource(configLocation));
        }
        mybatisPlus.setConfiguration(properties.getConfiguration());
        mybatisPlus.setPlugins(this.interceptors);
        MybatisConfiguration mc = new MybatisConfiguration();
        mc.setDefaultScriptingLanguage(MybatisXMLLanguageDriver.class);
        mc.setMapUnderscoreToCamelCase(false);// 数据库和java都是驼峰，就不需要
        mybatisPlus.setConfiguration(mc);
        if (this.databaseIdProvider != null) {
            mybatisPlus.setDatabaseIdProvider(this.databaseIdProvider);
        }
        mybatisPlus.setTypeAliasesPackage(this.properties.getTypeAliasesPackage());
        mybatisPlus.setTypeHandlersPackage(this.properties.getTypeHandlersPackage());
        mybatisPlus.setMapperLocations(this.properties.resolveMapperLocations());
        // 设置mapper.xml文件的路径
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] resource = resolver.getResources("classpath:/mapper/box/*.xml");
        mybatisPlus.setMapperLocations(resource);
        return mybatisPlus.getObject();
    }
    @Primary
    @Bean(name = "boxDataSourceTransactionManager")
    public DataSourceTransactionManager transactionManager(@Qualifier("boxDruidDataSource") DruidDataSource boxDruidDataSource) {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(boxDruidDataSource);
        return transactionManager;
    }

    @Primary
    @Bean(name = "boxTransactionTemplate")
    public TransactionTemplate boxTransactionTemplate(@Qualifier("boxDataSourceTransactionManager") DataSourceTransactionManager dataSourceTransactionManager) {
        return new TransactionTemplate(dataSourceTransactionManager);
    }
}
