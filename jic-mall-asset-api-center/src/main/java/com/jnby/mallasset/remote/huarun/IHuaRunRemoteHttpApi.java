package com.jnby.mallasset.remote.huarun;


import com.jnby.mallasset.remote.huarun.entity.*;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.HeaderMap;
import retrofit2.http.POST;

import java.util.Map;


/**
 * 文件名: com.jnby.mallasset.remote.huarun-IHuaRunRemoteHttpApi.java
 * 文件简介: h华润
 *
 * <AUTHOR>
 * @date 2024/7/19 15:56
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface IHuaRunRemoteHttpApi {

    /**
     * 功能描述: 用户登录
     * 使用场景:
     *
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.huarun.entity.BaseHuaRunResp<com.jnby.mallasset.remote.huarun.entity.UserLoginRespEntity>>
     * <AUTHOR>
     * @date 2024/7/19 20:28
     */
    @POST("/1.0/api/user-login")
    Call<BaseHuaRunResp<UserLoginRespEntity>> userLogin(@Body UserLoginReqEntity req);

    /**
     * 功能描述: 订单同步
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooResp>
     * <AUTHOR>
     * @date 2024/7/20 10:01
     */
    @POST("/1.0/api/order-synchronization")
    Call<BaseHuaRunResp<SynchronizationOrderRespEntity>> synchronizationOrder(@HeaderMap Map<String, String> headers, @Body SynchronizationOrderReqEntity req);

//    /**
//     * 功能描述: 退货退款
//     * 使用场景:
//     *
//     * @param headers
//     * @param req
//     * @return retrofit2.Call<com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooResp>
//     * <AUTHOR>
//     * @date 2024/7/20 10:01
//     */
//    @POST("/1.0/api/refund-ord")
//    Call<BaseHuaRunResp<RefundOrderRespEntity>> refundOrder(@HeaderMap Map<String, String> headers, @Body RefundOrderReqEntity req);
//
//    /**
//     * 功能描述: 预退货
//     * 使用场景:
//     *
//     * @param headers
//     * @param req
//     * @return retrofit2.Call<com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooResp>
//     * <AUTHOR>
//     * @date 2024/7/20 10:01
//     */
//    @POST("/1.0/api/prepare-refund-ord")
//    Call<BaseHuaRunResp<PrepareRefundOrderRespEntity>> prepareRefundOrder(@HeaderMap Map<String, String> headers, @Body PrepareRefundOrderReqEntity req);
//
//    /**
//     * 功能描述: 自收银退货
//     * 使用场景:
//     *
//     * @param headers
//     * @param req
//     * @return retrofit2.Call<com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooResp>
//     * <AUTHOR>
//     * @date 2024/7/20 10:01
//     */
//    @POST("/1.0/api/shop-refund-ord")
//    Call<BaseHuaRunResp<RefundOrderRespEntity>> shopRefundOrder(@HeaderMap Map<String, String> headers, @Body RefundOrderReqEntity req);
    /**
     * 功能描述: 会员注册
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.huarun.entity.BaseHuaRunResp<com.jnby.mallasset.remote.huarun.entity.MemberRegisterRespEntity>>
     * <AUTHOR>
     * @date 2024/7/20 15:59
     */
    @POST("/1.0/api/user-reg")
    Call<BaseHuaRunResp<MemberRegisterRespEntity>> memberRegister(@HeaderMap Map<String, String> headers, @Body MemberRegisterReqEntity req);
}
