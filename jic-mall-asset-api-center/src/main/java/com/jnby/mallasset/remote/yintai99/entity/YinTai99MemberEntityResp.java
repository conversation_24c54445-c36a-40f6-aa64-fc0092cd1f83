package com.jnby.mallasset.remote.yintai99.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class YinTai99MemberEntityResp implements Serializable {
    @ApiModelProperty("错误码")
    private int errorCode;
    @ApiModelProperty("错误信息")
    private String errorMessage;
    @ApiModelProperty("会员卡")
    private String vipCode;
    @ApiModelProperty("会员基本资料")
    private List<VipBasicInfo> vipBasicInfo;
    private VipDiscount vipDiscount;
    private List<VipBonus> vipBonus;

}
