package com.jnby.mallasset.remote.k11.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.k11.entity-OrderConsumeEntity.java
 * 文件简介:
 *
 * <AUTHOR>
 * @date 2025/8/7 20:10
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class OrderConsumeEntity implements Serializable {
    @ApiModelProperty(value = "积分成功1，积分失败0")
    private int status;
    @ApiModelProperty(value = "积分失败或不可积分时显示原因")
    private String bonus_description;
    @ApiModelProperty(value = "会员卡号")
    private String member_code;
    @ApiModelProperty(value = "积分值")
    private Long bonus_point;
    @ApiModelProperty(value = "使用的KDP")
    private Long used_kdp;
}
