package com.jnby.mallasset.remote.guangHuan.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class SaveTradeParam {
    @ApiModelProperty("金额（分）, 必须为大于零的整数")
    private int amount;
    @ApiModelProperty("交易来源类型")
    private String changeSubType;
    @ApiModelProperty("消费时间，格式 yyyy-MM-dd HH:mm:ss")
    private String consumptionTime;
    @ApiModelProperty("会员 id")
    private String memberId;
    @ApiModelProperty("商品列表")
    private List<TradeProductDTO> products;
    @ApiModelProperty("广场代码")
    private String projectCode;
    @ApiModelProperty("终端号")
    private String serialNo;
    @ApiModelProperty("流水号")
    private String serialNumber;
    @ApiModelProperty("交易类型 0-正常交易 1-退货")
    private int tradeType;
    @ApiModelProperty("原交易号,退货时传入")
    private String originTransNo;
    @ApiModelProperty("外部交易号需要全局唯一")
    private String transactionNo;
    @ApiModelProperty("付款列表")
    private List<TradePaymentParam> payments;
    @ApiModelProperty("备注")
    private String comments;
}
