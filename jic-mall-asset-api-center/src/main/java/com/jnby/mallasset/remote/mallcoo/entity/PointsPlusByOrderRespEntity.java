package com.jnby.mallasset.remote.mallcoo.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PointsPlusByOrderRespEntity implements Serializable {
    @ApiModelProperty("本次积分")
    private Long Score;
    @ApiModelProperty("当前总积分")
    private Long CurScore;
    @ApiModelProperty("订单号")
    private String ordNum;
    @ApiModelProperty("操作时间")
    private String OperateTime;
    @ApiModelProperty("业务ID，唯一值")
    private String exchangeId;
    @ApiModelProperty("外部单号")
    private String exchangeNo;
}
