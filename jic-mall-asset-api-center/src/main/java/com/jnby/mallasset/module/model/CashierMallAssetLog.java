package com.jnby.mallasset.module.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jnby.mallasset.enums.AssetTypeEnum;
import com.jnby.mallasset.enums.OperationStatusTypeEnum;
import com.jnby.mallasset.enums.OperationTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-07-24 16:41:49
 * @Description: 商场资产操作流水
 */
@TableName("CASHIER_MALL_ASSET_LOG")
@ApiModel(value = "CashierMallAssetLog对象", description = "商场资产操作流水")
@Data
public class CashierMallAssetLog implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "0未删除、1删除")
    @TableField("IS_DELETE")
    private Boolean isDelete;
    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;
    /**
     * {@link AssetTypeEnum}
     */
    @ApiModelProperty(value = "资产类型.1=积分、2=优惠券")
    @TableField("ASSET_TYPE")
    private Integer assetType;
    /**
     * {@link OperationTypeEnum}
     */
    @ApiModelProperty(value = "操作类型.1=使用，2=返还，3=冻结，4=解冻")
    @TableField("OPERATION_TYPE")
    private Integer operationType;
    @ApiModelProperty(value = "积分值")
    @TableField("POINTS")
    private Long points;
    @ApiModelProperty(value = "用户ID:默认为手机号")
    @TableField("CUSTOMER_ID")
    private String customerId;
    @ApiModelProperty(value = "券号")
    @TableField("COUPON_NO")
    private String couponNo;
    @ApiModelProperty(value = "批量业务处理ID")
    @TableField("BIZ_ID")
    private String bizId;
    @ApiModelProperty(value = "订单号")
    @TableField("ORDER_NO")
    private String orderNo;
    /**
     * {@link OperationStatusTypeEnum}
     */
    @ApiModelProperty(value = "操作结果.1=成功,2=失败")
    @TableField("OPT_STATUS")
    private Integer optStatus;
    @ApiModelProperty(value = "操作失败的原因（200个字符）")
    @TableField("OPT_FAIL_MSG")
    private String optFailMsg;
    @ApiModelProperty(value = "伯俊门店CODE")
    @TableField("BJ_STORE_ID")
    private String bjStoreId;
    @ApiModelProperty(value = "猫酷券核销时间")
    @TableField("MALL_COO_USE_TIME")
    private String mallCooUseTime;
    @ApiModelProperty(value = "猫酷取消核销traceId")
    @TableField("MALL_COO_RETURN_TRACE_ID")
    private String mallCooReturnTraceId;

    @ApiModelProperty(value = "券模版号")
    @TableField("TEMPLATE_NO")
    private String templateNo;
    @ApiModelProperty(value = "券模版面额")
    @TableField("TEMPLATE_AMOUNT")
    private BigDecimal templateAmount;
}
