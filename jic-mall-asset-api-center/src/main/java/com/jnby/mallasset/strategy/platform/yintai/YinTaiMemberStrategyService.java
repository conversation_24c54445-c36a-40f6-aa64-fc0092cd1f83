package com.jnby.mallasset.strategy.platform.yintai;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gitee.sop.sdk.client.OpenClient;
import com.gitee.sop.sdk.request.CommonRequest;
import com.gitee.sop.sdk.response.CommonResponse;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.remote.yintai.entity.YinTaiMemberRespEntity;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.TreeMap;

/**
 * 银泰会员平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.YIN_TAI, category = PlatformCategoryTypeEnum.MEMBER)
@Slf4j
public class YinTaiMemberStrategyService extends AbstractMemberService {

    @Autowired
    private CashierMallMemberLogMapper cashierMallMemberLogMapper;

    @Value("${yinTai.url}")
    private String yinTaiUrl;

    @Override
    public ResponseResult openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef assetStoreRef) {
        log.info("银泰注册会员输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult commonResponse = new ResponseResult();
//        CommonMemberResponse commonMemberResponse = new CommonMemberResponse();
//        try {
//            // 记录流水
//            CashierMallMemberLog cashierMallMemberLog = recordCashierMallMemberLog(memberRegisterReq, assetStoreRef);
//            // 查询会员是否注册
//            ResponseResult<YinTaiMemberRespEntity> result = getMemberInfo(memberRegisterReq,assetStoreRef);
//            if(result.getCode() == 200){
//                commonMemberResponse.setMemberFlag(1);
//                YinTaiMemberRespEntity memberRespEntity = result.getData();
//                commonMemberResponse.setMemberId(memberRespEntity.getVipId());
//                commonResponse.setMsg(result.getMsg());
//                commonResponse.setData(commonMemberResponse);
//                // 更新执行标识
//                cashierMallMemberLog.setOpenUserId(memberRespEntity.getVipId());
//                cashierMallMemberLog.setIsExecute("Y");
//                cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
//                return commonResponse;
//            }
//            // 新会员走注册逻辑
//            CommonRequest request = new CommonRequest("user.userinfo.get");
//            // 请求参数
//            Map<String, Object> bizModel = new TreeMap<>();
//            bizModel.put("mobile", memberRegisterReq.getMobile());
//            bizModel.put("channel", "jnby");
//            bizModel.put("source", "source11");
//            bizModel.put("name", memberRegisterReq.getNickName());
//            bizModel.put("requestId", IdUtil.simpleUUID());
//            bizModel.put("storeNo", "HZ01");
//            request.setBizModel(bizModel);
//            log.info("银泰注册会员调用远程接口参数：{}", JSON.toJSONString(bizModel));
//            OpenClient client = new OpenClient(yinTaiUrl, assetStoreRef.getPlatformAppId(), assetStoreRef.getPlatformPrivateKey(),assetStoreRef.getPlatformPublicKey());
//            CommonResponse response = client.execute(request);
//            commonResponse.setMsg(response.getMsg());
//            if(response.isSuccess() && response.getCode().equals("10000")){
//                if(response.getBody() != null){
//                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
//                    YinTaiMemberRespEntity memberRespEntity = JSONObject.parseObject(response.getBody(), YinTaiMemberRespEntity.class);
//                    commonMemberResponse.setMemberFlag(0);
//                    commonMemberResponse.setMemberId(memberRespEntity.getVipId());
//                    commonResponse.setData(commonMemberResponse);
//                }
//            }else {
//                commonResponse.setCode(Integer.parseInt(response.getCode()));
//                commonResponse.setMsg(response.getMsg());
//            }
//        }catch (Exception e){
//            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
//            commonResponse.setMsg(e.getMessage());
//            log.info("{} 银泰注册会员异常",memberRegisterReq.getMobile(), e);
//        }
        return commonResponse;
    }

    private CashierMallMemberLog recordCashierMallMemberLog(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallMemberLog cashierMallMemberLog = new CashierMallMemberLog();
        cashierMallMemberLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallMemberLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallMemberLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallMemberLog.setBjStoreId(memberRegisterReq.getStoreId());
        cashierMallMemberLog.setMobile(memberRegisterReq.getMobile());
        cashierMallMemberLog.setIsExecute("N");
        cashierMallMemberLogMapper.insertEntity(cashierMallMemberLog);
        return cashierMallMemberLog;
    }

    @Override
    public ResponseResult<YinTaiMemberRespEntity> getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef assetStoreRef) {
        log.info("银泰查询会员输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult<YinTaiMemberRespEntity> commonResponse = new ResponseResult<>();
        OpenClient client = new OpenClient(yinTaiUrl, assetStoreRef.getPlatformAppId(), assetStoreRef.getPlatformPrivateKey(), assetStoreRef.getPlatformPublicKey());
        try {
            CommonRequest request = new CommonRequest("vip.info.getOne");
            // 请求参数
            String platformMallId = assetStoreRef.getPlatformMallId();
            Map<String, Object> bizModel = new TreeMap<>();
            bizModel.put("channel", platformMallId);
            bizModel.put("requestId", platformMallId + "-" + IdUtil.simpleUUID());
            bizModel.put("mobile", memberRegisterReq.getMobile());
            bizModel.put("storeNo", "HZ02");
            request.setBizModel(bizModel);
            log.info("银泰查询会员调用远程接口参数：{}", JSON.toJSONString(bizModel));
            CommonResponse response = client.execute(request);
            log.info("银泰查询会员返回参数：{}", JSON.toJSONString(response));
            if (StringUtils.isNotBlank(response.getBody())) {
                JSONObject jsonObject = JSONObject.parseObject(response.getBody());
                YinTaiMemberRespEntity entity = jsonObject.getObject("data", YinTaiMemberRespEntity.class);
                if (entity != null) {
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    commonResponse.setMsg(response.getSubMsg());
                    commonResponse.setData(entity);
                } else {
                    throw new RuntimeException("用户不存在");
                }
            } else {
                throw new RuntimeException("回参为空");

            }
        } catch (Exception e) {
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 银泰查询会员异常", memberRegisterReq.getMobile(), e);
        }
        return commonResponse;
    }
}
