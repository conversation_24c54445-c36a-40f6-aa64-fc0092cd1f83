package com.jnby.mallasset.remote.mallcoo.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Builder
@ApiModel("券使用检查入参")
@Data
public class CouponCheckReqEntity implements Serializable {

    @ApiModelProperty("券码")
    @JSONField(ordinal = 1, name = "VCode")
    private String VCode;
    @ApiModelProperty("猫酷核销商户ID")
    @JSONField(ordinal = 2, name = "McShopID")
    private Long McShopID;

}
