package com.jnby.mallasset.gateway.common;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/9/23 14:12
 * @Description: 编程校验token有效性
 */
@Slf4j
public class TokenUtils {
    public static final String PREFIX_USER_TOKEN  = "prefix_user_token_";
    /**
     * 获取 request 里传递的 token
     *
     * @param headers
     * @return
     */
    public static String getTokenByRequest(HttpHeaders headers) {
        List<String> token = headers.get("X-Access-Token");
        if (token == null || token.isEmpty()) {
            return null;
        }
        return token.get(0);
    }


    /**
     * 刷新token（保证用户在线操作不掉线）
     * @param token
     * @param redisUtil
     * @return
     */
    public static boolean verifyToken(String token, RedisUtil redisUtil) {
        log.info("验证token合法性: token.....");
        String cacheToken = String.valueOf(redisUtil.get(PREFIX_USER_TOKEN + token));
        if (StringUtils.isNotEmpty(cacheToken)){
            return true;
        }
        return false;
    }
}
