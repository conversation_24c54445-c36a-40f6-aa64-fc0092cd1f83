package com.jnby.mallasset.strategy.platform.kaide;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.remote.kaide.IKaiDeRemoteHttpApi;
import com.jnby.mallasset.remote.kaide.constant.FieldConstant;
import com.jnby.mallasset.remote.kaide.entity.BaseKaiDeResp;
import com.jnby.mallasset.remote.kaide.entity.KaiDeMemberRespEntity;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.HashMap;
import java.util.Map;

/**
 * 凯德会员平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.KAI_DE, category = PlatformCategoryTypeEnum.MEMBER)
@AllArgsConstructor
@Slf4j
public class KaiDeMemberStrategyService extends AbstractMemberService {

    private IKaiDeRemoteHttpApi kaiDeRemoteHttpApi;

    @Override
    public ResponseResult openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult<KaiDeMemberRespEntity> getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("凯德查询会员输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult<KaiDeMemberRespEntity> commonResponse = new ResponseResult<>();
        try{
            Map<String, String> header = new HashMap<>();
            header.put(FieldConstant.HEADER_SUBSCRIPTION_KEY,cashierMallAssetStoreRef.getPlatformPublicKey());
            header.put(FieldConstant.HEADER_TOKEN,cashierMallAssetStoreRef.getPlatformPrivateKey());
            log.info("凯德查询会员调用远程接口参数：{}", memberRegisterReq.getMobile());
            Response<BaseKaiDeResp<KaiDeMemberRespEntity>> execute = kaiDeRemoteHttpApi.getMemberInfo(header,memberRegisterReq.getMobile()).execute();
            log.info("凯德查询会员返回参数：{}", JSON.toJSONString(execute));
            BaseKaiDeResp<KaiDeMemberRespEntity> body = execute.body();
            if(body != null){
                if(body.getErrorCode() == 0){
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                }else {
                    commonResponse.setCode(body.getErrorCode());
                }
                if(body.getBody() != null){
                    commonResponse.setData(body.getBody());
                }
                commonResponse.setMsg(body.getMessage());
            }else {
                if(execute.errorBody() != null){
                    String errorBody = execute.errorBody().string();
                    BaseKaiDeResp<KaiDeMemberRespEntity> baseKaiDeResp = JSONObject.parseObject(errorBody,BaseKaiDeResp.class);
                    commonResponse.setCode(baseKaiDeResp.getErrorCode());
                    commonResponse.setMsg(baseKaiDeResp.getMessage());
                    commonResponse.setData(baseKaiDeResp.getBody());
                }
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 凯德查询会员异常",memberRegisterReq.getMobile(), e);
        }
        return commonResponse;
    }
}
