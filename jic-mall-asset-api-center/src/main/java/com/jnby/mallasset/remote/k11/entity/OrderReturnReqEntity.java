package com.jnby.mallasset.remote.k11.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 文件名: com.jnby.mallasset.remote.haiancheng.entity-BaseHacResp.java
 * 文件简介: 海岸城基础返回对象
 *
 * <AUTHOR>
 * @date 2025/1/13 9:51
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class OrderReturnReqEntity implements Serializable {
    @ApiModelProperty(value = "销售单号")
    private String order_code;
    @ApiModelProperty(value = "退单单号")
    private String return_code;
    @ApiModelProperty(value = "原销售单号店铺编码")
    private String store_code;
    @ApiModelProperty(value = "原销售单号销售时间,时间戳")
    private Long sales_time;
    @ApiModelProperty(value = "ALL”全单退，”PART” 非全单退")
    private String type;
    @ApiModelProperty(value = "退款金额")
    private BigDecimal return_amount;
    @ApiModelProperty(value = "退单时间，时间戳")
    private Long return_at;

}
