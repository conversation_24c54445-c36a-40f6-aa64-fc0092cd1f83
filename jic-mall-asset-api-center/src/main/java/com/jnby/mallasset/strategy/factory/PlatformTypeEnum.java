package com.jnby.mallasset.strategy.factory;

import com.jnby.mallasset.config.exception.MallException;
import com.jnby.mallasset.config.exception.SystemErrorEnum;

import java.util.stream.Stream;

/**
 * 平台枚举
 */
public enum PlatformTypeEnum {

    MALL_COO(1, "猫酷"),
    YIN_TAI(2, "银泰"),
    HUA_RUN(3, "华润"),
    OELI(4, "天目里"),
    DA_RONG_CHENG(5, "大融城"),
    KAI_DE(6, "凯德"),
    WAN_KE(7, "万科"),
    KK_MALL(8, "KKMALL"),
    GUANG_HUAN(9, "重庆光环"),
    LONG_HU(10, "龙湖"),

    DA_YUE_CHENG(12, "大悦城"),
    XIE_XIN(11, "协信"),
    HAI_AN_CHENG(13, "海岸城"),
    YIN_TAI_99(14, "银泰in99"),
    ;
    /**
     * 商场编号
     */
    private final Integer code;

    /**
     * 商场名称
     */
    private final String name;

    PlatformTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 通过code获取对于枚举对象
     */
    public static PlatformTypeEnum getEnumByCode(Integer code) {
        return Stream.of(PlatformTypeEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new MallException(SystemErrorEnum.MALL_IS_NULL_ERROR));
    }
}
