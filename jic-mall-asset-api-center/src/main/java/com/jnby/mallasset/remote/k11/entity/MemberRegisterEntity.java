package com.jnby.mallasset.remote.k11.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.haiancheng.entity-BaseHacResp.java
 * 文件简介: 海岸城基础返回对象
 *
 * <AUTHOR>
 * @date 2025/1/13 9:51
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class MemberRegisterEntity implements Serializable {
    @ApiModelProperty(value = "会员编号")
    private String member_id;
    @ApiModelProperty(value = "会员中文名称")
    private String nick_name;
    @ApiModelProperty(value = "会员卡号")
    private String vip_code;
    @ApiModelProperty(value = "会员等级")
    private String level_id;
    @ApiModelProperty(value = "是否绑定KDP：0-否；1-是")
    private int is_bind_kdp;
    @ApiModelProperty(value = "积分")
    private String spoint;
    @ApiModelProperty(value = "kdp积分")
    private Double kpoint;

}
