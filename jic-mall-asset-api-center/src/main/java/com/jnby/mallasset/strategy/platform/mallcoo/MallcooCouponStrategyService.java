package com.jnby.mallasset.strategy.platform.mallcoo;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jnby.mallasset.api.dto.coupon.CouponInfoRespDto;
import com.jnby.mallasset.config.exception.MallException;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.convert.CouponConvertor;
import com.jnby.mallasset.enums.OperationStatusTypeEnum;
import com.jnby.mallasset.module.model.CashierMallAssetLog;
import com.jnby.mallasset.remote.mallcoo.IMallCooRemoteHttpApi;
import com.jnby.mallasset.remote.mallcoo.MallCooUtils;
import com.jnby.mallasset.remote.mallcoo.entity.*;
import com.jnby.mallasset.strategy.category.AbstractCouponService;
import com.jnby.mallasset.strategy.context.CouponContext;
import com.jnby.mallasset.strategy.context.UserContext;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 猫酷平台优惠券策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.MALL_COO, category = PlatformCategoryTypeEnum.COUPON)
@Slf4j
public class MallcooCouponStrategyService extends AbstractCouponService {
    @Autowired
    private IMallCooRemoteHttpApi mallCooRemoteHttpApi;
    @Autowired
    private CouponConvertor couponConvertor;

    @Override
    public void checkUser(UserContext user) {
        Preconditions.checkArgument(Objects.nonNull(user), "必要信息不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(user.getPhone()), "消费者不能为空");
        Preconditions.checkArgument(Objects.nonNull(user.getStoreConfig()), "门店配置不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(user.getStoreConfig().getPlatformAppId()), "商家应用ID不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(user.getStoreConfig().getPlatformPublicKey()), "商家公钥不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(user.getStoreConfig().getPlatformPrivateKey()), "商家私钥不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(user.getStoreConfig().getPlatformMallStoreId()), "商家门店ID不能为空");
    }


    @Override
    public void preCheckUseCoupon(UserContext user, CouponContext req) {
        Preconditions.checkArgument(Objects.nonNull(req), "必要请求信息不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getOrderNo()), "订单号不能为空");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(req.getCouponNoList()), "券号不能为空");
        String appId = user.getStoreConfig().getPlatformAppId();
        String platformPublicKey = user.getStoreConfig().getPlatformPublicKey();
        String platformPrivateKey = user.getStoreConfig().getPlatformPrivateKey();
        String platformMallStoreId = user.getStoreConfig().getPlatformMallStoreId();
        List<String> couponNoList = req.getCouponNoList();
        for (String couponNo : couponNoList) {
            try {
                CouponCheckReqEntity reqEntity = CouponCheckReqEntity.builder().VCode(couponNo).McShopID(Long.valueOf(platformMallStoreId)).build();
                Map<String, String> headers = MallCooUtils.getHeaders(appId, platformPublicKey, platformPrivateKey, JSON.toJSONString(reqEntity));
                log.info("猫酷优惠券使用前检查券调用远程接口入参，headers:[{}]--------------req:[{}]", JSON.toJSONString(headers), JSON.toJSONString(reqEntity));
                Response<BaseMallCooResp<CouponCheckRespEntity>> execute = mallCooRemoteHttpApi.checkCoupon(headers, reqEntity).execute();
                boolean successful = execute.isSuccessful();
                if (!successful) {
                    throw new MallException(SystemErrorEnum.COUPON_USE_CHECK_REQUEST_ERROR);
                }
                BaseMallCooResp<CouponCheckRespEntity> body = execute.body();
                log.info("猫酷优惠券使用前检查调用远程接口响应：{}", JSON.toJSONString(body));
                if (!body.isSuccess()) {
                    throw new MallException(body.getMessage());
                }
                CouponCheckRespEntity data = body.getData();
                if (data == null || data.getUseState() == null) {
                    throw new MallException(SystemErrorEnum.COUPON_USE_CHECK_RESPONSE_ERROR);
                }
                Integer useState = data.getUseState();
                if (Objects.equals(useState, 1)) {
                    throw new MallException(SystemErrorEnum.COUPON_USE_CHECK_IS_USED_ERROR);
                }
            } catch (IOException e) {
                log.error("猫酷优惠券使用前检查超时", e);
                throw new MallException(SystemErrorEnum.COUPON_USE_TIMEOUT_ERROR);
            } catch (MallException e) {
                log.error("猫酷优惠券使用前检查已知异常", e);
                throw e;
            } catch (Exception e) {
                log.error("猫酷优惠券使用前检查异常", e);
                throw new MallException(SystemErrorEnum.COUPON_USE_UNKNOWN_ERROR);
            }
        }
    }

    @Override
    public void doUseCoupon(UserContext user, CouponContext context) {
        String appId = user.getStoreConfig().getPlatformAppId();
        String platformPublicKey = user.getStoreConfig().getPlatformPublicKey();
        String platformPrivateKey = user.getStoreConfig().getPlatformPrivateKey();
        String platformMallStoreId = user.getStoreConfig().getPlatformMallStoreId();
        try {
            List<CouponBatchUseReqEntity.UseInfoList> reqList = couponConvertor.mallCooCouponNoList2UseList(context.getCouponNoList());
            CouponBatchUseReqEntity reqEntity = CouponBatchUseReqEntity.builder().Verification("3").IsAllSuccess(true)
                    .McShopID(Long.valueOf(platformMallStoreId)).UseInfoList(reqList).OrderNo(context.getOrderNo()).build();
            Map<String, String> headers = MallCooUtils.getHeaders(appId, platformPublicKey, platformPrivateKey, JSON.toJSONString(reqEntity));
            log.info("猫酷优惠券使用调用远程接口入参，headers:[{}]--------------req:[{}]", JSON.toJSONString(headers), JSON.toJSONString(reqEntity));
            Response<BaseMallCooResp<CouponBatchUseRespEntity>> execute = mallCooRemoteHttpApi.batchUseCouponByVCodes(headers, reqEntity).execute();
            boolean successful = execute.isSuccessful();
            if (!successful) {
                throw new MallException(SystemErrorEnum.COUPON_USE_REQUEST_ERROR);
            }
            BaseMallCooResp<CouponBatchUseRespEntity> body = execute.body();
            log.info("猫酷优惠券使用调用远程接口响应：{}", JSON.toJSONString(body));
            if (!body.isSuccess()) {
                throw new MallException(body.getMessage());
            }
            CouponBatchUseRespEntity data = body.getData();
            if (data == null || CollectionUtils.isEmpty(data.getUseInfoList())) {
                throw new MallException(SystemErrorEnum.COUPON_USE_RESPONSE_ERROR);
            }
            List<CouponBatchUseRespEntity.VCodeResultInfo> useInfoList = data.getUseInfoList();
            boolean presentError = useInfoList.stream().anyMatch(rsp -> !Objects.equals(1, rsp.getCode()));
            if (presentError) {
                throw new MallException(SystemErrorEnum.COUPON_USE_PARTIAL_FAILURE_ERROR);
            }
            // 处理成功，记录核销时间
            useInfoList.forEach(rsp -> {
                CashierMallAssetLog cashierMallAssetLog = context.getVcode2LogMap().get(rsp.getVCode());
                if (Objects.nonNull(cashierMallAssetLog)) {
                    cashierMallAssetLog.setMallCooUseTime(rsp.getOperatorTime());
                }
            });
        } catch (IOException e) {
            log.error("猫酷优惠券使用超时", e);
            throw new MallException(SystemErrorEnum.COUPON_USE_TIMEOUT_ERROR);
        } catch (MallException e) {
            log.error("猫酷优惠券使用已知异常", e);
            throw e;
        } catch (Exception e) {
            log.error("猫酷优惠券使用异常", e);
            throw new MallException(SystemErrorEnum.COUPON_USE_UNKNOWN_ERROR);
        }
    }

    @Override
    public void preCheckReturnCoupon(UserContext user, CouponContext req) {
        Preconditions.checkArgument(Objects.nonNull(req), "必要请求信息不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getOrderNo()), "订单号不能为空");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(req.getCouponNoList()), "券号不能为空");
    }

    @Override
    public void doReturnCoupon(UserContext user, CouponContext context) {
        String appId = user.getStoreConfig().getPlatformAppId();
        String platformPublicKey = user.getStoreConfig().getPlatformPublicKey();
        String platformPrivateKey = user.getStoreConfig().getPlatformPrivateKey();
        String platformMallStoreId = user.getStoreConfig().getPlatformMallStoreId();
        try {
            List<CouponBatchReturnReqEntity.ReturnCoupon> couponInfoList = couponConvertor.mallCooCouponNoList2ReturnList(context.getCouponNoList());
            CouponBatchReturnReqEntity reqEntity = CouponBatchReturnReqEntity.builder().CouponInfoList(couponInfoList).build();
            Map<String, String> headers = MallCooUtils.getHeaders(appId, platformPublicKey, platformPrivateKey, JSON.toJSONString(reqEntity));
            log.info("猫酷优惠券返还调用远程接口入参，headers:[{}]--------------req:[{}]", JSON.toJSONString(headers), JSON.toJSONString(reqEntity));
            Response<BaseMallCooResp<CouponBatchReturnRespEntity>> execute = mallCooRemoteHttpApi.batchReturnCouponByVCodes(headers, reqEntity).execute();
            boolean successful = execute.isSuccessful();
            if (!successful) {
                throw new MallException(SystemErrorEnum.COUPON_RETURN_REQUEST_ERROR);
            }
            BaseMallCooResp<CouponBatchReturnRespEntity> body = execute.body();
            log.info("猫酷优惠券返还调用远程接口响应：{}", JSON.toJSONString(body));
            if (!body.isSuccess()) {
                throw new MallException(body.getMessage());
            }
            CouponBatchReturnRespEntity data = body.getData();
            if (data == null || data.getCouponInfoList() == null) {
                throw new MallException(SystemErrorEnum.COUPON_RETURN_RESPONSE_ERROR);
            }

            // 记录结果
            List<CouponBatchReturnRespEntity.ReturnCoupon> resultCouponInfoList = data.getCouponInfoList();
            for (CouponBatchReturnRespEntity.ReturnCoupon returnCoupon : resultCouponInfoList) {
                Map<String, CashierMallAssetLog> vcode2LogMap = context.getVcode2LogMap();
                CashierMallAssetLog cashierMallAssetLog = vcode2LogMap.get(returnCoupon.getVCode());
                if (Objects.nonNull(cashierMallAssetLog)) {
                    cashierMallAssetLog.setMallCooReturnTraceId(returnCoupon.getTraceID());
                    if (Boolean.FALSE.equals(returnCoupon.getIsSuccess())) {
                        cashierMallAssetLog.setOptStatus(OperationStatusTypeEnum.FAIL.getCode());
                        String msg = returnCoupon.getMsg();
                        cashierMallAssetLog.setOptFailMsg((msg != null && msg.length() > 200) ? msg.substring(0, 200) : msg);
                    }
                }
            }
        } catch (IOException e) {
            log.error("猫酷优惠券返还超时", e);
            throw new MallException(SystemErrorEnum.COUPON_RETURN_TIMEOUT_ERROR);
        } catch (MallException e) {
            log.error("猫酷优惠券返还已知异常", e);
            throw e;
        } catch (Exception e) {
            log.error("猫酷优惠券返还异常", e);
            throw new MallException(SystemErrorEnum.COUPON_RETURN_UNKNOWN_ERROR);
        }
    }

    @Override
    public void preCheckSendCoupon(UserContext user, CouponContext req) {
    }

    @Override
    public void doSendCoupon(UserContext user, CouponContext context) {
        String appId = user.getStoreConfig().getPlatformAppId();
        String platformPublicKey = user.getStoreConfig().getPlatformPublicKey();
        String platformPrivateKey = user.getStoreConfig().getPlatformPrivateKey();
        String platformMallStoreId = user.getStoreConfig().getPlatformMallStoreId();
        try {
            // 目前只有停车券，未来如果有多个类型的券，得考虑用枚举区分取值。
            CouponBatchSendReqEntity.UseInfoList useInfo = new CouponBatchSendReqEntity.UseInfoList(context.getTraceId(),
                    user.getMallConfig().getParkingCouponTemplateNo(), user.getPhone());
            CouponBatchSendReqEntity reqEntity = CouponBatchSendReqEntity.builder().UserList(Lists.newArrayList(useInfo)).build();
            Map<String, String> headers = MallCooUtils.getHeaders(appId, platformPublicKey, platformPrivateKey, JSON.toJSONString(reqEntity));
            log.info("猫酷优惠券发放 调用远程接口入参，headers:[{}]--------------req:[{}]", JSON.toJSONString(headers), JSON.toJSONString(reqEntity));
            Response<BaseMallCooResp<List<CouponBatchSendRespEntity>>> execute = mallCooRemoteHttpApi.sendCouponByMobile(headers, reqEntity).execute();
            boolean successful = execute.isSuccessful();
            if (!successful) {
                throw new MallException(SystemErrorEnum.COUPON_SEND_REQUEST_ERROR);
            }
            BaseMallCooResp<List<CouponBatchSendRespEntity>> body = execute.body();
            log.info("猫酷优惠券发放 调用远程接口响应：{}", JSON.toJSONString(body));
            if (!body.isSuccess()) {
                throw new MallException(body.getMessage());
            }
            List<CouponBatchSendRespEntity> data = body.getData();
            if (data == null || CollectionUtils.isEmpty(data) || data.get(0) == null) {
                throw new MallException(SystemErrorEnum.COUPON_USE_RESPONSE_ERROR);
            }
            if (StringUtil.isNotBlank(data.get(0).getFailReason())) {
                throw new MallException(data.get(0).getFailReason());
            }
            // 券码
            context.setCouponNoList(Lists.newArrayList(data.get(0).getVCode()));
           
        } catch (IOException e) {
            log.error("猫酷优惠券发放 超时", e);
            throw new MallException(SystemErrorEnum.COUPON_SEND_TIMEOUT_ERROR);
        } catch (MallException e) {
            log.error("猫酷优惠券发放 已知异常", e);
            throw e;
        } catch (Exception e) {
            log.error("猫酷优惠券发放 异常", e);
            throw new MallException(SystemErrorEnum.COUPON_SEND_UNKNOWN_ERROR);
        }
    }

    @Override
    public List<CouponInfoRespDto> doList(UserContext user) {
        String appId = user.getStoreConfig().getPlatformAppId();
        String platformPublicKey = user.getStoreConfig().getPlatformPublicKey();
        String platformPrivateKey = user.getStoreConfig().getPlatformPrivateKey();
        String platformMallStoreId = user.getStoreConfig().getPlatformMallStoreId();
        String phone = user.getPhone();
        try {
            CouponCanUseListReqEntity reqEntity = CouponCanUseListReqEntity.builder().MinID(0).PageSize(100)
                    .McShopID(Long.valueOf(platformMallStoreId)).Mobile(phone).build();
            Map<String, String> headers = MallCooUtils.getHeaders(appId, platformPublicKey, platformPrivateKey, JSON.toJSONString(reqEntity));
            log.info("猫酷优惠券获取调用远程接口入参，headers:[{}]--------------req:[{}]", JSON.toJSONString(headers), JSON.toJSONString(reqEntity));
            Response<BaseMallCooResp<CouponCanUseListRespEntity>> execute = mallCooRemoteHttpApi.listCanUseCouponByMobile(headers, reqEntity).execute();
            log.info("猫酷优惠券获取调用远程接口响应：{}", JSON.toJSONString(execute));
            boolean successful = execute.isSuccessful();
            if (!successful) {
                throw new MallException(SystemErrorEnum.COUPON_LIST_REQUEST_ERROR);
            }
            BaseMallCooResp<CouponCanUseListRespEntity> body = execute.body();
            if (!body.isSuccess()) {
                // 猫酷返回的错误码为6，表示暂无数据，理论上这是正常的业务逻辑
                if (body.getCode() == 6) {
                    return Collections.emptyList();
                }
                throw new MallException(body.getMessage());
            }
            CouponCanUseListRespEntity data = body.getData();
            if (data == null) {
                throw new MallException(SystemErrorEnum.COUPON_LIST_RESPONSE_ERROR);
            }
            // 按照ReduceMoney减免金额倒序排列
            List<CouponCanUseListRespEntity.CouponInfo> list = Optional.ofNullable(data.getCouponInfoList())
                    .orElse(Lists.newArrayList())
                    .stream().sorted(Comparator.comparing(CouponCanUseListRespEntity.CouponInfo::getReduceMoney).reversed())
                    .collect(Collectors.toList());
            return couponConvertor.mallCooEntityList2DtoList(list);
        } catch (IOException e) {
            log.error("猫酷优惠券查询超时", e);
            throw new MallException(SystemErrorEnum.COUPON_LIST_TIMEOUT_ERROR);
        } catch (MallException e) {
            log.error("猫酷优惠券查询已知异常", e);
            throw e;
        } catch (Exception e) {
            log.error("猫酷优惠券查询异常", e);
            throw new MallException(SystemErrorEnum.COUPON_LIST_UNKNOWN_ERROR);
        }
    }
}
