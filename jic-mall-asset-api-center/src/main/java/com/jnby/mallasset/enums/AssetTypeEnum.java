package com.jnby.mallasset.enums;

/**
 * 资产枚举
 */
public enum AssetTypeEnum {

    POINTS(1, "积分"),
    COUPON(2, "券"),
    ;
    /**
     * 编号
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;

    AssetTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
