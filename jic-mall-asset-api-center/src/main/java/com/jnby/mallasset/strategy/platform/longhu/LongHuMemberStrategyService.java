package com.jnby.mallasset.strategy.platform.longhu;

import com.jnby.common.ResponseResult;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.LONG_HU, category = PlatformCategoryTypeEnum.MEMBER)
@Slf4j
public class LongHuMemberStrategyService extends AbstractMemberService {

    @Override
    public ResponseResult openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }
}
