package com.jnby.mallasset.remote.haiancheng.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.haiancheng.entity-BaseHacResp.java
 * 文件简介: 海岸城基础返回对象
 *
 * <AUTHOR>
 * @date 2025/1/13 9:51
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class BaseHacResp<T> implements Serializable {
    @ApiModelProperty(value = "结果")
    private String code;
    @ApiModelProperty(value = "返回消息")
    private String msg;
    @ApiModelProperty("响应数据")
    private T data;

    public boolean isSuccess(){
        if (this.code.equals("ok")) {
            return true;
        }
        return false;
    }
}
