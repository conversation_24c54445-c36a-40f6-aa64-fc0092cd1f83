package com.jnby.mallasset.remote.wanke.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class WanKeMemberRespEntity implements Serializable {
    @ApiModelProperty(value = "会员ID")
    private String memberId;
    @ApiModelProperty(value = "会员手机号")
    private String mobileNo;
    @ApiModelProperty(value = "会员等级id")
    private Integer ratingId;
    @ApiModelProperty(value = "会员等级名称")
    private String ratingName;
    @ApiModelProperty(value = "会员名称(姓名)")
    private String memberName;
    @ApiModelProperty(value = "昵称")
    private String nickName;
}
