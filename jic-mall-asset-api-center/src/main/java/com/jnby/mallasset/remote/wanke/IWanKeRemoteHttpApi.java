package com.jnby.mallasset.remote.wanke;


import com.jnby.mallasset.remote.huarun.entity.UserLoginReqEntity;
import com.jnby.mallasset.remote.wanke.entity.*;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.Map;


/**
 * 万科
 */
public interface IWanKeRemoteHttpApi {

    /**
     * 功能描述: 会员查询-仅线上
     * 使用场景:
     *
     * @param paramMap
     * @return retrofit2.Call<com.jnby.mallasset.remote.wanke.entity.BaseWanKeResp<com.jnby.mallasset.remote.wanke.entity.WanKeMemberRespEntity>>
     * <AUTHOR>
     * @date 2024/7/29 18:02
     */
    @GET("/yinli-openapi/api/v1/member")
    Call<BaseWanKeResp<WanKeMemberRespEntity>> queryMember(@HeaderMap Map<String, String> headers,@QueryMap Map<String,Object> paramMap);
    /**
     * 功能描述: 会员查询-仅测试
     * 使用场景:
     *
     * @param paramMap
     * @return retrofit2.Call<com.jnby.mallasset.remote.wanke.entity.BaseWanKeResp<com.jnby.mallasset.remote.wanke.entity.WanKeMemberRespEntity>>
     * <AUTHOR>
     * @date 2024/7/29 18:02
     */
    @GET("/openapi-uat/api/v1/member")
    Call<BaseWanKeResp<WanKeMemberRespEntity>> queryMemberFromTest(@HeaderMap Map<String, String> headers,@QueryMap Map<String,Object> paramMap);

    /**
     * 功能描述: 会员注册
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.wanke.entity.BaseWanKeResp<com.jnby.mallasset.remote.wanke.entity.WanKeMemberRespEntity>>
     * <AUTHOR>
     * @date 2024/7/29 19:11
     */
    @POST("/yinli-openapi/api/v1/member/register")
    Call<BaseWanKeResp<WanKeMemberRespEntity>> registerMember(@HeaderMap Map<String, String> headers,@Body WanKeMemberReqEntity req);

    /**
     * 功能描述: 消费加积分
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.wanke.entity.BaseWanKeResp>
     * <AUTHOR>
     * @date 2024/7/30 13:18
     */
    @POST("/yinli-openapi/api/v1/order/yueshang/pointOrder")
    Call<BaseWanKeResp> pointOrder(@HeaderMap Map<String, String> headers,@Body WanKeOrderReqEntity req);


    /**
     * 功能描述: 退货退积分
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.wanke.entity.BaseWanKeResp>
     * <AUTHOR>
     * @date 2024/7/30 13:19
     */
    @POST("/yinli-openapi/api/v1/order/yueshang/refundOrder")
    Call<BaseWanKeResp> refundOrder(@HeaderMap Map<String, String> headers,@Body WanKeOrderRefundReqEntity req);
}
