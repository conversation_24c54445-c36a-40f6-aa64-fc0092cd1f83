package com.jnby.mallasset.strategy.platform.guangHuan;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.dto.req.order.PayItem;
import com.jnby.mallasset.dto.req.order.ProductItem;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.remote.guangHuan.IGuangHuanRemoteHttpApi;
import com.jnby.mallasset.remote.guangHuan.entity.*;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 凯德订单平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.GUANG_HUAN, category = PlatformCategoryTypeEnum.ORDER)
@Slf4j
@AllArgsConstructor
public class GuangHuanOrderStrategyService extends AbstractOrderService {

    private CashierMallOrderLogMapper cashierMallOrderLogMapper;
    private IGuangHuanRemoteHttpApi guangHuanRemoteHttpApi;

    @Override
    public ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef mallAssetStoreRef) {
        log.info("重庆光环订单同步输入参数：{}", JSON.toJSONString(orderConsumeReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderConsumeReq, mallAssetStoreRef);

            Map<String, String> header = new HashMap<>();
            header.put("Authorization",mallAssetStoreRef.getPlatformPublicKey());
            // 组装请求对象
            SaveTradeParam req = new SaveTradeParam();
            req.setAmount(orderConsumeReq.getOrdAmount().multiply(new BigDecimal(100)).intValue());
            req.setChangeSubType("pos");
            req.setTransactionNo(orderConsumeReq.getOrdNum());
            req.setConsumptionTime(DateUtil.timeStamp3Date(orderConsumeReq.getOrdFinishTime(),null));
            req.setMemberId(orderConsumeReq.getOuterMemberId());
            List<TradeProductDTO> products = new ArrayList<>();
            List<ProductItem> productItemList = orderConsumeReq.getProductItemList();
            for(int i=0; i < productItemList.size(); i++){
                ProductItem productItem = productItemList.get(i);
                TradeProductDTO tradeProductDTO = new TradeProductDTO();
                tradeProductDTO.setAmount(productItem.getItemPrice().multiply(new BigDecimal(100)).intValue()*productItem.getItemQty());
                tradeProductDTO.setName(productItem.getItemName());
                tradeProductDTO.setId(productItem.getItemNo());
                tradeProductDTO.setStoreCode(mallAssetStoreRef.getPlatformMallStoreId());
                products.add(tradeProductDTO);
            }
            req.setProducts(products);
            req.setProjectCode(mallAssetStoreRef.getPlatformMallId());
            req.setSerialNo("JNBY");
            req.setSerialNumber(orderConsumeReq.getOrdPayNo());
            req.setTradeType(0);
            List<TradePaymentParam> payments = new ArrayList<>();
            List<PayItem> payItemList = orderConsumeReq.getPayItemList();
            payItemList.forEach(payItem -> {
                if(payItem.getId() == 62 || payItem.getId() == 231){
                    TradePaymentParam paymentParam = new TradePaymentParam();
                    paymentParam.setAmount(payItem.getPayamount().multiply(new BigDecimal(100)).intValue());
                    paymentParam.setPayment("WX");
                    payments.add(paymentParam);
                }
            });
            req.setPayments(payments);
            req.setComments("JNBY");

            log.info("重庆光环订单同步调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseGuangHuanResp<SaveTradeResp>> execute = guangHuanRemoteHttpApi.saveTrade(header,req).execute();
            BaseGuangHuanResp<SaveTradeResp> body = execute.body();
            commonResponse.setMsg(body.getMessage());
            if(body.getStatus().equals("0000")){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                SaveTradeResp saveTradeResp = body.getData();
                OrderResponseEntity orderResponseEntity = new OrderResponseEntity();
                orderResponseEntity.setOrdNum(orderConsumeReq.getOrdNum());
                orderResponseEntity.setExchangeId(saveTradeResp.getTransactionNo());
                orderResponseEntity.setExchangeNo(saveTradeResp.getTransactionNo());
                commonResponse.setData(orderResponseEntity);
                // 更新返回的参数
                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLog.setUpdateExchangeId(saveTradeResp.getTransactionNo());
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            }else {
                commonResponse.setCode(Integer.parseInt(body.getStatus()));
                commonResponse.setData(body.getData());
            }
            commonResponse.setMsg(body.getMessage());
            log.info("重庆光环订单同步返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 重庆光环订单同步失败",orderConsumeReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeNo(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setBjStoreId(orderConsumeReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(1);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult pushRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef assetStoreRef) {
        log.info("重庆光环订单退货退款输入参数：{}", JSON.toJSONString(orderRefundReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderRefundReq, assetStoreRef);

            Map<String, String> header = new HashMap<>();
            header.put("Authorization",assetStoreRef.getPlatformPublicKey());
            // 组装请求对象
            SaveTradeParam req = new SaveTradeParam();
            req.setAmount(orderRefundReq.getRefundAmount().multiply(new BigDecimal(100)).intValue());
            req.setChangeSubType("pos");
            req.setConsumptionTime(DateUtil.timeStamp3Date(orderRefundReq.getOrdFinishTime(),null));
            req.setMemberId(orderRefundReq.getOuterMemberId());
            List<TradeProductDTO> products = new ArrayList<>();
            List<ProductItem> productItemList = orderRefundReq.getProductItemList();
            for(int i=0; i < productItemList.size(); i++){
                ProductItem productItem = productItemList.get(i);
                TradeProductDTO tradeProductDTO = new TradeProductDTO();
                tradeProductDTO.setAmount(productItem.getItemPrice().abs().multiply(new BigDecimal(100)).intValue());
                tradeProductDTO.setName(productItem.getItemName());
                tradeProductDTO.setId(String.valueOf(i+1));
                tradeProductDTO.setSrcLine(productItem.getItemNo());
                tradeProductDTO.setStoreCode(assetStoreRef.getPlatformMallStoreId());
                products.add(tradeProductDTO);
            }
            req.setProducts(products);
            req.setProjectCode(assetStoreRef.getPlatformMallId());
            req.setSerialNo("JNBY");
            req.setSerialNumber(orderRefundReq.getOrdPayNo());
            req.setTradeType(1);
            List<TradePaymentParam> payments = new ArrayList<>();
            List<PayItem> payItemList = orderRefundReq.getPayItemList();
            payItemList.forEach(payItem -> {
                if(payItem.getId() == 62 || payItem.getId() == 231){
                    TradePaymentParam paymentParam = new TradePaymentParam();
                    paymentParam.setAmount(payItem.getPayamount().abs().multiply(new BigDecimal(100)).intValue());
                    paymentParam.setPayment("WX");
                    payments.add(paymentParam);
                }
            });
            req.setPayments(payments);
            req.setOriginTransNo(orderRefundReq.getOrdNum());
            req.setTransactionNo(orderRefundReq.getRefundNo());
            req.setComments("JNBY");
            log.info("重庆光环订单同步调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseGuangHuanResp<SaveTradeResp>> execute = guangHuanRemoteHttpApi.saveTrade(header,req).execute();
            BaseGuangHuanResp<SaveTradeResp> body = execute.body();
            commonResponse.setMsg(body.getMessage());
            if(body.getStatus().equals("0000")){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                SaveTradeResp saveTradeResp = body.getData();
                OrderResponseEntity orderResponseEntity = new OrderResponseEntity();
                orderResponseEntity.setOrdNum(orderRefundReq.getOrdNum());
                orderResponseEntity.setExchangeId(saveTradeResp.getTransactionNo());
                orderResponseEntity.setExchangeNo(saveTradeResp.getTransactionNo());
                commonResponse.setData(orderResponseEntity);
                // 更新返回的参数
                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLog.setUpdateExchangeId(saveTradeResp.getTransactionNo());
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            }else {
                commonResponse.setCode(Integer.parseInt(body.getStatus()));
                commonResponse.setData(body.getData());
            }
            commonResponse.setMsg(body.getMessage());
            log.info("重庆光环订单退货退款返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 重庆光环订单退货退款失败",orderRefundReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderRefundReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setExchangeNo(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setBjStoreId(orderRefundReq.getStoreId());
        cashierMallOrderLog.setMallId(assetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(assetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(assetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(2);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult prepareRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

}
