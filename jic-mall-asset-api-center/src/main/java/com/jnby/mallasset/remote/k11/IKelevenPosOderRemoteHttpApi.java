package com.jnby.mallasset.remote.k11;


import com.jnby.mallasset.remote.k11.entity.pos.KElevenResponseSoapEnvelope;
import com.jnby.mallasset.remote.k11.entity.pos.KElevenSoapEnvelope;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/**
 * 文件名: com.jnby.mallasset.remote.k11-KElevenRemoteHttpApi.java
 * 文件简介: K11 平台
 *
 * <AUTHOR>
 * @date 2025/8/6 17:52
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface IKelevenPosOderRemoteHttpApi {

    @Headers({
            "Content-Type:text/xml; charset=utf-8",
            "SOAPAction:http://tempuri.org/PostSales"
    })
    @POST("/pos.asmx")
    Call<KElevenResponseSoapEnvelope> PostSales(@Body KElevenSoapEnvelope req);
}
