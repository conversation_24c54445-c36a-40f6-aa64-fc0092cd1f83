package com.jnby.mallasset.remote.mallcoo.entity.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class GuangHuanUploadPosReq {

    @ApiModelProperty("采集点编号")
    private String collectionTerminalNo;

    @ApiModelProperty("小票流水号")
    private String ticketNumber;

    @ApiModelProperty("是否压单标识")
    private boolean offsetMark;

    @ApiModelProperty("交易时间:格式：yyyy-MM-dd HH:mm:ss")
    private String tradeTime;

    @ApiModelProperty("交易类型:SALE(“交易”), RTN(“退货”)")
    private String tradeType;

    @ApiModelProperty("实收金额(最终金额)")
    private BigDecimal tradeAmount;

    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmount;

    @ApiModelProperty("商品数量")
    private int qty;

    @ApiModelProperty("是否会员")
    private boolean member;

    @ApiModelProperty("商品明细")
    private List<GuangHuanArticleItems> articleItems;
}
