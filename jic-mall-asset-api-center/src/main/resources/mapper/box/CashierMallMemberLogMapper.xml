<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper">
    <insert id="insertEntity" parameterType="com.jnby.mallasset.module.model.CashierMallMemberLog">
        <selectKey keyProperty="count" resultType="java.lang.Integer" order="BEFORE">
            select count(*) count from jnby.cashier_mall_member_log where MOBILE = #{mobile} and BJ_STORE_ID = #{bjStoreId}
        </selectKey>
        <if test="count == 0">
            insert into jnby.cashier_mall_member_log(ID,MOBILE,MALL_ID,MALL_STORE_ID,MALL_PLATFORM,BJ_STORE_ID,IS_EXECUTE)
            values(seq_cashier_mall_member_log.nextval,#{mobile},#{mallId},#{mallStoreId},#{mallPlatform},#{bjStoreId},#{isExecute})
        </if>
        <if test="count > 0">
            update jnby.cashier_mall_member_log set MALL_ID = #{mallId},MALL_STORE_ID = #{mallStoreId},MALL_PLATFORM = #{mallPlatform}
            where MOBILE = #{mobile} and BJ_STORE_ID = #{bjStoreId}
        </if>
    </insert>

    <update id="updateInfoByParam" parameterType="com.jnby.mallasset.module.model.CashierMallMemberLog">
        update jnby.cashier_mall_member_log
        <set>
            <if test="openUserId != null and openUserId != ''">
                OPEN_USER_ID = #{openUserId},
            </if>
            <if test="isExecute != null ">
                IS_EXECUTE = #{isExecute},
            </if>
        </set>
        where MOBILE = #{mobile} and BJ_STORE_ID = #{bjStoreId}
    </update>

    <select id="getMaxId" resultType="java.lang.Long" useCache="false" flushCache="true">
        select SEQ_CASHIER_MALL_MEMBER_LOG.nextval as seq from dual
    </select>

    <select id="selectCashierMallMemberLog" resultType="com.jnby.mallasset.module.model.CashierMallMemberLog">
        select
            OPEN_USER_ID openUserId,
            MOBILE MOBILE,
            BJ_STORE_ID bjStoreId,
            HAS_CHOSE_PRIVACY hasChosePrivacy
        from jnby.cashier_mall_member_log
        where MOBILE = #{mobile} and BJ_STORE_ID = #{storeCode}
        and rownum = 1
    </select>
</mapper>