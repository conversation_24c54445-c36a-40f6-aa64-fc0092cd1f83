package com.jnby.mallasset.remote.drc;


import com.jnby.mallasset.remote.drc.entity.*;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.HeaderMap;
import retrofit2.http.POST;

import java.util.Map;


/**
 * 大融城
 */
public interface IDrcRemoteHttpApi {

    /**
     * 功能描述: 获取用户token
     * 使用场景:
     *
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.huarun.entity.BaseHuaRunResp<com.jnby.mallasset.remote.huarun.entity.UserLoginRespEntity>>
     * <AUTHOR>
     * @date 2024/7/23 13:58
     */
    @POST("/SyncApi/Auth/getAccessToken")
    Call<BaseDrcResp<AccessTokenRespEntity>> getAccessToken(@Body AccessTokenReqEntity req);

    /**
     * 功能描述: 会员注册
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.drc.entity.BaseDrcResp<com.jnby.mallasset.remote.drc.entity.DrcMemberRegisterRespEntity>>
     * <AUTHOR>
     * @date 2024/7/24 13:31
     */
    @POST("/SyncApi/v1/createMember")
    Call<BaseDrcResp<BaseDrcDataResp<DrcMemberRegisterRespEntity>>> createMember(@HeaderMap Map<String, String> headers, @Body DrcMemberRegisterReqEntity req);

    /**
     * 功能描述: 查询会员
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.drc.entity.BaseDrcResp<com.jnby.mallasset.remote.drc.entity.DrcMemberRegisterRespEntity>>
     * <AUTHOR>
     * @date 2024/7/24 13:31
     */
    @POST("/SyncApi/v1/queryMemberInfo")
    Call<BaseDrcResp<BaseDrcDataResp<DrcMemberRegisterRespEntity>>> queryMemberInfo(@HeaderMap Map<String, String> headers, @Body DrcMemberRegisterReqEntity req);

    /**
     * 功能描述: 订单同步、售后
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.drc.entity.BaseDrcResp<com.jnby.mallasset.remote.drc.entity.DrcMemberRegisterRespEntity>>
     * <AUTHOR>
     * @date 2024/7/24 13:31
     */
    @POST("/SyncApi/v1/salesIntegral")
    Call<BaseDrcResp<BaseDrcDataResp<DrcOrderRespEntity>>> orderSales(@HeaderMap Map<String, String> headers, @Body DrcOrderReqEntity req);

}
