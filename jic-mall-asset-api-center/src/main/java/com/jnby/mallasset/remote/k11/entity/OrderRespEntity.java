package com.jnby.mallasset.remote.k11.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.k11.entity-OrderRespEntity.java
 * 文件简介:
 *
 * <AUTHOR>
 * @date 2025/8/7 20:12
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class OrderRespEntity implements Serializable {

    @ApiModelProperty("订单号")
    private String ordNum;
    @ApiModelProperty("业务ID，唯一值")
    private String exchangeId;
    @ApiModelProperty("外部单号")
    private String exchangeNo;
}
