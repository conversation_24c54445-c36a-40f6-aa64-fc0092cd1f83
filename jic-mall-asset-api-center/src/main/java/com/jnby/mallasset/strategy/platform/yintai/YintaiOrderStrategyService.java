package com.jnby.mallasset.strategy.platform.yintai;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gitee.sop.sdk.client.OpenClient;
import com.gitee.sop.sdk.request.CommonRequest;
import com.gitee.sop.sdk.response.CommonResponse;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.remote.dingding.IDingDingRemoteHttpApi;
import com.jnby.mallasset.remote.dingding.entity.DingDingMsg;
import com.jnby.mallasset.remote.dingding.entity.DingDingResponse;
import com.jnby.mallasset.remote.yintai.entity.TransPaying;
import com.jnby.mallasset.remote.yintai.entity.TransProduct;
import com.jnby.mallasset.remote.yintai.entity.YinTaiBaseRespEntity;
import com.jnby.mallasset.remote.yintai.entity.YinTaiOrderResponse;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.DateUtil;
import com.jnby.mallasset.util.OrderNoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * 银泰订单策略
 */
@Service
@Slf4j
@PlatformTypeAnnotation(platform = PlatformTypeEnum.YIN_TAI, category = PlatformCategoryTypeEnum.ORDER)
public class YintaiOrderStrategyService extends AbstractOrderService {

    @Autowired
    private CashierMallOrderLogMapper cashierMallOrderLogMapper;
    @Autowired
    private IDingDingRemoteHttpApi dingDingRemoteHttpApi;

    @Value("${yinTai.url}")
    private String yinTaiUrl;

    @Override
    public ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef assetStoreRef) {
        log.info("银泰订单同步输入参数：{}", JSON.toJSONString(orderConsumeReq));
        ResponseResult<YinTaiOrderResponse> commonResponse = new ResponseResult<>();
        OpenClient client = new OpenClient(yinTaiUrl, assetStoreRef.getPlatformAppId(), assetStoreRef.getPlatformPrivateKey(),assetStoreRef.getPlatformPublicKey());
        try {
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderConsumeReq, assetStoreRef);
            String tradeTime = DateUtil.timeStamp3Date(orderConsumeReq.getOrdFinishTime(),null);
            CommonRequest request = new CommonRequest("order.third.syn");
            // 请求参数
            Map<String, Object> bizModel = initRequestOrderParam(orderConsumeReq, assetStoreRef, tradeTime);
            request.setBizModel(bizModel);
            log.info("银泰订单同步调用远程接口参数：{}", JSON.toJSONString(bizModel));
            CommonResponse response = client.execute(request);
            YinTaiBaseRespEntity resp = JSONObject.parseObject(response.getBody(), YinTaiBaseRespEntity.class);
            if(resp.isSuccess() && resp.getCode().equals("10000")){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setMsg(resp.getMsg());
                YinTaiOrderResponse yinTaiOrderResponse = new YinTaiOrderResponse();
                yinTaiOrderResponse.setOrdNum(orderConsumeReq.getOrdNum());
                yinTaiOrderResponse.setExchangeId(orderConsumeReq.getOrdNum());
                yinTaiOrderResponse.setExchangeNo(orderConsumeReq.getOrdNum());
                commonResponse.setData(yinTaiOrderResponse);
                // 更新返回的参数
                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLog.setRemark(ResultCodeEnum.SUCCESS.getMsg());
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            }else {
                commonResponse.setCode(Integer.parseInt(resp.getCode()));
                commonResponse.setMsg(resp.getMsg());
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 银泰订单同步异常",orderConsumeReq.getOrdNum(), e);
        }
        log.info("银泰订单同步返回参数：{}", JSON.toJSONString(commonResponse));
        return commonResponse;
    }

    private Map<String, Object> initRequestOrderParam(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef assetStoreRef, String tradeTime) {
        Map<String, Object> bizModel = new TreeMap<>();
        String platformMallId = assetStoreRef.getPlatformMallId();
        bizModel.put("billType", 1L);
        bizModel.put("channel", platformMallId);
        bizModel.put("thirdCounterId", assetStoreRef.getPlatformMallStoreId());
        bizModel.put("requestId", platformMallId + "-" + IdUtil.simpleUUID());
        bizModel.put("orderNo", orderConsumeReq.getOrdNum());
        bizModel.put("billTime", tradeTime);
        bizModel.put("productNum", 1);
        bizModel.put("allAmt", orderConsumeReq.getOrdAmount());
        bizModel.put("billAmt", orderConsumeReq.getOrdAmount());
        bizModel.put("netName", "wechat");
        bizModel.put("netAmt", orderConsumeReq.getOrdAmount());
        bizModel.put("vipPhone", orderConsumeReq.getMemberTel());

        List<TransPaying> payingListList = new ArrayList<>();
        TransPaying transPaying = new TransPaying();
        transPaying.setSort(1);
        transPaying.setPayTime(tradeTime);
        transPaying.setPayAmt(orderConsumeReq.getOrdAmount());
        transPaying.setIsScore(0);
        transPaying.setPayName("wechat");
        transPaying.setPayTypeId("W001");
        transPaying.setTraceNo(orderConsumeReq.getOrdPayNo());
        payingListList.add(transPaying);
        bizModel.put("transPayingList", payingListList);

        List<TransProduct> transProductList = new ArrayList<>();
        TransProduct transProduct = new TransProduct();
        String[] productInfo = assetStoreRef.getIntegralSkuCode().split(",");
        transProduct.setSort(1);
        transProduct.setProductNum(1);
        transProduct.setProductId(productInfo[0]);
        transProduct.setProductName(productInfo[1]);
        transProduct.setUnitPrice(orderConsumeReq.getOrdAmount());
        transProduct.setRealAmt(orderConsumeReq.getOrdAmount());
        transProductList.add(transProduct);
        bizModel.put("transProductList", transProductList);
        return bizModel;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeNo(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setBjStoreId(orderConsumeReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(1);
        cashierMallOrderLog.setRefundAmount(orderConsumeReq.getOrdAmount());
        cashierMallOrderLogMapper.insertAddLog(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult pushRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef assetStoreRef) {
        log.info("银泰订单退货退款输入参数：{}", JSON.toJSONString(orderRefundReq));
        ResponseResult<YinTaiOrderResponse> commonResponse = new ResponseResult<>();
        OpenClient client = new OpenClient(yinTaiUrl, assetStoreRef.getPlatformAppId(), assetStoreRef.getPlatformPrivateKey(),assetStoreRef.getPlatformPublicKey());
        try {
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordRefundOrderLog(orderRefundReq, assetStoreRef);

            CommonRequest request = new CommonRequest("order.third.syn");
            BigDecimal refundAmount = orderRefundReq.getRefundAmount();
            BigDecimal ordAmount = orderRefundReq.getOrdAmount();
            CashierMallOrderLog orderLog = cashierMallOrderLogMapper.selectOrderLogByOrderNumAndExchangeId(orderRefundReq.getOrdNum(),orderRefundReq.getExchangeId());
            if(ordAmount.compareTo(refundAmount) == 0){
                // 整单退
                Map<String, Object> bizModel = initRequestRefundParam(orderRefundReq, assetStoreRef,orderLog);
                request.setBizModel(bizModel);
                log.info("银泰订单整单退货调用远程接口参数：{}", JSON.toJSONString(bizModel));
                CommonResponse response = client.execute(request);
                YinTaiBaseRespEntity resp = JSONObject.parseObject(response.getBody(), YinTaiBaseRespEntity.class);
                if(resp.isSuccess() && resp.getCode().equals("10000")){
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    commonResponse.setMsg(resp.getMsg());
                    YinTaiOrderResponse yinTaiOrderResponse = new YinTaiOrderResponse();
                    yinTaiOrderResponse.setOrdNum(orderRefundReq.getOrdNum());
                    yinTaiOrderResponse.setExchangeId(orderRefundReq.getRefundNo());
                    yinTaiOrderResponse.setExchangeNo(orderRefundReq.getRefundNo());
                    commonResponse.setData(yinTaiOrderResponse);
                    // 更新返回的参数
                    cashierMallOrderLog.setIsExecute("Y");
                    cashierMallOrderLog.setRemark(ResultCodeEnum.SUCCESS.getMsg());
                    cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
                }else {
                    commonResponse.setCode(Integer.parseInt(response.getCode()));
                    commonResponse.setMsg(response.getMsg());
                }
                log.info("银泰订单整单退货返回参数：{}", JSON.toJSONString(bizModel));
                return commonResponse;
            }else {
                // 部分退逻辑,先退上笔单的总额，再创建剩余金额的新单
                Map<String, Object> bizModel = initRequestRefundParam(orderRefundReq, assetStoreRef,orderLog);
                request.setBizModel(bizModel);
                log.info("银泰订单部分退货调用远程接口参数：{}", JSON.toJSONString(bizModel));
                CommonResponse response = client.execute(request);
                YinTaiBaseRespEntity resp = JSONObject.parseObject(response.getBody(), YinTaiBaseRespEntity.class);
                log.info("银泰订单部分退货返回参数：{}", JSON.toJSONString(resp));
                if(resp.isSuccess() && resp.getCode().equals("10000")){
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    commonResponse.setMsg(resp.getMsg());
                    YinTaiOrderResponse yinTaiOrderResponse = new YinTaiOrderResponse();
                    yinTaiOrderResponse.setOrdNum(orderRefundReq.getOrdNum());
                    yinTaiOrderResponse.setExchangeId(orderRefundReq.getRefundNo());
                    yinTaiOrderResponse.setExchangeNo(orderRefundReq.getRefundNo());
                    commonResponse.setData(yinTaiOrderResponse);
                    // 更新返回的参数
                    cashierMallOrderLog.setIsExecute("Y");
                    cashierMallOrderLog.setRemark(ResultCodeEnum.SUCCESS.getMsg());
                    cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
                }else {
                    commonResponse.setCode(Integer.parseInt(resp.getCode()));
                    commonResponse.setMsg(resp.getMsg());
                }

                if(commonResponse.getCode() == ResultCodeEnum.SUCCESS.getCode()) {
                    // 创建一笔新单,查询剩余金额
                    CashierMallOrderLog mallOrderLog = cashierMallOrderLogMapper.selectSumRefundAmount(orderRefundReq.getOrdNum());
                    BigDecimal refundAmountSum = mallOrderLog.getRefundAmount();
                    int isNeedCreate = ordAmount.subtract(refundAmountSum).compareTo(BigDecimal.ZERO);
                    if(isNeedCreate != 0){
                        Long seq = cashierMallOrderLogMapper.selectYinTaiOrderNoSeq();
                        String docNo = "RE" + OrderNoUtils.generalOrderNo(seq);
                        String tradeTime = DateUtil.timeStamp3Date(orderRefundReq.getRefundTime(),null);
                        // 不为0需要新建订单
                        CashierMallOrderLog logInfo = recordNewLog(orderRefundReq, assetStoreRef, docNo);

                        BigDecimal amount = ordAmount.subtract(refundAmountSum);
                        Map<String, Object> newBizModel = initNewCreateOrder(orderRefundReq, assetStoreRef, docNo, tradeTime, amount);
                        request.setBizModel(newBizModel);
                        log.info("银泰订单同步创建新订单调用远程接口参数：{}", JSON.toJSONString(newBizModel));
                        CommonResponse commonResponseNew = client.execute(request);
                        YinTaiBaseRespEntity baseRespEntity = JSONObject.parseObject(commonResponseNew.getBody(), YinTaiBaseRespEntity.class);
                        log.info("银泰订单同步创建新订单返回参数：{}", JSON.toJSONString(baseRespEntity));
                        if(baseRespEntity.isSuccess() && baseRespEntity.getCode().equals("10000")){
                            commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                            commonResponse.setMsg(baseRespEntity.getMsg());
                            // 更新原单的exchangeNo
                            CashierMallOrderLog mallOrderLog1 = new CashierMallOrderLog();
                            mallOrderLog1.setOrdNum(orderRefundReq.getOrdNum());
                            mallOrderLog1.setExchangeNo(docNo);
                            mallOrderLog1.setExchangeId(orderRefundReq.getExchangeId());
                            mallOrderLog1.setRemark(ResultCodeEnum.SUCCESS.getMsg());
                            mallOrderLog1.setRefundAmount(amount);
                            cashierMallOrderLogMapper.updateExchangeNoWithParam(mallOrderLog1);
                            logInfo.setRemark(ResultCodeEnum.SUCCESS.getMsg());
                            cashierMallOrderLogMapper.updateOrderLogByParam(logInfo);
                        }else {
                            commonResponse.setCode(Integer.parseInt(baseRespEntity.getCode()));
                            commonResponse.setMsg("银泰原单已完成部分退款，但创建新单失败：" + baseRespEntity.getMsg());
                            // 推送钉钉告警
                            pushDingMsg(newBizModel, baseRespEntity);
                        }
                    }
                }
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 银泰订单退货异常",orderRefundReq.getRefundNo(), e);
        }
        log.info("银泰订单退货退款返回参数：{}", JSON.toJSONString(commonResponse));
        return commonResponse;
    }

    private static Map<String, Object> initNewCreateOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef, String docNo, String tradeTime, BigDecimal amount) {
        Map<String, Object> newBizModel = new TreeMap<>();
        String platformMallId = assetStoreRef.getPlatformMallId();
        newBizModel.put("billType", 1L);
        newBizModel.put("channel", platformMallId);
        newBizModel.put("thirdCounterId", assetStoreRef.getPlatformMallStoreId());
        newBizModel.put("requestId", platformMallId + "-" + IdUtil.simpleUUID());
        newBizModel.put("orderNo", docNo);
        newBizModel.put("billTime", tradeTime);
        newBizModel.put("productNum", 1);
        newBizModel.put("allAmt", amount);
        newBizModel.put("billAmt", amount);
        newBizModel.put("netName", "wechat");
        newBizModel.put("netAmt", amount);
        newBizModel.put("vipPhone", orderRefundReq.getMemberTel());

        List<TransPaying> payingListList = new ArrayList<>();
        TransPaying transPaying = new TransPaying();
        transPaying.setSort(1);
        transPaying.setPayTime(tradeTime);
        transPaying.setPayAmt(amount);
        transPaying.setIsScore(0);
        transPaying.setPayName("wechat");
        transPaying.setPayTypeId("W001");
        transPaying.setTraceNo(orderRefundReq.getOrdPayNo());
        payingListList.add(transPaying);
        newBizModel.put("transPayingList", payingListList);

        List<TransProduct> transProductList = new ArrayList<>();
        TransProduct transProduct = new TransProduct();
        String[] productInfo = assetStoreRef.getIntegralSkuCode().split(",");
        transProduct.setSort(1);
        transProduct.setProductNum(1);
        transProduct.setProductId(productInfo[0]);
        transProduct.setProductName(productInfo[1]);
        transProduct.setUnitPrice(amount);
        transProduct.setRealAmt(amount);
        transProductList.add(transProduct);
        newBizModel.put("transProductList", transProductList);
        return newBizModel;
    }

    private CashierMallOrderLog recordNewLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef, String docNo) {
        CashierMallOrderLog logInfo = new CashierMallOrderLog();
        logInfo.setOrdNum(docNo);
        logInfo.setExchangeId(orderRefundReq.getOrdNum());
        logInfo.setExchangeNo(orderRefundReq.getRefundNo());
        logInfo.setBjStoreId(orderRefundReq.getStoreId());
        logInfo.setMallId(assetStoreRef.getPlatformMallId());
        logInfo.setIsCallback("Y");
        logInfo.setIsExecute("Y");
        logInfo.setMallPlatform(assetStoreRef.getApiPlatform());
        logInfo.setMallStoreId(assetStoreRef.getPlatformMallStoreId());
        logInfo.setType(1);
        logInfo.setRefundAmount(BigDecimal.ZERO);
        cashierMallOrderLogMapper.insertEntity(logInfo);
        return logInfo;
    }

    private void pushDingMsg(Map<String, Object> newBizModel, YinTaiBaseRespEntity yinTaiBaseRespEntity) throws IOException {
        String msg = "数据推送异常：\n" + "功能：银泰订单原单退款成功，创建新单失败" + "\n请求参数: \n" + JSON.toJSON(newBizModel) + "\n请求结果: \n" + JSON.toJSON(yinTaiBaseRespEntity);
        DingDingMsg dingDingMsg = new DingDingMsg();
        Map<String,Object> param = new HashMap<>();
        param.put("content",msg);
        dingDingMsg.setText(param);
        dingDingMsg.setMsgtype("text");
        Response<DingDingResponse> dingDingResponse = dingDingRemoteHttpApi.pushMsg(dingDingMsg).execute();
        log.info("银泰订单推送消息返回结果：{}",dingDingResponse.body());
    }

    private CashierMallOrderLog recordRefundOrderLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderRefundReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setExchangeNo(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setBjStoreId(orderRefundReq.getStoreId());
        cashierMallOrderLog.setMallId(assetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(assetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(assetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(2);
        cashierMallOrderLog.setRefundAmount(orderRefundReq.getRefundAmount());
        cashierMallOrderLogMapper.insertAddLog(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    private Map<String, Object> initRequestRefundParam(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef,CashierMallOrderLog orderLog) {
        String tradeTime = DateUtil.timeStamp3Date(orderRefundReq.getRefundTime(),null);
        Map<String, Object> bizModel = new TreeMap<>();
        String platformMallId = assetStoreRef.getPlatformMallId();
        bizModel.put("billType", 2L);
        bizModel.put("channel", platformMallId);
        bizModel.put("thirdCounterId", assetStoreRef.getPlatformMallStoreId());
        bizModel.put("requestId", platformMallId + "-" + IdUtil.simpleUUID());
        bizModel.put("orderNo", orderRefundReq.getRefundNo());
        bizModel.put("originalOrderNo", orderLog.getExchangeNo());
        bizModel.put("billTime", tradeTime);
        bizModel.put("productNum", 1);
        bizModel.put("allAmt", orderLog.getRefundAmount());
        bizModel.put("billAmt", orderLog.getRefundAmount());
        bizModel.put("netAmt", orderLog.getRefundAmount());
        bizModel.put("netName", "wechat");
        bizModel.put("vipPhone", orderRefundReq.getMemberTel());

        List<TransPaying> payingListList = new ArrayList<>();
        TransPaying transPaying = new TransPaying();
        transPaying.setSort(1);
        transPaying.setPayTime(tradeTime);
        transPaying.setPayAmt(orderLog.getRefundAmount());
        transPaying.setIsScore(0);
        transPaying.setTraceNo(orderRefundReq.getOrdPayNo());
        transPaying.setPayName("wechat");
        transPaying.setPayTypeId("W001");
        payingListList.add(transPaying);
        bizModel.put("transPayingList", payingListList);

        List<TransProduct> transProductList = new ArrayList<>();
        TransProduct transProduct = new TransProduct();
        String[] productInfo = assetStoreRef.getIntegralSkuCode().split(",");
        transProduct.setSort(1);
        transProduct.setProductNum(1);
        transProduct.setProductId(productInfo[0]);
        transProduct.setProductName(productInfo[1]);
        transProduct.setUnitPrice(orderLog.getRefundAmount());
        transProduct.setRealAmt(orderLog.getRefundAmount());
        transProductList.add(transProduct);
        bizModel.put("transProductList", transProductList);
        return bizModel;
    }

    @Override
    public ResponseResult prepareRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }
}
