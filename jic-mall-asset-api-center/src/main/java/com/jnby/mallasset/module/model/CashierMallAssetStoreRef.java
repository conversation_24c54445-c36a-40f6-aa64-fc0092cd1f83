package com.jnby.mallasset.module.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-07-15 13:35:45
 * @Description: 商场资产规则配置
 */
@Data
@ApiModel(value="CashierMallAssetStoreRef", description="商场门店关联对象")
public class CashierMallAssetStoreRef implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long id;
    @ApiModelProperty(value = "0未删除、1已删除")
    private Boolean isDelete;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "商场资产配置ID")
    private Long mallAssetConfigId;
    @ApiModelProperty(value = "商场名称")
    private String mallName;
    @ApiModelProperty(value = "商场门店ID")
    private String platformMallStoreId;
    @ApiModelProperty(value = "商场门店名称")
    private String mallStoreName;
    @ApiModelProperty(value = "支付平台：1微信直连 2收钱吧 3支付宝直连 4电银")
    private String payChannel;
    @ApiModelProperty(value = "支付密钥")
    private String paySign;
    @ApiModelProperty(value = "伯俊门店ID")
    private String bjStoreId;
    @ApiModelProperty(value = "积分换算比例。1积分换算多少元。例如0.01=1积分兑换0.01元")
    private BigDecimal pointsDeductionScale;
    @ApiModelProperty(value = "单订单积分抵扣起扣门槛")
    private Long pointsDeductionThreshold;
    @ApiModelProperty(value = "单订单积分抵扣上限")
    private Long pointsDeductionUpperLimit;
    @ApiModelProperty(value = "单订单积分抵扣阶梯单位。100代表积分每满100才可以抵扣计算")
    private Long pointsDeductionLadder;
    @ApiModelProperty(value = "微信直连的商户ID")
    private String mchId;
    @ApiModelProperty(value = "平台商场ID")
    private String platformMallId;
    @ApiModelProperty(value = "平台APP_ID")
    private String platformAppId;
    @ApiModelProperty(value = "平台公钥")
    private String platformPublicKey;
    @ApiModelProperty(value = "平台私钥")
    private String platformPrivateKey;
    @ApiModelProperty(value = "收钱吧APP_ID")
    private String sqbAppId;
    @ApiModelProperty(value = "收钱吧终端ID")
    private String sqbTerminalSn;
    @ApiModelProperty(value = "对接API平台：1=猫酷，2=银泰,3-华润")
    private Integer apiPlatform;
    @ApiModelProperty(value = "积分商品条码")
    private String integralSkuCode;
    @ApiModelProperty(value = "是否整单退：Y-是；N-否")
    private String allRefund;
    @ApiModelProperty(value = "是否使用停车券。0=否，1=是")
    private Boolean hasUseParkingCoupon;
}
