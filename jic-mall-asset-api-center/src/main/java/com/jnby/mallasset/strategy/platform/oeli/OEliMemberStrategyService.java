package com.jnby.mallasset.strategy.platform.oeli;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.MallException;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.remote.oeli.IOeliRemoteHttpApi;
import com.jnby.mallasset.remote.oeli.entity.BaseIOeliResp;
import com.jnby.mallasset.remote.oeli.entity.MemberInfoRespEntity;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;

/**
 * 天目里会员策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.OELI, category = PlatformCategoryTypeEnum.MEMBER)
@AllArgsConstructor
@Slf4j
public class OEliMemberStrategyService extends AbstractMemberService {

    @Autowired
    private IOeliRemoteHttpApi iOeliRemoteHttpApi;

    @Override
    public ResponseResult openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("天目里会员查询输入参数：{}", JSON.toJSONString(memberRegisterReq));
        try {
            Response<BaseIOeliResp<MemberInfoRespEntity>> execute = iOeliRemoteHttpApi.getMember(memberRegisterReq.getMobile()).execute();
            boolean successful = execute.isSuccessful();
            if (!successful) {
                throw new MallException(SystemErrorEnum.MEMBER_INTERFACE_API_ERROR);
            }
            BaseIOeliResp<MemberInfoRespEntity> body = execute.body();
            log.info("天目里会员查询调用远程接口响应：{}", JSON.toJSONString(body));
            if (!body.isSuccess()) {
                throw new MallException(body.getMsg());
            }
            MemberInfoRespEntity data = body.getData();
            if (data == null || Boolean.FALSE.equals(data.getRegister())) {
                throw new MallException(SystemErrorEnum.MEMBER_NOT_EXIST_ERROR);
            }
            return ResponseResult.success(data);
        } catch (IOException e) {
            log.error("天目里会员查询超时", e);
            throw new MallException(SystemErrorEnum.MEMBER_INTERFACE_TIMEOUT_ERROR);
        } catch (MallException e) {
            log.error("天目里会员查询已知异常", e);
            throw e;
        } catch (Exception e) {
            log.error("天目里会员查询异常", e);
            throw new MallException(SystemErrorEnum.MEMBER_UNKNOWN_ERROR);
        }
    }
}
