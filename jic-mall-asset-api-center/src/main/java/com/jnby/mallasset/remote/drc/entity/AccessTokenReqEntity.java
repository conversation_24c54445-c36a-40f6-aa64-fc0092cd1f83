package com.jnby.mallasset.remote.drc.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class AccessTokenReqEntity implements Serializable {
    @ApiModelProperty(value = "appid", required = true)
    private String appid;
    @ApiModelProperty(value = "秘钥", required = true)
    private String appsecret;
}
