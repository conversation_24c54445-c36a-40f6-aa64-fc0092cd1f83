package com.jnby.mallasset.strategy.platform.drc;

import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import com.jnby.mallasset.constant.MallRedisKeyConstant;
import com.jnby.mallasset.module.mapper.box.CashierMallStoreConfigMapper;
import com.jnby.mallasset.module.model.CashierMallStoreConfig;
import com.jnby.mallasset.remote.drc.IDrcRemoteHttpApi;
import com.jnby.mallasset.remote.drc.entity.AccessTokenReqEntity;
import com.jnby.mallasset.remote.drc.entity.AccessTokenRespEntity;
import com.jnby.mallasset.remote.drc.entity.BaseDrcResp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;

/**
 * 天目里用户token
 */
@Service
@AllArgsConstructor
@Slf4j
public class DrcAccessTokenService {

    private IDrcRemoteHttpApi drcRemoteHttpApi;
    private RedisPoolUtil redisPoolUtil;
    private CashierMallStoreConfigMapper cashierMallStoreConfigMapper;

    public String getAccessToken(String storeId) throws IOException {
        String tokenKey = MallRedisKeyConstant.User.TOKEN_KEY_DRC + storeId;
        String token = RedisTemplateUtil.get(redisPoolUtil,tokenKey);
        if(token != null){
            return token;
        }
        // 根据storeId查询用户名、密码
        CashierMallStoreConfig config = cashierMallStoreConfigMapper.selectByStoreId(storeId);
        AccessTokenReqEntity req = new AccessTokenReqEntity();
        req.setAppid(config.getPlatformAppId());
        req.setAppsecret(config.getPlatformPrivateKey());
        Response<BaseDrcResp<AccessTokenRespEntity>> execute = drcRemoteHttpApi.getAccessToken(req).execute();
        BaseDrcResp<AccessTokenRespEntity> body = execute.body();
        if(body.isSuccess()){
            token = body.getResult().getToken();
            RedisTemplateUtil.setex(redisPoolUtil, tokenKey, token, MallRedisKeyConstant.Expire.ONE_HALF_HOUR);
        }
        return token;
    }
}
