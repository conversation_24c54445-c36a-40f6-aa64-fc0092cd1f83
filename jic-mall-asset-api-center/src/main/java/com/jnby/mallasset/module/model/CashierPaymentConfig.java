package com.jnby.mallasset.module.model;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: CodeGenerator
 * @Date: 2024-12-10 16:32:19
 * @Description: 收银支付配置
 */
@Data
@TableName("CASHIER_PAYMENT_CONFIG")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="CashierPaymentConfig对象", description="收银支付配置")
@Deprecated
public class CashierPaymentConfig implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID")
    private Long id;
    @ApiModelProperty(value = "0未删除、1已删除")
    @TableField("IS_DELETE")
    private Integer isDelete;
    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "伯俊门店CODE")
    @TableField("BJ_STORE_ID")
    private String bjStoreId;
    @ApiModelProperty(value = "门店名称")
    @TableField("STORE_NAME")
    private String storeName;
    @ApiModelProperty(value = "支付平台：1=微信直连、2=收钱吧-线上支付、3=支付宝直连、4=电银、5=华润自研、6=银联-B扫C、7=收钱吧-轻POS支付、8=银联-轻POS支付、9=收钱吧-B扫C")
    @TableField("PAY_CHANNEL")
    private Integer payChannel;
    @ApiModelProperty(value = "支付配置ID：对应支付中心的不同平台配置表的ID")
    @TableField("PAY_CONFIG_ID")
    private String payConfigId;
    @ApiModelProperty(value = "微商城是否使用：0=否，1=是")
    @TableField("HAS_WXMALL")
    private Boolean hasWxmall;
    @ApiModelProperty(value = "POS+线下是否使用：0=否，1=是")
    @TableField("HAS_POS")
    private Boolean hasPos;
    @ApiModelProperty(value = "POS+线上（离店）是否使用：0=否，1=是")
    @TableField("HAS_POS_ONLINE")
    private Boolean hasPosOnline;
    @ApiModelProperty(value = "BOX是否使用：0=否，1=是")
    @TableField("HAS_BOX")
    private Boolean hasBox;
    @ApiModelProperty(value = "设备号")
    @TableField("DEVICE_NUMBER")
    private String deviceNumber;
    @ApiModelProperty(value = "储值卡是否适用：0=否，1=是")
    @TableField("HAS_STORE_CARD")
    private Boolean hasStoreCard;
}
