package com.jnby.mallasset.strategy.platform.k11;

import com.jnby.mallasset.util.BASE64Utils;
import com.jnby.mallasset.util.Md5Util;

import java.util.*;

public class KElevenGenerateSign {

    public static void main(String[] args) {
//        long timestamp = System.currentTimeMillis()/1000;
//        System.out.println(timestamp);
//
//        Map<String,Object> jsonObject = new HashMap<>();
//        jsonObject.put("phone","15820584615");
//        jsonObject.put("phone_code","86");
//        jsonObject.put("reg_origin","SZ02");
//        jsonObject.put("nick_name","会员");
//        System.out.println("json=" + JsonUtil.toJson(jsonObject));
//
//
//        Map<String, String> params = new TreeMap<>();
//        params.put("content", JsonUtil.toJson(jsonObject));
////        params.put("phone_code", "86");
////        params.put("reg_origin", "SZ02");
////        params.put("nick_name", "会员");
//        params.put("timestamp", timestamp + "");
//        params.put("apiKey", "761175265309414");
//        params.put("interfaceId", "42943192bb75e59a4211");
//        System.out.println(genSignature(params));


        String deSte = "efy3J8jab825Rel4IjowLCJtc2ciOiLmiJDlip8iLCJkYXRhIjpbeyJtZW1iZXJfaWQiOiIxMjU0NDYxNTAzNzA5OTM4Iiwibmlja19uYW1lIjoi5Lya5ZGYIiwiZW5fbmFtZSI6IiIsImxldmVsX2lkIjoiMTAwIiwidW5pb25faWQiOiIiLCJhcmVhX2lkIjoiMzAiLCJ2aXBfY29kZSI6IjEyNTQ0NjE1MDM3MDk5MzgiLCJzcG9pbnQiOiIwIiwia3BvaW50IjowLCJpc19iaW5kX2tkcCI6MCwibWFsbF9jb2RlIjoiU1owMiJ9XX0mbscd";
        System.out.println(decodeSignature(deSte,"f38a85e4"));
    }

    public static String genSignature(Map<String, String> params) {
        // 1. 添加apiSecret参数
        Map<String, String> paramsWithSecret = new TreeMap<>(params);
        // 2. 移除已有的sign参数（如果存在）
        paramsWithSecret.remove("sign");

        // 3. 按key排序
        List<String> sortedKeys = new ArrayList<>(paramsWithSecret.keySet());
        Collections.sort(sortedKeys);

        // 4. 拼接字符串
        StringBuilder sb = new StringBuilder();
        for (String key : sortedKeys) {
            sb.append(key).append("+").append(paramsWithSecret.get(key));
        }
        return Md5Util.getMd5HashForLow(sb.toString());
    }

    public static String decodeSignature(String string, String skey) {
        if (string == null || string.isEmpty()) {
            return "";
        }
        if (skey == null) {
            skey = "";
        }

        // 替换字符（类似PHP的str_replace）
        String replaced = string.replace("mqtp", ":")
                .replace("mbscd", "=")
                .replace("nnddt", "+")
                .replace("abcde", "/")
                .replace("adted", "|");

        // 每2个字符分割成数组
        String[] strArr = new String[(replaced.length() + 1) / 2];
        for (int i = 0; i < replaced.length(); i += 2) {
            int endIndex = Math.min(i + 2, replaced.length());
            strArr[i / 2] = replaced.substring(i, endIndex);
        }

        // 处理skey
        for (int key = 0; key < skey.length(); key++) {
            if (key < strArr.length) {
                char value = skey.charAt(key);
                if (strArr[key].length() > 1 && strArr[key].charAt(1) == value) {
                    strArr[key] = String.valueOf(strArr[key].charAt(0));
                }
            }
        }
        // 拼接数组并base64解码
        String joined = String.join("", strArr);
        return BASE64Utils.decode(joined);
    }
}



