package com.jnby.mallasset.remote.yintai99.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SalesItemEntity implements Serializable {
    @ApiModelProperty("銷售貨品的行號")
    private int salesLineNumber;
    @ApiModelProperty("銷售員編號")
    private String salesman;
    @ApiModelProperty("貨品編號或條形碼")
    private String itemCode;
    @ApiModelProperty("貨品所屬機構的識別碼")
    private String itemOrgId;
    @ApiModelProperty("貨品批號,*")
    private String itemLotNum;
    @ApiModelProperty("預留字段")
    private int serialNumber;
    @ApiModelProperty("庫存類型 : 0")
    private int inventoryType;
    @ApiModelProperty("銷售數量")
    private int qty;
    @ApiModelProperty("貨品折扣模式 0")
    private BigDecimal itemDiscountLess;
    @ApiModelProperty("貨品折扣模式 0")
    private BigDecimal totalDiscountLess;
    @ApiModelProperty("貨品淨銷售金額")
    private BigDecimal netAmount;
    @ApiModelProperty("貨品備註")
    private String salesItemRemark;
    @ApiModelProperty("擴展參數")
    private String extendParameter;
    @ApiModelProperty("会员积分")
    private Integer vipBonusEarn;
}
