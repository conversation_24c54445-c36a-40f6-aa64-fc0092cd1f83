package com.jnby.mallasset.remote.mallcoo.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Builder
@ApiModel("券使用检查回参")
@Data
public class CouponCheckRespEntity implements Serializable {
    @ApiModelProperty("使用状态 1：已使用 2：未使用")
    private Integer UseState;
}
