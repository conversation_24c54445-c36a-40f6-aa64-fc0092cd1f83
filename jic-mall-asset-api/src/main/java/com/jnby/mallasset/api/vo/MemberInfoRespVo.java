package com.jnby.mallasset.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MemberInfoRespVo implements Serializable {

    @ApiModelProperty("会员手机号")
    private String tel;
    @ApiModelProperty("会员昵称")
    private String nickName;
    @ApiModelProperty("会员卡号")
    private String cardNo;
    @ApiModelProperty("会员卡名称")
    private String cardName;
    @ApiModelProperty("会员卡等级编码")
    private String cardTypeCode;
    @ApiModelProperty("会员卡等级id")
    private String cardTypeID;
    @ApiModelProperty("会员用户id")
    private String userId;
}
