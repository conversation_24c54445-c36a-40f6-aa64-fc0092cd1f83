package com.jnby.mallasset.remote.mallcoo.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Builder
@ApiModel("可用券列表入参")
@Data
public class CouponCanUseListReqEntity implements Serializable {
    @ApiModelProperty("用户手机号")
    @JSONField(ordinal = 1, name = "Mobile")
    private String Mobile;

    @ApiModelProperty("猫酷核销商户ID")
    @JSONField(ordinal = 2, name = "McShopID")
    private Long McShopID;

    @ApiModelProperty("每页查询数据的数量")
    @JSONField(ordinal = 3, name = "PageSize")
    private Integer PageSize;

    @ApiModelProperty("查询小于XX券ID的数据（首页则填写0）【分页查询这里传入的是本次请求返回结果中最小的那个券ID即可】\n")
    @JSONField(ordinal = 4, name = "MinID")
    private Integer MinID;
}
