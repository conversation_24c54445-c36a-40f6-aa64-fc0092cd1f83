package com.jnby.mallasset.remote.huarun.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 文件名: com.jnby.mallasset.remote.huarun.entity-OrdItem.java
 * 文件简介: 付款信息
 *
 * <AUTHOR>
 * @date 2024/7/20 9:31
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class PayItems {

    @ApiModelProperty("付款ID")
    @JSONField(ordinal = 1)
    private Long id;
    @ApiModelProperty("付款名称")
    @JSONField(ordinal = 2)
    private String name;
    @ApiModelProperty("付款金额")
    @JSONField(ordinal = 3)
    private BigDecimal payamount;
}
