package com.jnby.mallasset.remote.xiexin.entity.vip;

import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

@Data
@Root(name = "GetVipCard", strict = false)
@Namespace(reference = "http://tempuri.org/")
public class XiexinVipRequestModel {

    @Element(name = "condType" , required = false)
    private String condType;

    @Element(name = "condValue" , required = false)
    private String condValue;
}
