package com.jnby.mallasset.remote.oeli.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("天目里会员回参")
@Data
public class MemberInfoRespEntity implements Serializable {
    @ApiModelProperty("是否注册")
    private Boolean register;
    @ApiModelProperty("会员积分")
    private Long point;
    @ApiModelProperty("会员类型:greenCard=绿卡，normal=普通会员")
    private String memberType;
    @ApiModelProperty("卡名")
    private String cardName;
}
