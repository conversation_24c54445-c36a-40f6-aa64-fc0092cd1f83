package com.jnby.mallasset.remote.k11.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 文件名: com.jnby.mallasset.remote.haiancheng.entity-BaseHacResp.java
 * 文件简介: 海岸城基础返回对象
 *
 * <AUTHOR>
 * @date 2025/1/13 9:51
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class OrderPayItemEntity implements Serializable {
    @ApiModelProperty(value = "支付类型")
    private String type;
    @ApiModelProperty(value = "支付方式付出积分")
    private double kpoint;
    @ApiModelProperty(value = "支付方式代表的金额")
    private BigDecimal price;
    @ApiModelProperty(value = "是否计算积分（用户实际支付金额）")
    private int is_real;

}
