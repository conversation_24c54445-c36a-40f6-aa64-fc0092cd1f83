package com.jnby.mallasset.remote.yintai.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TransPaying {
    @ApiModelProperty(value = "顺序号")
    private int sort;
    @ApiModelProperty(value = "支付时间")
    private String payTime;
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payAmt;
    @ApiModelProperty(value = "是否积分 :1 参与积分；0 不参与积分")
    private int isScore;
    @ApiModelProperty(value = "微信支付宝或其他支付渠道的支付流水号")
    private String traceNo;
    @ApiModelProperty(value = "付款方式")
    private String payName;
    @ApiModelProperty(value = "支付类型")
    private String payTypeId;
}
