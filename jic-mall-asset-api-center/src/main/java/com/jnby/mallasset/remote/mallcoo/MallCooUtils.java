package com.jnby.mallasset.remote.mallcoo;

import com.alibaba.fastjson.JSON;
import com.jnby.mallasset.config.exception.MallException;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.config.retrofit.RetrofitConfig;
import com.jnby.mallasset.remote.mallcoo.entity.*;
import com.jnby.mallasset.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import retrofit2.Response;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
public class MallCooUtils {

    public static Map<String, String> getHeaders(String appId, String publicKey, String privateKey, String json) {
        log.info("猫酷验签获取header入参,appId:[{}],publicKey:[{}],privateKey:[{}],json:[{}]", appId, publicKey, privateKey, json);
        Map<String, String> headers;
        try {
            String timeStamp = DateUtil.getShortStrDateTime(new Date());
            headers = new LinkedHashMap<>();
            headers.put("AppID", appId);
            headers.put("TimeStamp", timeStamp);
            headers.put("PublicKey", publicKey);

            String encryptString = new StringBuilder()
                    .append("{publicKey:").append(publicKey)
                    .append(",timeStamp:").append(timeStamp).append(",data:").append(json)
                    .append(",privateKey:").append(privateKey)
                    .append("}").toString();
            String str = getMD5(encryptString);
            headers.put("Sign", str);
        } catch (Exception e) {
            log.error("猫酷验签获取header异常", e);
            throw new MallException(SystemErrorEnum.AUTH_ERROR);
        }
        return headers;
    }

    public static void main(String[] args) {
        String appId = "66dfe3386554f0b396d37d58";
        String publicKey = "07ZWgm";
        String privateKey = "f7b49a2736c4298a";

//        MemberCreateCardReqEntity req = MemberCreateCardReqEntity.builder().mobile("15820584615").build();
        MemberQueryReqEntity req = new MemberQueryReqEntity();
        req.setMobile("17602536345");
        try {
            Map<String, String> headers = getHeaders(appId, publicKey, privateKey, JSON.toJSONString(req));
            IMallCooRemoteHttpApi api = RetrofitConfig.getMainApi("https://openapi10.mallcoo.cn", IMallCooRemoteHttpApi.class);
            Response<BaseMallCooResp<MemberQueryRespEntity>> execute = api.getMember2(headers, req).execute();
            BaseMallCooResp body = execute.body();
            MemberQueryRespEntity data = (MemberQueryRespEntity) body.getData();
            System.out.println(body.isSuccess());
            System.out.println(JSON.toJSONString(body));
            System.out.println(JSON.toJSONString(data));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void main0(String[] args) {
        String appId = "66dfe3386554f0b396d37d58";
        String publicKey = "07ZWgm";
        String privateKey = "f7b49a2736c4298a";

//        MemberCreateCardReqEntity req = MemberCreateCardReqEntity.builder().mobile("15820584615").build();
        MemberCreateCardReqEntity req = new MemberCreateCardReqEntity();
        req.setMobile("17602536345");
        try {
            Map<String, String> headers = getHeaders(appId, publicKey, privateKey, JSON.toJSONString(req));
            IMallCooRemoteHttpApi api = RetrofitConfig.getMainApi("https://openapi10.mallcoo.cn", IMallCooRemoteHttpApi.class);
            Response<BaseMallCooResp<MemberCreateCardRespEntity>> execute = api.createCard(headers, req).execute();
            BaseMallCooResp body = execute.body();
            System.out.println(body.isSuccess());
            System.out.println(JSON.toJSONString(body));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void main1(String[] args) {
        String appId = "668f6acf5e621a2583a8c6b4";
        String publicKey = "FmSvbe";
        String privateKey = "3f77f25da6ba1698";

        PointsPlusByOrderReqEntity req = new PointsPlusByOrderReqEntity();
        req.setMobile("15820584615");
//        req.setPayAmount(365.5);
//        req.setTotalAmount(1195.0);
//        req.setMcShopID(1427336L);
//        req.setTradeTime("2023-05-12 00:00:00");
//        req.setTransID("RE2305120191777");
        try {
            Map<String, String> headers = getHeaders(appId, publicKey, privateKey, JSON.toJSONString(req));
            IMallCooRemoteHttpApi api = RetrofitConfig.getMainApi("https://openapi10.mallcoo.cn", IMallCooRemoteHttpApi.class);
            Response<BaseMallCooResp<PointsPlusByOrderRespEntity>> execute = api.pointsPlusByOrder(headers, req).execute();
            BaseMallCooResp body = execute.body();
            System.out.println(body.isSuccess());
            System.out.println(JSON.toJSONString(body));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static String getMD5(String signStr) throws NoSuchAlgorithmException {
        MessageDigest md5Instance = MessageDigest.getInstance("MD5");
        md5Instance.update(signStr.getBytes(StandardCharsets.UTF_8));
        byte[] source = md5Instance.digest();

        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        int j = source.length;
        char[] str = new char[j * 2];
        int k = 0;
        for (byte byte0 : source) {
            str[k++] = hexDigits[byte0 >>> 4 & 0xf];
            str[k++] = hexDigits[byte0 & 0xf];
        }

        return new String(str).substring(8, 24);
    }
}
