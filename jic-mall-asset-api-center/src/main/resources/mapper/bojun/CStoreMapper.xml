<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.mallasset.module.mapper.bojun.CStoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.mallasset.module.model.bojun.CStore">
        <id column="ID" property="id" />
        <result column="AD_CLIENT_ID" property="adClientId" />
        <result column="AD_ORG_ID" property="adOrgId" />
        <result column="ISACTIVE" property="isactive" />
        <result column="MODIFIERID" property="modifierid" />
        <result column="CREATIONDATE" property="creationdate" />
        <result column="MODIFIEDDATE" property="modifieddate" />
        <result column="OWNERID" property="ownerid" />
        <result column="NAME" property="name" />
        <result column="DESCRIPTION" property="description" />
        <result column="C_AREA_ID" property="cAreaId" />
        <result column="LOCKCASH" property="lockcash" />
        <result column="ADDRESS" property="address" />
        <result column="PHONE" property="phone" />
        <result column="FAX" property="fax" />
        <result column="CONTACTOR_ID" property="contactorId" />
        <result column="MONTHFEE" property="monthfee" />
        <result column="ISSTOP" property="isstop" />
        <result column="RENTBEGIN" property="rentbegin" />
        <result column="RENTEND" property="rentend" />
        <result column="PROPORTION" property="proportion" />
        <result column="EMPCNT" property="empcnt" />
        <result column="CHECKDATE" property="checkdate" />
        <result column="ISCENTER" property="iscenter" />
        <result column="ISRETAIL" property="isretail" />
        <result column="MOBIL" property="mobil" />
        <result column="SNAME" property="sname" />
        <result column="POSTCAL" property="postcal" />
        <result column="CALCULATION" property="calculation" />
        <result column="C_CUSTOMER_ID" property="cCustomerId" />
        <result column="C_CUSTOMERUP_ID" property="cCustomerupId" />
        <result column="C_PRICEAREA_ID" property="cPriceareaId" />
        <result column="ISFAIRORIG" property="isfairorig" />
        <result column="AREAMNG_ID" property="areamngId" />
        <result column="LIMITQTY" property="limitqty" />
        <result column="LIMITAMT" property="limitamt" />
        <result column="LIMITMO" property="limitmo" />
        <result column="MARKDIS" property="markdis" />
        <result column="DATEBLOCK" property="dateblock" />
        <result column="C_STORETYPE_JZ_ID" property="cStoretypeJzId" />
        <result column="IMGURL1" property="imgurl1" />
        <result column="IMGURL2" property="imgurl2" />
        <result column="IMGURL3" property="imgurl3" />
        <result column="IMGURL4" property="imgurl4" />
        <result column="IMGURL5" property="imgurl5" />
        <result column="BIGAREAMNG_ID" property="bigareamngId" />
        <result column="C_PROVINCE_ID" property="cProvinceId" />
        <result column="C_CITY_ID" property="cCityId" />
        <result column="STORESIGN" property="storesign" />
        <result column="C_STORETYPE" property="cStoretype" />
        <result column="REMARK" property="remark" />
        <result column="CODE" property="code" />
        <result column="ISUFSTORE" property="isufstore" />
        <result column="C_STORE_ID" property="cStoreId" />
        <result column="C_DEPARTMENT_ID" property="cDepartmentId" />
        <result column="C_CLASSCODE_ID" property="cClasscodeId" />
        <result column="UF_CODE" property="ufCode" />
        <result column="BILLDATE_FRIST" property="billdateFrist" />
        <result column="PRIORITY" property="priority" />
        <result column="C_BLOCK_ID" property="cBlockId" />
        <result column="ISFICTITIOUS" property="isfictitious" />
        <result column="SHOP_RECEIVE_TYPE" property="shopReceiveType" />
        <result column="POSPW" property="pospw" />
        <result column="ISBLOCK" property="isblock" />
        <result column="CLOPSTORETYPE" property="clopstoretype" />
        <result column="DISCOUNT" property="discount" />
        <result column="CLOP_STORE" property="clopStore" />
        <result column="ISGIFT" property="isgift" />
        <result column="ISDISCOM" property="isdiscom" />
        <result column="C_DEPART_ID" property="cDepartId" />
        <result column="IMP_MONTH" property="impMonth" />
        <result column="IMP_TYPE1" property="impType1" />
        <result column="IMP_TYPE2" property="impType2" />
        <result column="IMP_TYPE3" property="impType3" />
        <result column="TAXRATE" property="taxrate" />
        <result column="C_STOREATTRIB1_ID" property="cStoreattrib1Id" />
        <result column="C_STOREATTRIB2_ID" property="cStoreattrib2Id" />
        <result column="C_STOREATTRIB3_ID" property="cStoreattrib3Id" />
        <result column="C_STOREATTRIB4_ID" property="cStoreattrib4Id" />
        <result column="C_STOREATTRIB5_ID" property="cStoreattrib5Id" />
        <result column="C_STOREATTRIB6_ID" property="cStoreattrib6Id" />
        <result column="C_STOREATTRIB7_ID" property="cStoreattrib7Id" />
        <result column="C_STOREATTRIB8_ID" property="cStoreattrib8Id" />
        <result column="C_STOREATTRIB9_ID" property="cStoreattrib9Id" />
        <result column="C_STOREATTRIB10_ID" property="cStoreattrib10Id" />
        <result column="C_STOREATTRIB11_ID" property="cStoreattrib11Id" />
        <result column="C_STOREATTRIB12_ID" property="cStoreattrib12Id" />
        <result column="C_STOREATTRIB13_ID" property="cStoreattrib13Id" />
        <result column="C_STOREATTRIB14_ID" property="cStoreattrib14Id" />
        <result column="C_STOREATTRIB15_ID" property="cStoreattrib15Id" />
        <result column="C_STOREATTRIB16_ID" property="cStoreattrib16Id" />
        <result column="C_STOREATTRIB17_ID" property="cStoreattrib17Id" />
        <result column="C_STOREATTRIB18_ID" property="cStoreattrib18Id" />
        <result column="C_STOREATTRIB19_ID" property="cStoreattrib19Id" />
        <result column="C_STOREATTRIB20_ID" property="cStoreattrib20Id" />
        <result column="ISNEGATIVE" property="isnegative" />
        <result column="TDEFDOWNTYPE_ID" property="tdefdowntypeId" />
        <result column="C_QTYADDAREA_ID" property="cQtyaddareaId" />
        <result column="C_CORP_ID" property="cCorpId" />
        <result column="USBKEY" property="usbkey" />
        <result column="IFEBSTORE" property="ifebstore" />
        <result column="DATE_ENDACCOUNT" property="dateEndaccount" />
        <result column="ISSTOCK" property="isstock" />
        <result column="Y_STORE" property="yStore" />
        <result column="C_VIPTYPE_ID1" property="cViptypeId1" />
        <result column="IF_WMS" property="ifWms" />
        <result column="IFORDERSTORE" property="iforderstore" />
        <result column="ORDERLIMITDATE" property="orderlimitdate" />
        <result column="WEBPOSLOGINURL" property="webposloginurl" />
        <result column="COMPTYPE" property="comptype" />
        <result column="C_STORE_SQL" property="cStoreSql" />
        <result column="M_DIM1_ID" property="mDim1Id" />
        <result column="STORETYPE" property="storetype" />
        <result column="RETCHKORG" property="retchkorg" />
        <result column="MARKET" property="market" />
        <result column="C_STOREGRADE_ID" property="cStoregradeId" />
        <result column="C_STOREKIND_ID" property="cStorekindId" />
        <result column="C_INTEGRALAREA_ID" property="cIntegralareaId" />
        <result column="FRAMWORK_AREA_ID" property="framworkAreaId" />
        <result column="C_ARCBRAND_ID" property="cArcbrandId" />
        <result column="IS_RESTORE" property="isRestore" />
        <result column="IS_MARK" property="isMark" />
        <result column="IS_RET" property="isRet" />
        <result column="WEBPOS_OFFLINE" property="webposOffline" />
        <result column="LOWEST_DISCOUNT" property="lowestDiscount" />
        <result column="CHK_OVERDAYS" property="chkOverdays" />
        <result column="C_MARKBALTYPE_ID" property="cMarkbaltypeId" />
        <result column="ISVIPINTL" property="isvipintl" />
        <result column="ISVIPDIS" property="isvipdis" />
        <result column="ISONLYCARD" property="isonlycard" />
        <result column="C_PAYWAY_DEFAULT" property="cPaywayDefault" />
        <result column="CREDITLIMIT" property="creditlimit" />
        <result column="EB_CREDITRANK_ID" property="ebCreditrankId" />
        <result column="EB_BONUSTYPE_ID" property="ebBonustypeId" />
        <result column="IS_TAOBAO" property="isTaobao" />
        <result column="EB_SHIPTYPE_ID" property="ebShiptypeId" />
        <result column="SHELFDEPTH" property="shelfdepth" />
        <result column="IS_MORESALESREP" property="isMoresalesrep" />
        <result column="DIM1_FILTER" property="dim1Filter" />
        <result column="C_POSADDR_ID" property="cPosaddrId" />
        <result column="POSPRINT" property="posprint" />
        <result column="IS_MODIFYPAYAMT" property="isModifypayamt" />
        <result column="OPENDATE" property="opendate" />
        <result column="LEASEPERIOD" property="leaseperiod" />
        <result column="ENDSALE" property="endsale" />
        <result column="USEMONTH" property="usemonth" />
        <result column="CONTRACT" property="contract" />
        <result column="ENDDATE" property="enddate" />
        <result column="RENCOST" property="rencost" />
        <result column="BEARCOMPANY_ID" property="bearcompanyId" />
        <result column="CHARGEOF_ID" property="chargeofId" />
        <result column="ALIPAY_KEY" property="alipayKey" />
        <result column="ALIPAY_PARTNERID" property="alipayPartnerid" />
        <result column="ALIPAY_SELL_MAIL" property="alipaySellMail" />
        <result column="IS_MARKETNO" property="isMarketno" />
        <result column="IS_MANUALINT" property="isManualint" />
        <result column="CONTACTOR" property="contactor" />
        <result column="BILLDATERANGE" property="billdaterange" />
        <result column="LONGITUDE" property="longitude" />
        <result column="LATITUDE" property="latitude" />
        <result column="IS_CREATEDATA" property="isCreatedata" />
        <result column="IS_TONC" property="isTonc" />
        <result column="IS_COMPARED" property="isCompared" />
        <result column="PREOPENDATE" property="preopendate" />
        <result column="C_STOREATTRIB21_ID" property="cStoreattrib21Id" />
        <result column="IS_PROCLOSE" property="isProclose" />
        <result column="IS_CHECKALIAS" property="isCheckalias" />
        <result column="C_CONSUMEAREA_ID" property="cConsumeareaId" />
        <result column="IS_COUNTER" property="isCounter" />
        <result column="IS_EXCSTORE" property="isExcstore" />
        <result column="ORGMODIFYPER" property="orgmodifyper" />
        <result column="C_DISTRICT_ID" property="cDistrictId" />
        <result column="WECHAT_CUSTOMERID" property="wechatCustomerid" />
        <result column="SUB_MCH_ID" property="subMchId" />
        <result column="ISAIXIU" property="isaixiu" />
        <result column="IS_SMARTPAY" property="isSmartpay" />
        <result column="HR_GROUP_ID" property="hrGroupId" />
        <result column="EB_STORAGE_TYPE" property="ebStorageType" />
        <result column="IS_JSTYPE" property="isJstype" />
        <result column="IS_BCLOUD_STORE" property="isBcloudStore" />
        <result column="RETAIL_RET_DAY" property="retailRetDay" />
        <result column="Q_NVL" property="qNvl" />
        <result column="C_MALL_ID" property="cMallId" />
        <result column="Q_PASSWORD" property="qPassword" />
        <result column="Q_SMS" property="qSms" />
        <result column="Q_CODE" property="qCode" />
        <result column="C_BIGAREA_ID" property="cBigareaId" />
        <result column="IS_BANKBUY" property="isBankbuy" />
        <result column="MAINMEDIAADDRESS" property="mainmediaaddress" />
        <result column="CHANGE" property="change" />
        <result column="CURRENCY" property="currency" />
        <result column="CURRENCY_SIGN" property="currencySign" />
        <result column="ISRETPAY" property="isretpay" />
        <result column="SF_CARDNO" property="sfCardno" />
        <result column="C_PAYWAY_FILTER" property="cPaywayFilter" />
        <result column="IS_ALLPAYWAY" property="isAllpayway" />
        <result column="IS_DISSTORE" property="isDisstore" />
        <result column="IS_UNIONSTORE" property="isUnionstore" />
        <result column="RETAIL_LOCATION" property="retailLocation" />
        <result column="IS_MORECURRENCY" property="isMorecurrency" />
        <result column="C_CURRENCY_ID" property="cCurrencyId" />
        <result column="POS_AUTO_DIS" property="posAutoDis" />
        <result column="SUBSYSTEM_NAME" property="subsystemName" />
        <result column="MENU_LIST" property="menuList" />
        <result column="ISRET_LEVEL_ON" property="isretLevelOn" />
        <result column="ISPADPOS" property="ispadpos" />
        <result column="ISKEEP_PWD" property="iskeepPwd" />
        <result column="DATE_HOUR_OFFSET" property="dateHourOffset" />
        <result column="POS_SERIALNO" property="posSerialno" />
        <result column="IS_HPMART" property="isHpmart" />
        <result column="SHOPID" property="shopid" />
        <result column="SHOPCODE" property="shopcode" />
        <result column="PRODUCTCODE" property="productcode" />
        <result column="Q_CODE_POS" property="qCodePos" />
        <result column="GUESTMACHINECOM" property="guestmachinecom" />
        <result column="GUESTMACHINE" property="guestmachine" />
        <result column="ISMUSTENTERVIP" property="ismustentervip" />
        <result column="CHKDAY" property="chkday" />
        <result column="MALLCODE" property="mallcode" />
        <result column="COUNTERNUM" property="counternum" />
        <result column="EB_EXPRESS_ID" property="ebExpressId" />
        <result column="SDTSTORE" property="sdtstore" />
        <result column="IS_BPAYSHOWVOUCHER" property="isBpayshowvoucher" />
        <result column="WECHAT_CUSTOMERIDNEW" property="wechatCustomeridnew" />
        <result column="C_COUNTRY_ID" property="cCountryId" />
        <result column="IS_O2O" property="isO2o" />
        <result column="ALLOW_RECEIPT" property="allowReceipt" />
        <result column="IS_MODIFYAMT_REASON" property="isModifyamtReason" />
        <result column="BPOSEX" property="bposex" />
        <result column="ISSDT" property="issdt" />
        <result column="DXLX" property="dxlx" />
        <result column="C_UNIONSTORE_ID" property="cUnionstoreId" />
        <result column="IS_AUTOIN" property="isAutoin" />
        <result column="DEFAULT_CHARGEPAYWAY" property="defaultChargepayway" />
        <result column="DEFAULTLASTDATE" property="defaultlastdate" />
        <result column="INVOICE_TEMPLATE" property="invoiceTemplate" />
        <result column="IS_SHOWVISITPLAN" property="isShowvisitplan" />
        <result column="INVOICE_TAXNO" property="invoiceTaxno" />
        <result column="CHECKDATADOWN" property="checkdatadown" />
        <result column="PORDERLIMITDATE" property="porderlimitdate" />
        <result column="IS_CHECKSKUONLINE" property="isCheckskuonline" />
        <result column="COMBINEAFTER" property="combineafter" />
        <result column="IS_REFRESHNETWORK" property="isRefreshnetwork" />
        <result column="IS_FORCEBPOS" property="isForcebpos" />
        <result column="DESCRIPTION1" property="description1" />
        <result column="IS_OXO" property="isOxo" />
        <result column="JIT_STORECODE" property="jitStorecode" />
        <result column="C_DISTR__YXJ_ID" property="cDistrYxjId" />
        <result column="IS_FILTERVIPTYPE" property="isFilterviptype" />
        <result column="IS_VIP_CHECK" property="isVipCheck" />
        <result column="IS_BRITHDAYDIS" property="isBrithdaydis" />
        <result column="BROWSER_OPEN_MODE" property="browserOpenMode" />
        <result column="IS_INTEGRAL_BASE" property="isIntegralBase" />
        <result column="IS_ORDER" property="isOrder" />
        <result column="CAN_GRAB" property="canGrab" />
        <result column="IS_PERFORMANCE_SHARE" property="isPerformanceShare" />
        <result column="MORLTHENOTE" property="morlthenote" />
        <result column="ISMORLTHENOTEP" property="ismorlthenotep" />
        <result column="IS_CONTROL" property="isControl" />
        <result column="IS_PRINT_ELECTRONICINVOICE" property="isPrintElectronicinvoice" />
        <result column="OPENVIP_AMT" property="openvipAmt" />
        <result column="MORDERLIMITDATE" property="morderlimitdate" />
        <result column="PRINTTPLS4SGLPDT" property="printtpls4sglpdt" />
        <result column="INVOICE_PHONE" property="invoicePhone" />
        <result column="INVOICE_ACCOUNT" property="invoiceAccount" />
        <result column="INVOICE_BANK" property="invoiceBank" />
        <result column="INVOICE_ADDR" property="invoiceAddr" />
        <result column="INVOICE_COM" property="invoiceCom" />
        <result column="MALL_NAME" property="mallName" />
        <result column="MALL_NO" property="mallNo" />
        <result column="MALL_ADDRESS" property="mallAddress" />
        <result column="IS_STONO" property="isStono" />
        <result column="IS_WMSSTORE" property="isWmsstore" />
        <result column="WMS_STORECODE" property="wmsStorecode" />
        <result column="IS_TOWMS" property="isTowms" />
        <result column="IS_MARKET" property="isMarket" />
        <result column="C_REALSTORE_ID" property="cRealstoreId" />
        <result column="DESCRIPTION01" property="description01" />
        <result column="DESCRIPTION02" property="description02" />
        <result column="DESCRIPTION03" property="description03" />
        <result column="DESCRIPTION04" property="description04" />
        <result column="DESCRIPTION05" property="description05" />
        <result column="MERCHANT_NUMBER" property="merchantNumber" />
        <result column="PAYMENT_KEY" property="paymentKey" />
        <result column="IS_DELIVERY" property="isDelivery" />
        <result column="DESKEY" property="deskey" />
        <result column="CARRYMODE" property="carrymode" />
        <result column="IS_SELECTPRINT" property="isSelectprint" />
        <result column="PRINTTEMPLATELIST" property="printtemplatelist" />
        <result column="IS_EXAMINATION" property="isExamination" />
        <result column="SHOUQIANBACODE" property="shouqianbacode" />
        <result column="WECHAT_CUSTOMERIDNEW2" property="wechatCustomeridnew2" />
        <result column="SHOUQIANBA_USE" property="shouqianbaUse" />
        <result column="O2OVOICE" property="o2ovoice" />
        <result column="PENDVOICE" property="pendvoice" />
        <result column="MERCHANT" property="merchant" />
        <result column="VIPPRINTTEMPLATE" property="vipprinttemplate" />
        <result column="JTK" property="jtk" />
        <result column="RECHARGE" property="recharge" />
        <result column="COMFIRM_BEFOREPAY" property="comfirmBeforepay" />
        <result column="MONITOR_URL" property="monitorUrl" />
        <result column="MANAGERNAV_OPEN_MODE" property="managernavOpenMode" />
        <result column="VOUCHER_STORE_TYPE" property="voucherStoreType" />
        <result column="ISMONENY" property="ismoneny" />
        <result column="MOBILEPAYSOLUTION" property="mobilepaysolution" />
        <result column="INTERNAL_PURCHASE_STORE" property="internalPurchaseStore" />
        <result column="PADPOS_TEMPLATE" property="padposTemplate" />
        <result column="ALLOW_CUSTOMER" property="allowCustomer" />
        <result column="BRANCHIDCARD" property="branchidcard" />
        <result column="POSIDCARD" property="posidcard" />
        <result column="ONLINEORDER" property="onlineorder" />
        <result column="VIP_ACTIVATE" property="vipActivate" />
        <result column="IS_WADE" property="isWade" />
        <result column="ISPRINTHOLDRETAIL" property="isprintholdretail" />
        <result column="MD5" property="md5" />
        <result column="MISPOSCARD" property="misposcard" />
        <result column="MISPOSTERMINAL" property="misposterminal" />
        <result column="MISPOSVISION" property="misposvision" />
        <result column="WECHAT_CUSTOMERIDNEW3" property="wechatCustomeridnew3" />
        <result column="WECHAT_CUSTOMERIDNEW4" property="wechatCustomeridnew4" />
        <result column="CCB_POSB_TERM_NO" property="ccbPosbTermNo" />
        <result column="USER_ID" property="userId" />
        <result column="LANDI_UNIONPAY" property="landiUnionpay" />
        <result column="WECHAT_CUSTOMERIDNEW5" property="wechatCustomeridnew5" />
        <result column="POSBTOC_POSB_TERM_NO" property="posbtocPosbTermNo" />
        <result column="ISMOBILEPAYS" property="ismobilepays" />
        <result column="CONSUMER_CARD_PAY_TYPE" property="consumerCardPayType" />
        <result column="PAYWEB_APPKEY" property="paywebAppkey" />
        <result column="MANUALLYENTER" property="manuallyenter" />
        <result column="IS_VIP" property="isVip" />
        <result column="C_STOREATTRIB22_ID" property="cStoreattrib22Id" />
        <result column="IS_YUNDONG" property="isYundong" />
        <result column="IS_PENGMA" property="isPengma" />
        <result column="INVENTORY_YEAR" property="inventoryYear" />
        <result column="WAREHOUSE_RANG" property="warehouseRang" />
        <result column="CLOSEDATE" property="closedate" />
        <result column="IS_TINGYE" property="isTingye" />
        <result column="C_STOREATTRIB24_ID" property="cStoreattrib24Id" />
        <result column="C_STOREATTRIB25_ID" property="cStoreattrib25Id" />
        <result column="C_STOREATTRIB26_ID" property="cStoreattrib26Id" />
        <result column="C_STOREATTRIB27_ID" property="cStoreattrib27Id" />
        <result column="C_STOREATTRIB28_ID" property="cStoreattrib28Id" />
        <result column="C_STOREATTRIB29_ID" property="cStoreattrib29Id" />
        <result column="C_STOREATTRIB30_ID" property="cStoreattrib30Id" />
        <result column="C_STOREATTRIB31_ID" property="cStoreattrib31Id" />
        <result column="C_STOREATTRIB32_ID" property="cStoreattrib32Id" />
        <result column="C_STOREATTRIB33_ID" property="cStoreattrib33Id" />
        <result column="C_STOREATTRIB34_ID" property="cStoreattrib34Id" />
        <result column="C_STOREATTRIB35_ID" property="cStoreattrib35Id" />
        <result column="C_STOREATTRIB36_ID" property="cStoreattrib36Id" />
        <result column="C_STOREATTRIB37_ID" property="cStoreattrib37Id" />
        <result column="C_STOREATTRIB38_ID" property="cStoreattrib38Id" />
        <result column="C_STOREATTRIB39_ID" property="cStoreattrib39Id" />
        <result column="HEDIAN_PINPAI" property="hedianPinpai" />
        <result column="MARKET_NAME" property="marketName" />
        <result column="YEAR_RENT" property="yearRent" />
        <result column="DEC_OPENDATE" property="decOpendate" />
        <result column="TAX_DIS" property="taxDis" />
        <result column="YEAR_AMT_BOT" property="yearAmtBot" />
        <result column="CONTRACT_STATUS" property="contractStatus" />
        <result column="CLOSE_RESON" property="closeReson" />
        <result column="DISPLAY" property="display" />
        <result column="STORE_TYPE" property="storeType" />
        <result column="IS_BUS_LICENSE" property="isBusLicense" />
        <result column="C_STOREATTRIB23_ID" property="cStoreattrib23Id" />
        <result column="AREAMNG_NEW" property="areamngNew" />
        <result column="BIGAREAMNG_NEW" property="bigareamngNew" />
        <result column="TAX_NATURE" property="taxNature" />
        <result column="GRADE" property="grade" />
        <result column="IS_YTONO" property="isYtono" />
        <result column="DEFAULT01" property="default01" />
        <result column="DEFAULT02" property="default02" />
        <result column="DEFAULT04" property="default04" />
        <result column="DEFAULT05" property="default05" />
        <result column="DEFAULT06" property="default06" />
        <result column="DEFAULT15" property="default15" />
        <result column="DEFAULT03" property="default03" />
        <result column="DEFAULT07" property="default07" />
        <result column="DEFAULT08" property="default08" />
        <result column="DEFAULT09" property="default09" />
        <result column="DEFAULT10" property="default10" />
        <result column="DEFAULT11" property="default11" />
        <result column="DEFAULT13" property="default13" />
        <result column="DEFAULT14" property="default14" />
        <result column="DEFAULT12" property="default12" />
        <result column="OP_DEVICE_ID" property="opDeviceId" />
        <result column="ISINVOICE" property="isinvoice" />
        <result column="RETURNPRICE" property="returnprice" />
        <result column="ZX_CUSTOMERID" property="zxCustomerid" />
        <result column="INVOICEURL" property="invoiceurl" />
        <result column="SBSNUM" property="sbsnum" />
        <result column="ISUSERRFID" property="isuserrfid" />
        <result column="SUBORDERZT" property="suborderzt" />
        <result column="CAN_GRAB_NOCAN" property="canGrabNocan" />
        <result column="IS_UNIQUE" property="isUnique" />
        <result column="IS_CHECKSKUECRM" property="isCheckskuecrm" />
        <result column="IS_TORFID" property="isTorfid" />
        <result column="USE_POS" property="usePos" />
        <result column="ISUNIREC" property="isunirec" />
        <result column="C_STORE_JD_ID" property="cStoreJdId" />
        <result column="REMARK_JD" property="remarkJd" />
        <result column="EB_EXPRESS_JD_ID" property="ebExpressJdId" />
        <result column="USEPOSITION" property="useposition" />
        <result column="POSITIONMD" property="positionmd" />
        <result column="FH_BRAND" property="fhBrand" />
        <result column="INVOICE_COMPANY" property="invoiceCompany" />
        <result column="WECHAT_CUSTOMERIDNEW5_2" property="wechatCustomeridnew52" />
        <result column="POSBTOC_POSB_TERM_NO_2" property="posbtocPosbTermNo2" />
        <result column="DATUM_NUMBER" property="datumNumber" />
        <result column="VOU_INPUT_TYPE" property="vouInputType" />
        <result column="INIT_INPUT_TYPE" property="initInputType" />
        <result column="STORE_CLERK" property="storeClerk" />
        <result column="STORE_SALEACCOUNT" property="storeSaleaccount" />
        <result column="STORE_SALETAXNUM" property="storeSaletaxnum" />
        <result column="OLD_CODE" property="oldCode" />
        <result column="SMALL_ROUTINE" property="smallRoutine" />
        <result column="CHECK_CODE" property="checkCode" />
        <result column="OLD_XSQY" property="oldXsqy" />
        <result column="STORE_NAME" property="storeName" />
        <result column="FLOOR_PLAN" property="floorPlan" />
        <result column="SEATING_IMG" property="seatingImg" />
        <result column="TOSOLVED" property="tosolved" />
        <result column="IS_JPC" property="isJpc" />
        <result column="ZT_MONTHCODE" property="ztMonthcode" />
        <result column="C_STOREGS_ID" property="cStoregsId" />
        <result column="IS_TM" property="isTm" />
        <result column="TRANIN_STORE_ID" property="traninStoreId" />
        <result column="IS_CSC" property="isCsc" />
        <result column="C_STOREATTRIB40_ID" property="cStoreattrib40Id" />
        <result column="C_STOREATTRIB41_ID" property="cStoreattrib41Id" />
        <result column="IS_KG" property="isKg" />
        <result column="IS_YW" property="isYw" />
        <result column="BEGIN_NEWZX" property="beginNewzx" />
        <result column="END_NEWZX" property="endNewzx" />
        <result column="REMARK1" property="remark1" />
        <result column="REMARK2" property="remark2" />
        <result column="REMARK3" property="remark3" />
        <result column="REMARK4" property="remark4" />
        <result column="REMARK5" property="remark5" />
        <result column="REMARK6" property="remark6" />
        <result column="IS_KCSHOP" property="isKcshop" />
        <result column="KCSHOP_CODE" property="kcshopCode" />
        <result column="BIGGEST_DISCOUNT" property="biggestDiscount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, AD_CLIENT_ID, AD_ORG_ID, ISACTIVE, MODIFIERID, CREATIONDATE, MODIFIEDDATE, OWNERID, NAME, DESCRIPTION, C_AREA_ID, LOCKCASH, ADDRESS, PHONE, FAX, CONTACTOR_ID, MONTHFEE, ISSTOP, RENTBEGIN, RENTEND, PROPORTION, EMPCNT, CHECKDATE, ISCENTER, ISRETAIL, MOBIL, SNAME, POSTCAL, CALCULATION, C_CUSTOMER_ID, C_CUSTOMERUP_ID, C_PRICEAREA_ID, ISFAIRORIG, AREAMNG_ID, LIMITQTY, LIMITAMT, LIMITMO, MARKDIS, DATEBLOCK, C_STORETYPE_JZ_ID, IMGURL1, IMGURL2, IMGURL3, IMGURL4, IMGURL5, BIGAREAMNG_ID, C_PROVINCE_ID, C_CITY_ID, STORESIGN, C_STORETYPE, REMARK, CODE, ISUFSTORE, C_STORE_ID, C_DEPARTMENT_ID, C_CLASSCODE_ID, UF_CODE, BILLDATE_FRIST, PRIORITY, C_BLOCK_ID, ISFICTITIOUS, SHOP_RECEIVE_TYPE, POSPW, ISBLOCK, CLOPSTORETYPE, DISCOUNT, CLOP_STORE, ISGIFT, ISDISCOM, C_DEPART_ID, IMP_MONTH, IMP_TYPE1, IMP_TYPE2, IMP_TYPE3, TAXRATE, C_STOREATTRIB1_ID, C_STOREATTRIB2_ID, C_STOREATTRIB3_ID, C_STOREATTRIB4_ID, C_STOREATTRIB5_ID, C_STOREATTRIB6_ID, C_STOREATTRIB7_ID, C_STOREATTRIB8_ID, C_STOREATTRIB9_ID, C_STOREATTRIB10_ID, C_STOREATTRIB11_ID, C_STOREATTRIB12_ID, C_STOREATTRIB13_ID, C_STOREATTRIB14_ID, C_STOREATTRIB15_ID, C_STOREATTRIB16_ID, C_STOREATTRIB17_ID, C_STOREATTRIB18_ID, C_STOREATTRIB19_ID, C_STOREATTRIB20_ID, ISNEGATIVE, TDEFDOWNTYPE_ID, C_QTYADDAREA_ID, C_CORP_ID, USBKEY, IFEBSTORE, DATE_ENDACCOUNT, ISSTOCK, Y_STORE, C_VIPTYPE_ID1, IF_WMS, IFORDERSTORE, ORDERLIMITDATE, WEBPOSLOGINURL, COMPTYPE, C_STORE_SQL, M_DIM1_ID, STORETYPE, RETCHKORG, MARKET, C_STOREGRADE_ID, C_STOREKIND_ID, C_INTEGRALAREA_ID, FRAMWORK_AREA_ID, C_ARCBRAND_ID, IS_RESTORE, IS_MARK, IS_RET, WEBPOS_OFFLINE, LOWEST_DISCOUNT, CHK_OVERDAYS, C_MARKBALTYPE_ID, ISVIPINTL, ISVIPDIS, ISONLYCARD, C_PAYWAY_DEFAULT, CREDITLIMIT, EB_CREDITRANK_ID, EB_BONUSTYPE_ID, IS_TAOBAO, EB_SHIPTYPE_ID, SHELFDEPTH, IS_MORESALESREP, DIM1_FILTER, C_POSADDR_ID, POSPRINT, IS_MODIFYPAYAMT, OPENDATE, LEASEPERIOD, ENDSALE, USEMONTH, CONTRACT, ENDDATE, RENCOST, BEARCOMPANY_ID, CHARGEOF_ID, ALIPAY_KEY, ALIPAY_PARTNERID, ALIPAY_SELL_MAIL, IS_MARKETNO, IS_MANUALINT, CONTACTOR, BILLDATERANGE, LONGITUDE, LATITUDE, IS_CREATEDATA, IS_TONC, IS_COMPARED, PREOPENDATE, C_STOREATTRIB21_ID, IS_PROCLOSE, IS_CHECKALIAS, C_CONSUMEAREA_ID, IS_COUNTER, IS_EXCSTORE, ORGMODIFYPER, C_DISTRICT_ID, WECHAT_CUSTOMERID, SUB_MCH_ID, ISAIXIU, IS_SMARTPAY, HR_GROUP_ID, EB_STORAGE_TYPE, IS_JSTYPE, IS_BCLOUD_STORE, RETAIL_RET_DAY, Q_NVL, C_MALL_ID, Q_PASSWORD, Q_SMS, Q_CODE, C_BIGAREA_ID, IS_BANKBUY, MAINMEDIAADDRESS, CHANGE, CURRENCY, CURRENCY_SIGN, ISRETPAY, SF_CARDNO, C_PAYWAY_FILTER, IS_ALLPAYWAY, IS_DISSTORE, IS_UNIONSTORE, RETAIL_LOCATION, IS_MORECURRENCY, C_CURRENCY_ID, POS_AUTO_DIS, SUBSYSTEM_NAME, MENU_LIST, ISRET_LEVEL_ON, ISPADPOS, ISKEEP_PWD, DATE_HOUR_OFFSET, POS_SERIALNO, IS_HPMART, SHOPID, SHOPCODE, PRODUCTCODE, Q_CODE_POS, GUESTMACHINECOM, GUESTMACHINE, ISMUSTENTERVIP, CHKDAY, MALLCODE, COUNTERNUM, EB_EXPRESS_ID, SDTSTORE, IS_BPAYSHOWVOUCHER, WECHAT_CUSTOMERIDNEW, C_COUNTRY_ID, IS_O2O, ALLOW_RECEIPT, IS_MODIFYAMT_REASON, BPOSEX, ISSDT, DXLX, C_UNIONSTORE_ID, IS_AUTOIN, DEFAULT_CHARGEPAYWAY, DEFAULTLASTDATE, INVOICE_TEMPLATE, IS_SHOWVISITPLAN, INVOICE_TAXNO, CHECKDATADOWN, PORDERLIMITDATE, IS_CHECKSKUONLINE, COMBINEAFTER, IS_REFRESHNETWORK, IS_FORCEBPOS, DESCRIPTION1, IS_OXO, JIT_STORECODE, C_DISTR__YXJ_ID, IS_FILTERVIPTYPE, IS_VIP_CHECK, IS_BRITHDAYDIS, BROWSER_OPEN_MODE, IS_INTEGRAL_BASE, IS_ORDER, CAN_GRAB, IS_PERFORMANCE_SHARE, MORLTHENOTE, ISMORLTHENOTEP, IS_CONTROL, IS_PRINT_ELECTRONICINVOICE, OPENVIP_AMT, MORDERLIMITDATE, PRINTTPLS4SGLPDT, INVOICE_PHONE, INVOICE_ACCOUNT, INVOICE_BANK, INVOICE_ADDR, INVOICE_COM, MALL_NAME, MALL_NO, MALL_ADDRESS, IS_STONO, IS_WMSSTORE, WMS_STORECODE, IS_TOWMS, IS_MARKET, C_REALSTORE_ID, DESCRIPTION01, DESCRIPTION02, DESCRIPTION03, DESCRIPTION04, DESCRIPTION05, MERCHANT_NUMBER, PAYMENT_KEY, IS_DELIVERY, DESKEY, CARRYMODE, IS_SELECTPRINT, PRINTTEMPLATELIST, IS_EXAMINATION, SHOUQIANBACODE, WECHAT_CUSTOMERIDNEW2, SHOUQIANBA_USE, O2OVOICE, PENDVOICE, MERCHANT, VIPPRINTTEMPLATE, JTK, RECHARGE, COMFIRM_BEFOREPAY, MONITOR_URL, MANAGERNAV_OPEN_MODE, VOUCHER_STORE_TYPE, ISMONENY, MOBILEPAYSOLUTION, INTERNAL_PURCHASE_STORE, PADPOS_TEMPLATE, ALLOW_CUSTOMER, BRANCHIDCARD, POSIDCARD, ONLINEORDER, VIP_ACTIVATE, IS_WADE, ISPRINTHOLDRETAIL, MD5, MISPOSCARD, MISPOSTERMINAL, MISPOSVISION, WECHAT_CUSTOMERIDNEW3, WECHAT_CUSTOMERIDNEW4, CCB_POSB_TERM_NO, USER_ID, LANDI_UNIONPAY, WECHAT_CUSTOMERIDNEW5, POSBTOC_POSB_TERM_NO, ISMOBILEPAYS, CONSUMER_CARD_PAY_TYPE, PAYWEB_APPKEY, MANUALLYENTER, IS_VIP, C_STOREATTRIB22_ID, IS_YUNDONG, IS_PENGMA, INVENTORY_YEAR, WAREHOUSE_RANG, CLOSEDATE, IS_TINGYE, C_STOREATTRIB24_ID, C_STOREATTRIB25_ID, C_STOREATTRIB26_ID, C_STOREATTRIB27_ID, C_STOREATTRIB28_ID, C_STOREATTRIB29_ID, C_STOREATTRIB30_ID, C_STOREATTRIB31_ID, C_STOREATTRIB32_ID, C_STOREATTRIB33_ID, C_STOREATTRIB34_ID, C_STOREATTRIB35_ID, C_STOREATTRIB36_ID, C_STOREATTRIB37_ID, C_STOREATTRIB38_ID, C_STOREATTRIB39_ID, HEDIAN_PINPAI, MARKET_NAME, YEAR_RENT, DEC_OPENDATE, TAX_DIS, YEAR_AMT_BOT, CONTRACT_STATUS, CLOSE_RESON, DISPLAY, STORE_TYPE, IS_BUS_LICENSE, C_STOREATTRIB23_ID, AREAMNG_NEW, BIGAREAMNG_NEW, TAX_NATURE, GRADE, IS_YTONO, DEFAULT01, DEFAULT02, DEFAULT04, DEFAULT05, DEFAULT06, DEFAULT15, DEFAULT03, DEFAULT07, DEFAULT08, DEFAULT09, DEFAULT10, DEFAULT11, DEFAULT13, DEFAULT14, DEFAULT12, OP_DEVICE_ID, ISINVOICE, RETURNPRICE, ZX_CUSTOMERID, INVOICEURL, SBSNUM, ISUSERRFID, SUBORDERZT, CAN_GRAB_NOCAN, IS_UNIQUE, IS_CHECKSKUECRM, IS_TORFID, USE_POS, ISUNIREC, C_STORE_JD_ID, REMARK_JD, EB_EXPRESS_JD_ID, USEPOSITION, POSITIONMD, FH_BRAND, INVOICE_COMPANY, WECHAT_CUSTOMERIDNEW5_2, POSBTOC_POSB_TERM_NO_2, DATUM_NUMBER, VOU_INPUT_TYPE, INIT_INPUT_TYPE, STORE_CLERK, STORE_SALEACCOUNT, STORE_SALETAXNUM, OLD_CODE, SMALL_ROUTINE, CHECK_CODE, OLD_XSQY, STORE_NAME, FLOOR_PLAN, SEATING_IMG, TOSOLVED, IS_JPC, ZT_MONTHCODE, C_STOREGS_ID, IS_TM, TRANIN_STORE_ID, IS_CSC, C_STOREATTRIB40_ID, C_STOREATTRIB41_ID, IS_KG, IS_YW, BEGIN_NEWZX, END_NEWZX, REMARK1, REMARK2, REMARK3, REMARK4, REMARK5, REMARK6, IS_KCSHOP, KCSHOP_CODE, BIGGEST_DISCOUNT
    </sql>

</mapper>