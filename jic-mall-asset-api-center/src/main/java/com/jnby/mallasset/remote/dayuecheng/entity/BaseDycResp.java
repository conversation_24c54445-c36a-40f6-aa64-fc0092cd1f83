package com.jnby.mallasset.remote.dayuecheng.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-BaseDrcResp.java
 * 文件简介: 大悦城基础返回对象
 *
 * <AUTHOR>
 * @date 2024/7/23 14:00
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class BaseDycResp<T> implements Serializable {
    @ApiModelProperty(value = "响应结果")
    private int code;
    @ApiModelProperty(value = "响应消息")
    private String msg;
    @ApiModelProperty(value = "服务响应结果码")
    private int result_code;
    @ApiModelProperty(value = "服务响应消息")
    private String result_msg;
    @ApiModelProperty("签名")
    private String sign;
    @ApiModelProperty("响应数据")
    private T data;

    public boolean isSuccess(){
        if (this.code == 0) {
            return true;
        }
        return false;
    }
}
