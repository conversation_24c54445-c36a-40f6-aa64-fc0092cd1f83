package com.jnby.mallasset.sdk.api;

import com.jnby.mallasset.sdk.model.WhaleResponse;

interface Api {
    String test();

    WhaleResponse cameraFootfall(int categoryType, String body, String appId, String secret, String iv);

    WhaleResponse carParking(int categoryType, String body, String appId, String secret, String iv);

    WhaleResponse crm(int categoryType, String body, String appId, String secret, String iv);

    WhaleResponse pos(int categoryType, String body, String appId, String secret, String iv);

    WhaleResponse wifi(int categoryType, String body, String appId, String secret, String iv);

    WhaleResponse application(int categoryType, String body, String appId, String secret, String iv);

    WhaleResponse tag(int categoryType, String body, String appId, String secret, String iv);

    WhaleResponse biEvent(int categoryType, String body, String appId, String secret, String iv);
}
