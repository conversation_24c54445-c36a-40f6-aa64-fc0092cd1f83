package com.jnby.mallasset.remote.wanke.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class WanKeOrderRefundReqEntity implements Serializable {
    @ApiModelProperty(value = "订单号(系统内唯一标识)")
    @JSONField(ordinal = 1)
    private String orderNo;
    @ApiModelProperty(value = "原支付单单号")
    @JSONField(ordinal = 2)
    private String oriOrderNo;
    @ApiModelProperty(value = "印享星商户编码")
    @JSONField(ordinal = 3)
    private String storeCode;
    @ApiModelProperty(value = "订单渠道 1：pos，2口碑，3点评，5购啊购")
    @JSONField(ordinal = 4)
    private String channel;
    @ApiModelProperty(value = "原支付金额(单位：分)")
    @JSONField(ordinal = 5)
    private String payAmount;
    @ApiModelProperty(value = "退款金额(单位：分)")
    @JSONField(ordinal = 6)
    private String refundAmount;
    @ApiModelProperty(value = "退款时间(yyyy-MM-dd hh:mm:ss)")
    @JSONField(ordinal = 7)
    private String refundTime;
    @ApiModelProperty(value = "会员ID")
    @JSONField(ordinal = 8)
    private String memberId;
    @ApiModelProperty(value = "会员手机号")
    @JSONField(ordinal = 9)
    private String telephone;
    @ApiModelProperty(value = "支付方式 1现金 2银行卡 3微信 4支付宝 5未知")
    @JSONField(ordinal = 10)
    private String payType;
    @ApiModelProperty(value = "退款方式")
    @JSONField(ordinal = 11)
    private String payMethod;
}
