package com.jnby.mallasset.remote.dayuecheng.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DycMemberRegisterReqEntity.java
 * 文件简介: 会员注册请求
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */

@Data
public class DycMemberQueryRespEntity extends DycCommonReqEntity {
    @ApiModelProperty(value = "会员编号")
    private String cid;
    @ApiModelProperty(value = "账号")
    private String accountNo;
    @ApiModelProperty(value = "卡面编号")
    private String cardNo;
    @ApiModelProperty(value = "状态 0：无效 1：有效")
    private Integer active;
    @ApiModelProperty(value = "总可用积分")
    private Integer balance;
    @ApiModelProperty(value = "累计积分")
    private Integer accumulateScore;
    @ApiModelProperty(value = "当前积分")
    private Double currentBonus;
}
