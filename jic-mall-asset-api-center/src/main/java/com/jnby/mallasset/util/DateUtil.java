package com.jnby.mallasset.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * 〈一句话功能简述〉<br>
 * 〈Date工具类〉
 *
 * <AUTHOR>
 * @see [相关类/方法]（可选）
 * @since [产品/模块版本] （可选）
 */
public final class DateUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);

    /**
     * 年月日常量
     */
    public final static String DATEFORMATE_YYYYMMDD = "yyyyMMdd";

    public final static String DATEFORMATE_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public final static String DATEFORMATE_YYYY_MM_DD = "yyyy-MM-dd";

    public final static String DATEFORMATE_YYYY_MM = "yyyy-MM";

    public final static String DATEFORMATE_YYYY_MM_DD_HHMMSS = "yyyy-MM-dd HH:mm:ss";

    public final static String DATEFORMATE_YYYY_MM_DD_HHMMSS_POINT = "yyyy.MM.dd HH:mm:ss";

    public final static String DATEFORMATE_YYYY_MM_DD_HHMM = "yyyy-MM-dd HH:mm";

    public final static String DATE_FORMAT_YMDHM = "yyyy-MM-dd HH:mm:ss";

    public static final String DATEFORMATE_CN_TIME = "yyyy年MM月dd日 HH:mm:ss";

    public static final String DATEFORMATE_CN_DATE = "yyyy年MM月dd日";
    public static final String DATE_FORMAT_YYMMDD = "yyMMdd";

    public static final String DATE_FORMAT_YMD = "yyyy-M-dd";

    public static final String DATE_FORMAT_Y_M_DHMS = "yyyy/MM/dd HH:mm:ss";

    public static final String DATE_FORMAT_YYYY_MM_DD_HH = "yyyy-MM-dd HH";

    public static final String DATEFORMATE_CN_MONTHDAY = "MM月dd日";

    public static final String DATEFORMATE_CN_YEARMONTH = "yyyy年MM月";
    public static final String DATEFORMATE_CN_MONTHDAYHHSS = "MM月dd日 HH:mm";

    static final DateFormat YYYYMMDDHHMMSS_FORMAT = new SimpleDateFormat(
            DATEFORMATE_YYYYMMDDHHMMSS);

    static final DateFormat YYYYMMDD_FORMAT = new SimpleDateFormat(
            DATEFORMATE_YYYYMMDD);

    static final DateFormat YYYY_MM_DD_HHMMSS_FORMAT = new SimpleDateFormat(
            DATEFORMATE_YYYY_MM_DD_HHMMSS);

    static final DateFormat YYYY_MM_DD_FORMAT = new SimpleDateFormat(
            DATEFORMATE_YYYY_MM_DD);

    static final DateFormat YYYY_MM_FORMAT = new SimpleDateFormat(
            DATEFORMATE_YYYY_MM);

    static final DateFormat CN_TIME_FORMAT = new SimpleDateFormat(
            DATEFORMATE_CN_TIME);

    static final DateFormat CN_DATE_FORMAT = new SimpleDateFormat(
            DATEFORMATE_CN_DATE);

    /**
     * 格式化日期函数 内部使用
     * <p>
     * 根据指定格式对当前日期进行格式化
     *
     * @param date   当前日期
     * @param format 需要转化的格式
     * @return String 转换后的字符串格式日期
     */
    public static String parseDate(Date date, String format) {
        SimpleDateFormat dateformat = new SimpleDateFormat(format);
        return dateformat.format(date);
    }

    public static Date parseDatetoDate(Date date, String format) {
        SimpleDateFormat dateformat = new SimpleDateFormat(format);
        String dateStr = dateformat.format(date);
        return parseDate(dateStr, format);
    }

    /**
     * 格式化日期函数 内部使用
     * <p>
     * 根据指定格式对当前日期进行格式化
     *
     * @param date   当前日期
     * @param format 需要转化的格式
     * @return String 转换后的字符串格式日期
     */
    public static Date parseDateByDate(Date date, String format) {
        SimpleDateFormat dateformat = new SimpleDateFormat(format);
        String dateStr = dateformat.format(date);
        try {
            return dateformat.parse(dateStr);
        } catch (ParseException e) {
            LOGGER.error("日期转换异常，异常信息：", e);
        }
        return null;
    }

    /**
     * 将日期字符串转换成指定格式的日期
     *
     * @param dateStr 日期字符串
     * @param format  需要转化的格式
     * @return
     * @throws ParseException
     */
    public static Date parseDate(String dateStr, String format) {
        SimpleDateFormat dateformat = new SimpleDateFormat(format);
        try {
            return dateformat.parse(dateStr);
        } catch (ParseException e) {
            LOGGER.error("日期转换异常，异常信息：", e);
        }

        return null;
    }

    /**
     * <pre>
     * 日期对象转字符串
     * </pre>
     *
     * @param date
     * @param format
     * @return
     */
    public static String formatToStr(Date date, String format) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(date);
    }

    /**
     * 获取精确到年月日的时间戳
     * @return
     */
    public static long getCurrentTime(){
        String dateStr = timeStamp2Date(System.currentTimeMillis()/1000, DATEFORMATE_YYYY_MM_DD);
        return date2TimeStamp(dateStr, DATEFORMATE_YYYY_MM_DD);
    }

    public static long getCurrentTime(Date date){
        String str = parseDate(date, DATEFORMATE_YYYY_MM_DD);
        return date2TimeStamp(str, DATEFORMATE_YYYY_MM_DD);
    }

    public static long getCurrentTimeMillis(Date date){
        return date.getTime();
    }


    /**
     * 功能描述: <br>
     * 〈返回中文年月日〉
     *
     * @param date
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static String getDateCn(Date date) {
        return CN_DATE_FORMAT.format(date);
    }

    /**
     * get current time
     *
     * @return Timestamp
     */
    public static Timestamp getCurTime() {
        return new Timestamp(Calendar.getInstance().getTime().getTime());
    }

    public static String getTime() {
        Timestamp time = new Timestamp(Calendar.getInstance().getTime()
                .getTime());
        return time.toString();
    }

    public static Date getDate2Time(long s){
        long lt = new Long(s);
        Date date = new Date(lt);
        return date;
    }

    /**
     * 把yyyy-MM-dd格式的字符串转换成Date
     *
     * @param dateStr
     * @return
     */
    public static java.sql.Date getDateOfShortStr(String dateStr) {
        java.sql.Date da = null;
        try {
            da = new java.sql.Date(YYYY_MM_DD_FORMAT.parse(dateStr).getTime());
        } catch (Exception e) {
            LOGGER.error("日期转换异常，异常信息：", e);
        }
        return da;
    }

    /**
     * 把yyyy-MM-dd格式的字符串转换成Date
     *
     * @param dateStr
     * @return
     */
    public static Date getStrToDate(String dateStr) {
        Date dt = null;
        try {
            dt = YYYY_MM_DD_FORMAT.parse(dateStr);
        } catch (ParseException e) {
            LOGGER.error("日期转换异常，异常信息：", e);
        }
        return dt;
    }


    /**
     * 把yyyy-MM格式的字符串转换成Date
     *
     * @param dateStr
     * @return
     */
    public static Date getStrToYMDate(String dateStr){
        try {
            return YYYY_MM_FORMAT.parse(dateStr);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取分区索引
     *
     * @param time 时间（单位：秒）
     * @return
     */
    public static Date getIdxDate(Integer time) {
        if (null == time) {
            return null;
        }
        return getStrToDate(getStrDate(new Date(time * 1000L)));
    }

    /**
     * 毫秒转秒
     *
     * @param millisecond 毫秒
     * @return
     */
    public static Integer millisecondToSecond(Long millisecond) {
        if (null == millisecond) {
            return null;
        }
        Long second = millisecond / 1000;
        return second.intValue();
    }

    /**
     * 秒转毫秒
     *
     * @param second
     * @return
     */
    public static Long secondToMilliSecond(Integer second) {
        if (null == second) {
            return null;
        }
        return second * 1000L;
    }

    /**
     * 把日期转换成 yyyyMMdd格式的字符串
     *
     * @param date
     * @return
     */
    public static String getShortStrDate(Date date) {
        return YYYYMMDD_FORMAT.format(date);
    }

    /**
     * 把日期转换成 yyyyMMddHHmmss格式的字符串
     *
     * @param date
     * @return
     */
    public static String getShortStrDateTime(Date date) {
        return YYYYMMDDHHMMSS_FORMAT.format(date);
    }

    /**
     * 把日期转换成 yyyy年MM月dd日 HH:mm:ss格式的字符串
     *
     * @param date
     * @return
     */
    public static String getCnShortStrDateTime(Date date) {
        return CN_TIME_FORMAT.format(date);
    }

    /**
     * 把日期转换成 yyyy-MM-dd格式的字符串
     *
     * @param date
     * @return
     */
    public static String getStrDate(Date date) {
        return YYYY_MM_DD_FORMAT.format(date);
    }

    /**
     * 把日期转换成 yyyy-MM-dd HH:mm:ss格式的字符串
     *
     * @param date
     * @return
     */
    public static String getStrDateTime(Date date) {
        return YYYY_MM_DD_HHMMSS_FORMAT.format(date);
    }


    /**
     * 把日期转换成 yyyy-MM格式的字符串
     *
     * @param date
     * @return
     */
    public static String getStrDateTimeYyyyMm(Date date) {
        return YYYY_MM_FORMAT.format(date);
    }

    /**
     * 把yyyyMMdd格式字符串转换成 java.sql.Date
     *
     * @param dateStr
     * @return
     */
    public static java.sql.Date getSqlDateByShortStr(String dateStr) {
        java.sql.Date da = null;
        try {
            da = new java.sql.Date(YYYYMMDD_FORMAT.parse(dateStr).getTime());
        } catch (Exception e) {
            LOGGER.error("日期转换异常，异常信息：", e);
        }
        return da;
    }

    /**
     * 把yyyyMMddHHmmss格式字符串转换成 java.sql.Date
     *
     * @param dateStr
     * @return
     */
    public static java.sql.Date getSqlDateTimeByShortStr(String dateStr) {
        java.sql.Date da = null;
        try {
            da = new java.sql.Date(YYYYMMDDHHMMSS_FORMAT.parse(dateStr)
                    .getTime());
        } catch (Exception e) {
            LOGGER.error("日期转换异常，异常信息：", e);
        }
        return da;
    }

    /**
     * 把yyyyMMdd格式字符串转换成 java.util.Date
     *
     * @param
     * @return
     */
    public static Date getUtilDateByShortStr(String datestr) {
        try {
            return YYYYMMDD_FORMAT.parse(datestr);
        } catch (ParseException e) {
            LOGGER.error("日期转换异常，异常信息：", e);
            return null;
        }
    }

    /**
     * 把yyyyMMddHHmmss格式字符串转换成 java.util.Date
     *
     * @param
     * @return
     */
    public static Date getUtilDateTimeByShortStr(String datestr) {
        try {
            return YYYYMMDDHHMMSS_FORMAT.parse(datestr);
        } catch (ParseException e) {
            LOGGER.error("日期转换异常，异常信息：", e);
            return null;
        }
    }

    public static int getDaysofMonth(Date date1) throws Exception {
        Calendar cld = Calendar.getInstance();
        cld.setTime(date1);
        cld.set(5, 1);
        cld.add(2, 1);
        cld.add(6, -1);
        int days = cld.get(5);
        return days;
    }

    public static int getWeeksofMonth(Date date1) throws Exception {
        Calendar cld = Calendar.getInstance();
        cld.setTime(date1);
        cld.set(5, 1);
        cld.add(2, 1);
        cld.add(6, -1);
        int weeks = cld.get(4);
        return weeks;
    }

    public static boolean isCurrentMonth(Date date1) throws Exception {
        Calendar cld = Calendar.getInstance();
        int currentYear = cld.get(1);
        int currentMonth = cld.get(2);
        cld.setTime(date1);
        int year = cld.get(1);
        int month = cld.get(2);
        boolean currentFlag = false;
        if (currentYear == year && currentMonth == month)
            currentFlag = true;
        return currentFlag;
    }

    public static int getFirstDayofWeek(Date date1) throws Exception {
        Calendar cld = Calendar.getInstance();
        cld.setTime(date1);
        cld.set(5, 1);
        int dayOfFirstSunday = cld.get(7) - 1;
        return dayOfFirstSunday;
    }

    public static int getFirstWeekofMonth(Date date1) throws Exception {
        Calendar cld = Calendar.getInstance();
        cld.setTime(date1);
        cld.set(5, 1);
        int week = cld.get(3);
        return week;
    }

    public static Date getFirstDayofMonth(Date date1) throws Exception {
        Calendar cld = Calendar.getInstance();
        cld.setTime(date1);
        cld.set(5, 1);
        return cld.getTime();
    }

    public static Date getLastDayofMonth(Date date1) throws Exception {
        Calendar cld = Calendar.getInstance();
        cld.setTime(date1);
        cld.set(5, 1);
        return cld.getTime();
    }

    public static int getDayofMonth(Date date1) throws Exception {
        Calendar cld = Calendar.getInstance();
        cld.setTime(date1);
        int day = cld.get(5);
        return day;
    }

    public static int getMonth(Date date1) {
        Calendar cld = Calendar.getInstance();
        cld.setTime(date1);
        int month = cld.get(2);
        return month;
    }

    public static int getYear(Date date1) {
        Calendar cld = Calendar.getInstance();
        cld.setTime(date1);
        int year = cld.get(1);
        return year;
    }

    public static int getDayofWeek(Date date1) throws Exception {
        Calendar cld = Calendar.getInstance();
        cld.setTime(date1);
        int dayOfSunday = cld.get(7);
        return dayOfSunday;
    }

    public static int getCurrentDayofMonth() throws Exception {
        Calendar cld = Calendar.getInstance();
        int day = cld.get(5);
        return day;
    }

    public static String getDayOfWeekCn(Date date) {
        if (null == date) {
            return "";
        }

        SimpleDateFormat sdf = new SimpleDateFormat("EEEE");
        String week = sdf.format(date);
        return week;
//
//        Calendar cld = Calendar.getInstance();
//        cld.setTime(date);
//        String dayOfWeekCn = "";
//        switch (cld.get(Calendar.DAY_OF_WEEK)) {
//            case 0:
//                dayOfWeekCn = "周日";
//                break;
//            case 1:
//                dayOfWeekCn = "周一";
//                break;
//            case 2:
//                dayOfWeekCn = "周二";
//                break;
//            case 3:
//                dayOfWeekCn = "周三";
//                break;
//            case 4:
//                dayOfWeekCn = "周四";
//                break;
//            case 5:
//                dayOfWeekCn = "周五";
//                break;
//            case 6:
//                dayOfWeekCn = "周六";
//                break;
//        }
//        return dayOfWeekCn;
    }

    /**
     * 日期相加
     *
     * @param date 日期
     * @param day  天数
     * @return 返回相加后的日期
     */
    public static Date addDate(Date date, int day) {
        Calendar c = Calendar.getInstance();
//        c.set(Calendar.MILLISECOND, 999);
        c.setTimeInMillis(getMillis(date) + ((long) day) * 24 * 3600 * 1000);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        return c.getTime();
    }

    /**
     * 功能描述: <br>
     * 〈增加月份〉
     *
     * @param date  当前日期
     * @param month 月数
     * @return
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static Date addMonth(Date date, int month) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, month);
        return cal.getTime();
    }

    /**
     * date1 - date2 返回相差的天数
     *
     * @param date2 日期
     * @param date1 日期
     * @return
     */
    public static int diffDate(Date date1, Date date2) {
        return (int) ((getMillis(date1) - getMillis(date2)) / (24 * 3600 * 1000));
    }

    /**
     * long类型
     * @param date1
     * @param date2
     * @return
     */
    public static long diffDate(long date1, long date2 ){
        return (date2 - date1) == 0 ? 0 : (date2 - date1)/24/3600;
    }

    public static void main(String[] args) {
//        long day = DateUtil.diffDate(DateUtil.getCurrentTime(parseDate("2023-03-28 23:59:59", DATEFORMATE_YYYY_MM_DD)), getCurrentTime());
//        System.out.println(day);

        String date = timeStamp3Date(1735875218000L,DATEFORMATE_YYYYMMDDHHMMSS);
        System.out.println(date);
        System.out.println(date.substring(0,8));
        System.out.println(date.substring(8));
//        long day = DateUtil.diffDate(DateUtil.getCurrentTime(parseDate("2023-03-28 23:59:59", DATEFORMATE_YYYY_MM_DD)), getCurrentTime());
//        System.out.println(day);

        System.out.println(System.currentTimeMillis());

    }
    /**
     * long类型
     * @param startDate
     * @param endDate
     * @return
     */
    public static long diffDate2(long startDate, long endDate ){
        if ((endDate - startDate) < 0){
            return -1;
        }
        return (endDate - startDate) == 0 ? 0 : (endDate - startDate)/24/3600;
    }

    /**
     * 返回 日期相差的小时数
     *
     * @param date
     * @param date1
     * @return
     */
    public static int diffDateToHour(Date date, Date date1) {
        return (int) ((getMillis(date) - getMillis(date1)) / (1000 * 60 * 60));
    }

    /**
     * 返回毫秒
     *
     * @param date 日期
     * @return 返回毫秒
     */
    public static long getMillis(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.getTimeInMillis();
    }

    /**
     * 返回毫秒
     *
     * @return 返回毫秒
     */
    public static Integer getMillis() {
        Long now = System.currentTimeMillis();
        Long mm = now / 1000;
        return mm.intValue();
    }

    public static String formatDateTime(Timestamp ts) {
        String temp = String.valueOf(ts);
        if (temp != null && temp != "" && temp != "null")
            temp = temp.substring(0, 16);
        else if (temp == "null")
            temp = "";
        return temp;
    }

    /**
     * 获取当前时间Date
     * @return
     */
    public static Date currentDate(){
        return new Date(System.currentTimeMillis());
    }

    /**
     * 时间戳转换成日期格式字符串
     * @param seconds 精确到秒的字符串
     * @param format
     * @return
     */
    public static String timeStamp2Date(Long seconds,String format) {
        if(format == null || format.isEmpty()){
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date(seconds * 1000));
    }

    /**
     * 时间戳转换成日期格式字符串
     * @param seconds 精确到秒的字符串
     * @param format
     * @return
     */
    public static String timeStamp3Date(Long seconds,String format) {
        if(format == null || format.isEmpty()){
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date(seconds));
    }
    /**
     * 日期格式字符串转换成时间戳
     * @param date_str 字符串日期
     * @param format 如：yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static Long date2TimeStamp(String date_str,String format){
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.parse(date_str).getTime()/1000;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 判断是否同年同月
     *
     * @param t1 日期1
     * @param t2 日期2
     * @return
     */
    public static boolean isSameMonth(Timestamp t1, Timestamp t2) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(t1);
        int year1 = cal.get(Calendar.YEAR);
        int month1 = cal.get(Calendar.MONTH);
        cal.setTime(t2);
        int year2 = cal.get(Calendar.YEAR);
        int month2 = cal.get(Calendar.MONTH);
        if (year1 == year2 && month1 == month2)
            return true;
        return false;
    }

    /**
     * 判断是否同年同月同日
     *
     * @param date1
     * @param date2
     * @return
     */
    public static boolean isSameDay(Date date1, Date date2){
        if (null == date1||null == date2){
            return false;
        }
        return YYYY_MM_DD_FORMAT.format(date1).equals(YYYY_MM_DD_FORMAT.format(date2));
    }

    /**
     * 把日期格式化成中文格式
     */
    public static String parseCnDate(Date date) {
        return CN_TIME_FORMAT.format(date);
    }

    /**
     * 获取一年后的日期
     *
     * @param date
     * @return
     */
    public static Timestamp getNextYear(Date date) {
        Calendar thisDay = Calendar.getInstance();
        thisDay.add(Calendar.YEAR, 1);
        date = thisDay.getTime();
        String time = YYYY_MM_DD_HHMMSS_FORMAT.format(date);
        Timestamp ts = Timestamp.valueOf(time);
        return ts;
    }

    /**
     * 获取一周内第几天的日期
     *
     * @param date       需要获取的日期
     * @param dateOfWeek 第几天
     * @return
     */
    public static Date getDateOfWeek(Date date, Integer dateOfWeek) {
        if (null == date) {
            return date;
        }
        if (null == dateOfWeek) {
            dateOfWeek = 0;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int d = 2 - cal.get(Calendar.DAY_OF_WEEK);
        if (cal.get(Calendar.DAY_OF_WEEK) == 1) {
            d = -6;
        }
        cal.add(Calendar.DAY_OF_WEEK, d + dateOfWeek);
        return cal.getTime();
    }

    /**
     * 获取日期所在周的第一天
     *
     * @param date 需要获取的日期
     * @return
     */
    public static Date getFirstDateOfWeek(Date date) {
        return getDateOfWeek(date, 0);
    }

    /**
     * 获取日期所在周的最后一天
     *
     * @param date 需要获取的日期
     * @return
     */
    public static Date getLastDateOfWeek(Date date) {
        return getDateOfWeek(date, 7);
    }

    /**
     * 获取某年某月的某一天
     * @param year
     * @param month
     * @param day
     */
    public static Date getMonthDay(int year, int month, int day){
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month);
        calendar.set(Calendar.DAY_OF_MONTH, day);  //年月日
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date date = calendar.getTime();
        return date;
    }


    /**
     * <pre>
     *  得到日期的小时(24小时制)
     * </pre>
     *
     * @param date
     * @return
     */
    public static int getHour(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.HOUR_OF_DAY);
    }


    /**
     * 两个时间之间的天数
     *
     * @return
     */
    public static int getDayNum(Date start, Date end) {
        return Integer.parseInt(String.valueOf((end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000))) + 1;
    }

    /**
     * <pre>
     * 字符串转日期对象
     * </pre>
     *
     * @param dateStr
     * @param format
     * @return
     */
    public static Date formatToDate(String dateStr, String format) {
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        ParsePosition pos = new ParsePosition(0);// 从0开始解析
        Date strtodate = formatter.parse(dateStr, pos);
        return strtodate;
    }

    /**
     * 返回时间字符串列表 如["18:00","19:00"]
     */
    public static List<String> timeStrList() {
        Date now = new Date();
        Integer hour = getHour(now);
        List<String> timeStrList = new LinkedList<>();
        if (hour < 9) {
            hour = 8;
            now = DateUtil.setHour(now, 8);
        }
        for (int i = 1; i <= (17 - hour); i++) {
            String timeStr = DateUtil.formatToStr(DateUtil.addHour(now, i), "HH:00");
            timeStrList.add(timeStr);
        }
        return timeStrList;
    }

    /**
     * <pre>
     *  设置时间
     * </pre>
     *
     * @param date
     * @return
     */
    public static Date setHour(Date date, Integer hour) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, hour);
        return c.getTime();
    }
    /**
     * <pre>
     * 增加num小时后的时间
     * </pre>
     *
     * @param date
     * @return
     */
    public static Date addHour(Date date, long num) {
        long time = date.getTime() + num * 3600 * 1000;
        return new Date(time);
    }



    /**
     * 获取某天的结束时间
     *
     * @return
     */
    public static Date getSomeDayEndTimes(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }


    /**
     * 获取某天的开始时间
     *
     * @return
     */
    public static Date getSomeDayStartTimes(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 00);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 000);
        return calendar.getTime();
    }
    /**
     * <pre>
     * 增加年份
     * </pre>
     *
     * @param date  原始日期
     * @param years 增加的年数
     * @return 增加days天后的日期
     */
    public static Date addYear(Date date, int years) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.YEAR, years);
        return c.getTime();
    }

    /**
     * 获取当前月份第一天
     * @return
     */
    public static Date getCurrentMonthFirstDay(){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH,1);
        calendar.add(Calendar.MONTH,0);
        return parseDateByDate(new java.sql.Date(calendar.getTimeInMillis()),DateUtil.DATEFORMATE_YYYY_MM_DD);
    }

    public static long getTimeStampTime(String dateTime){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime localDateTime = LocalDateTime.parse(dateTime, formatter);
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.systemDefault());
        return zonedDateTime.toInstant().toEpochMilli();
    }

}
