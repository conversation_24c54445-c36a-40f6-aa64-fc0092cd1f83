package com.jnby.mallasset.remote.xiexin.entity.vip;

import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Root;

@Data
@Root(name = "crmVipLableMX",strict = false)
public class XiexinVipTagItemDetail {

    @Element(name = "jlbh" , required = false)
    private int jlbh;

    @Element(name = "labellx" , required = false)
    private int labellx;

    @Element(name = "gzmc" , required = false)
    private String gzmc;

    @Element(name = "labellbid" , required = false)
    private int labellbid;

    @Element(name = "bqmc" , required = false)
    private String bqmc;

    @Element(name = "bj_smzq" , required = false)
    private int bj_smzq;

    @Element(name = "hyid" , required = false)
    private int hyid;
}
