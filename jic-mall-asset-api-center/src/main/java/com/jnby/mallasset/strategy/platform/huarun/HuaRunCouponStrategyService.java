package com.jnby.mallasset.strategy.platform.huarun;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jnby.mallasset.api.dto.coupon.CouponInfoRespDto;
import com.jnby.mallasset.config.exception.MallException;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.convert.CouponConvertor;
import com.jnby.mallasset.enums.OperationStatusTypeEnum;
import com.jnby.mallasset.module.model.CashierMallAssetLog;
import com.jnby.mallasset.remote.huarun.IHuaRunCouponRemoteHttpApi;
import com.jnby.mallasset.remote.huarun.entity.BaseHuaRunResp;
import com.jnby.mallasset.remote.huarun.entity.CarCouponReqEntity;
import com.jnby.mallasset.remote.huarun.entity.CarCouponRespEntity;
import com.jnby.mallasset.remote.huarun.entity.CarVoucherRule;
import com.jnby.mallasset.remote.mallcoo.IMallCooRemoteHttpApi;
import com.jnby.mallasset.remote.mallcoo.MallCooUtils;
import com.jnby.mallasset.remote.mallcoo.entity.*;
import com.jnby.mallasset.strategy.category.AbstractCouponService;
import com.jnby.mallasset.strategy.context.CouponContext;
import com.jnby.mallasset.strategy.context.UserContext;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 猫酷平台优惠券策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.HUA_RUN, category = PlatformCategoryTypeEnum.COUPON)
@AllArgsConstructor
@Slf4j
public class HuaRunCouponStrategyService extends AbstractCouponService {

    private HuaRunLoginService huaRunLoginService;
    private IHuaRunCouponRemoteHttpApi huaRunCouponRemoteHttpApi;

    @Override
    public void checkUser(UserContext user) {

    }

    @Override
    public List<CouponInfoRespDto> doList(UserContext user) {
        return null;
    }

    @Override
    public void preCheckUseCoupon(UserContext user, CouponContext req) {

    }

    @Override
    public void doUseCoupon(UserContext user, CouponContext req) {

    }

    @Override
    public void preCheckReturnCoupon(UserContext user, CouponContext req) {

    }

    @Override
    public void doReturnCoupon(UserContext user, CouponContext req) {

    }

    @Override
    public void preCheckSendCoupon(UserContext user, CouponContext req) {

    }

    @Override
    public void doSendCoupon(UserContext user, CouponContext context) {
        String storeId = user.getStoreConfig().getBjStoreId();
        String tel = user.getPhone();
        String voucherTemplate = user.getMallConfig().getParkingCouponTemplateNo();
        try {
            String userToken = huaRunLoginService.getUserToken(storeId);
            CarCouponReqEntity reqEntity = new CarCouponReqEntity();
            reqEntity.setMemberTel(tel);
            reqEntity.setVoucherType(2);
            reqEntity.setVoucherTemplate(voucherTemplate);
            CarVoucherRule voucherRule = new CarVoucherRule();
            voucherRule.setVoucherCount(2);
            voucherRule.setVoucherReceive(false);
            reqEntity.setVoucherRule(voucherRule);

            log.info("华润发放停车券调用远程接口入参:{}", JSON.toJSONString(reqEntity));
            Map<String, String> header = new HashMap<>();
            header.put("Authorization","Bearer " + userToken);

            Response<BaseHuaRunResp<CarCouponRespEntity>> execute = huaRunCouponRemoteHttpApi.sendCarVoucher(header, reqEntity).execute();
            boolean successful = execute.isSuccessful();
            if (!successful) {
                throw new MallException(SystemErrorEnum.COUPON_SEND_REQUEST_ERROR);
            }
            BaseHuaRunResp<CarCouponRespEntity> body = execute.body();
            log.info("华润发放停车券调用远程接口响应：{}", JSON.toJSONString(body));
            if (!(body.getResult() == 1 || body.getResult() == 104)) {
                throw new MallException(body.getMsg());
            }
            CarCouponRespEntity data = body.getQuery();
            if (data == null) {
                throw new MallException(SystemErrorEnum.COUPON_SEND_RESPONSE_ERROR);
            }
            List<String> voucherList = new ArrayList<>();
            // 券码
            data.getVouchers().forEach(vouchers -> voucherList.add(vouchers.getVoucherNum()));
            context.setCouponNoList(voucherList);

        } catch (IOException e) {
            log.error("华润沈万发放停车券 超时", e);
            throw new MallException(SystemErrorEnum.COUPON_SEND_TIMEOUT_ERROR);
        } catch (MallException e) {
            log.error("华润沈万发放停车券 已知异常", e);
            throw e;
        } catch (Exception e) {
            log.error("华润沈万发放停车券 异常", e);
            throw new MallException(SystemErrorEnum.COUPON_SEND_UNKNOWN_ERROR);
        }

    }
}
