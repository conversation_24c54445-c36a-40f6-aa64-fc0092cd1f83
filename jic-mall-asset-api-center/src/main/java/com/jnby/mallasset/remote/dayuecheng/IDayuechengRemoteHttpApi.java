package com.jnby.mallasset.remote.dayuecheng;


import com.jnby.mallasset.remote.dayuecheng.entity.BaseDycResp;
import com.jnby.mallasset.remote.dayuecheng.entity.DycMemberQueryRespEntity;
import com.jnby.mallasset.remote.mallcoo.entity.*;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.Map;


/**
 * 大悦城
 */
public interface IDayuechengRemoteHttpApi {

    /**
     * 功能描述:
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.dayuecheng.entity.BaseDycResp<com.jnby.mallasset.remote.dayuecheng.entity.DycMemberQueryRespEntity>>
     * <AUTHOR>
     * @date 2025/4/2 15:36
     */
    @POST("/User/AdvancedInfo/v1/Get/ByMobile/")
    Call<BaseMallCooResp<MemberQueryRespEntity>> getUnionMemberInfo(@HeaderMap Map<String, String> headers, @Body MemberQueryReqEntity req);


    /**
     * 功能描述:
     * 使用场景:
     *
     * @param headers
     * @param req
     * @return retrofit2.Call<com.jnby.mallasset.remote.dayuecheng.entity.BaseDycResp<com.jnby.mallasset.remote.dayuecheng.entity.DycMemberQueryRespEntity>>
     * <AUTHOR>
     * @date 2025/4/2 15:48
     */
    @POST("/User/MallCard/v1/Open/ByMobile/")
    Call<BaseMallCooResp<MemberCreateCardRespEntity>> registerMemberInfo(@HeaderMap Map<String, String> headers, @Body MemberCreateCardReqEntity req);


    /**
     * 订单加积分
     */
    @POST("/User/Score/V2/Consume/Plus/ByMobile/")
    Call<BaseMallCooResp<PointsPlusByOrderRespEntity>> pointsPlusByOrder(@HeaderMap Map<String, String> headers, @Body PointsPlusByOrderReqEntity req);

    /**
     * 退款减积分
     */
    @POST("/Return/v1/Execute/")
    Call<BaseMallCooResp<PointsDeductionByRefundRespEntity>> pointsDeductionByRefund(@HeaderMap Map<String, String> headers, @Body PointsDeductionByRefundReqEntity req);

    @POST("/User/AdvancedInfo/v1/Get/ByMobile/")
    Call<BaseMallCooResp<MemberCreateCardRespEntity>> getMember(@HeaderMap Map<String, String> headers, @Body MemberCreateCardReqEntity req);

    @POST("/User/AdvancedInfo/v1/Get/ByMobile/")
    Call<BaseMallCooResp<MemberQueryRespEntity>> getMember2(@HeaderMap Map<String, String> headers, @Body MemberQueryReqEntity req);

    /**
     * 开卡
     */
    @POST("/User/MallCard/v1/Open/ByMobile/")
    Call<BaseMallCooResp<MemberCreateCardRespEntity>> createCard(@HeaderMap Map<String, String> headers, @Body MemberCreateCardReqEntity req);
}
