package com.jnby.mallasset.strategy.platform.kkmall;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.dto.req.order.PayItem;
import com.jnby.mallasset.dto.req.order.ProductItem;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.remote.dingding.IDingDingRemoteHttpApi;
import com.jnby.mallasset.remote.dingding.entity.DingDingMsg;
import com.jnby.mallasset.remote.dingding.entity.DingDingResponse;
import com.jnby.mallasset.remote.kkmall.IKkMallRemoteHttpApi;
import com.jnby.mallasset.remote.kkmall.entity.*;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * 凯德订单平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.KK_MALL, category = PlatformCategoryTypeEnum.ORDER)
@Slf4j
@AllArgsConstructor
public class KkMallOrderStrategyService extends AbstractOrderService {

    private CashierMallOrderLogMapper cashierMallOrderLogMapper;
    private IKkMallRemoteHttpApi kkMallRemoteHttpApi;
    private KkMallTokenService kkMallTokenService;
    private IDingDingRemoteHttpApi dingDingRemoteHttpApi;

    @Override
    public ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef mallAssetStoreRef) {
        log.info("KKMALL订单同步输入参数：{}", JSON.toJSONString(orderConsumeReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderConsumeReq, mallAssetStoreRef);

            Map<String, String> header = new HashMap<>();
            String token = kkMallTokenService.getUserToken(orderConsumeReq.getStoreId());
            log.info("KKMALL订单同步获取token：{}", token);
            header.put("X-Access-Token",token);
            // 组装请求对象
            KkMallOrderReqEntity req = initKkMallOrderReqEntity(orderConsumeReq, mallAssetStoreRef);
            log.info("KKMALL订单同步调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseKkMallResp<KkMallOrderRespEntity>> execute = kkMallRemoteHttpApi.orderSales(header,req).execute();
            BaseKkMallResp<KkMallOrderRespEntity> body = execute.body();
            commonResponse.setMsg(body.getMessage());
            commonResponse.setCode(body.getCode());
            if(body.getCode() == 0){
                String ordNum = orderConsumeReq.getOrdNum();
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                KkMallOrderRespEntity kkMallOrderRespEntity = body.getResult();
                kkMallOrderRespEntity.setOrdNum(ordNum);
                kkMallOrderRespEntity.setExchangeId(kkMallOrderRespEntity.getTransactionId());
                kkMallOrderRespEntity.setExchangeNo(orderConsumeReq.getOrdNum());
                commonResponse.setData(kkMallOrderRespEntity);
                // 更新返回的参数
                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLog.setUpdateExchangeId(kkMallOrderRespEntity.getTransactionId());
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);

                // 上传结算系统
                uploadOrderToSystem(orderConsumeReq, mallAssetStoreRef, ordNum,cashierMallOrderLog);
            }else {
                commonResponse.setData(body.getResult());
            }
            log.info("KKMALL订单同步返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} KKMALL订单同步失败",orderConsumeReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    private void uploadOrderToSystem(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef mallAssetStoreRef, String ordNum,CashierMallOrderLog cashierMallOrderLog) throws IOException {
        KkMallUploadOrderInfo orderInfo = new KkMallUploadOrderInfo();
        String integralSkuCode = mallAssetStoreRef.getIntegralSkuCode();
        String[] items = integralSkuCode.split(",");
        String[] tillIds = items[1].split("-");
        String tillId = tillIds[1];
        String dateTime = DateUtil.timeStamp3Date(orderConsumeReq.getOrdFinishTime(),null);
        String day = dateTime.substring(0,10);
        orderInfo.setApiKey(mallAssetStoreRef.getPlatformAppId());
        orderInfo.setSignature("");
        String docKey = day.concat(".").concat(mallAssetStoreRef.getPlatformMallStoreId()).concat(".").concat(tillId).concat(".").concat(ordNum);
        orderInfo.setDocKey(docKey);
        TransHeader transHeader = new TransHeader();
        transHeader.setTxDate(day);
        transHeader.setLedgerDatetime(dateTime);
        transHeader.setStoreCode(mallAssetStoreRef.getPlatformMallStoreId());
        transHeader.setTillId(tillId);
        transHeader.setDocNo(ordNum);
        transHeader.setTxAttrib("");
        transHeader.setVoidDocNo("");
        orderInfo.setTransHeader(transHeader);

        List<ProductItem> productItemList = orderConsumeReq.getProductItemList();
        SalesTotal salesTotal = new SalesTotal();
        String[] cashiers = items[0].split("-");
        String cashier = cashiers[1];
        salesTotal.setCashier(cashier);
        salesTotal.setVipCode("");
        salesTotal.setNetQty(orderConsumeReq.getTotQty());
        salesTotal.setNetAmount(orderConsumeReq.getOrdAmount());
        salesTotal.setExtendParameter("");
        salesTotal.setCalculateVipBonus("");
        orderInfo.setSalesTotal(salesTotal);
        // 商品
        List<SalesItem> salesItem = new ArrayList<>();
        String[] itemOrgIds = items[2].split("-");
        String itemOrgId = itemOrgIds[1];
        String[] itemCodes = items[3].split("-");
        String itemCode = itemCodes[1];
        for(int i=0; i< productItemList.size();i++){
            ProductItem productItem = productItemList.get(i);
            SalesItem item = new SalesItem();
            item.setSalesLineNumber(i+1);
            List<String> salesman = new ArrayList<>();
            salesman.add(mallAssetStoreRef.getPlatformMallStoreId());
            item.setSalesman(salesman);
            item.setItemCode(itemCode);
            item.setItemOrgId(itemOrgId);
            item.setItemLotNum("*");
            item.setSerialNumber("");
            item.setInventoryType(0);
            item.setQty(1);
            item.setItemDiscountLess(0);
            item.setTotalDiscountLess(0);
            item.setNetAmount(productItem.getItemPrice().multiply(new BigDecimal(productItem.getItemQty())));
            item.setSalesItemRemark("");
            item.setExtendParameter("");
            salesItem.add(item);
        }
        orderInfo.setSalesItem(salesItem);
        // 付款
        List<PayItem> payItemList = orderConsumeReq.getPayItemList();
        List<SalesTender> salesTender = new ArrayList<>();
        payItemList.forEach(payItem -> {
            if(payItem.getId() == 62 || payItem.getId() == 231){
                SalesTender salesTender1 = new SalesTender();
                salesTender1.setBaseCurrencyCode("RMB");
                salesTender1.setTenderCode("CH");
                salesTender1.setPayAmount(payItem.getPayamount());
                salesTender1.setBaseAmount(payItem.getPayamount());
                salesTender1.setExcessAmount(BigDecimal.ZERO);
                salesTender1.setExtendParameter("");
                salesTender.add(salesTender1);
            }
        });
        orderInfo.setSalesTender(salesTender);
        log.info("KKMALL订单同步上传结算系统传输参数：{}", JSON.toJSONString(orderInfo));
        Response<KkMallUploadOrderResp> response = kkMallRemoteHttpApi.uploadOrderInfo(orderInfo).execute();
        KkMallUploadOrderResp body = response.body();
        if(body.getErrorCode() != 0){
            pushMsg(orderInfo, body);
        }
        cashierMallOrderLog.setRemark("订单同步上传结算系统返回参数：" + body.getErrorMessage());
        cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
        log.info("KKMALL订单同步上传结算系统返回参数：{}", JSON.toJSONString(body));
    }

    private void pushMsg(KkMallUploadOrderInfo orderInfo, KkMallUploadOrderResp body) throws IOException {
        String msg = "数据推送异常：\n" + "功能：KKMALL同步结算系统数据" + "\n请求参数: \n" + JSON.toJSON(orderInfo) + "\n请求结果: \n" + JSON.toJSON(body);
        DingDingMsg dingDingMsg = new DingDingMsg();
        Map<String,Object> param = new HashMap<>();
        param.put("content",msg);
        dingDingMsg.setText(param);
        dingDingMsg.setMsgtype("text");
        Response<DingDingResponse> execute = dingDingRemoteHttpApi.pushMsg(dingDingMsg).execute();
        log.info("推送消息返回结果：{}",execute.body());
    }

    private KkMallOrderReqEntity initKkMallOrderReqEntity(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef mallAssetStoreRef) {
        KkMallOrderReqEntity req = new KkMallOrderReqEntity();
        req.setMallId(mallAssetStoreRef.getPlatformMallId());
        req.setStoreCode(mallAssetStoreRef.getPlatformMallStoreId());
        req.setSource(1);
        req.setTransTime(DateUtil.timeStamp3Date(orderConsumeReq.getOrdFinishTime(),null));
        req.setTransAmount(orderConsumeReq.getOrdAmount());
        req.setReceiptNo(orderConsumeReq.getOrdNum());
        req.setCustomerId(orderConsumeReq.getOuterMemberId());
        List<ProductItems> items = new ArrayList<>();
        List<ProductItem> productItemList = orderConsumeReq.getProductItemList();
        if(CollectionUtil.isNotEmpty(productItemList)){
            productItemList.forEach(productItem -> {
                ProductItems productItems = new ProductItems();
                productItems.setAmount(productItem.getItemPrice());
                productItems.setQuantity(productItem.getItemQty());
                productItems.setCode(productItem.getItemNo());
                productItems.setCanGetPoint(0);
                items.add(productItems);
            });
        }
        req.setItems(items);
        return req;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeNo(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setBjStoreId(orderConsumeReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(1);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult pushRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef assetStoreRef) {
        log.info("KKMALL订单退货退款输入参数：{}", JSON.toJSONString(orderRefundReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderRefundReq, assetStoreRef);

            String token = kkMallTokenService.getUserToken(orderRefundReq.getStoreId());
            Map<String, String> header = new HashMap<>();
            log.info("KKMALL订单退货退款获取token：{}", token);
            header.put("X-Access-Token",token);
            KkMallOrderRefundReqEntity req = new KkMallOrderRefundReqEntity();
            req.setSourceReceiptNo(orderRefundReq.getOrdNum());
            req.setReturnReceiptNo(orderRefundReq.getRefundNo());
            req.setTransTime(DateUtil.timeStamp3Date(orderRefundReq.getOrdFinishTime(),null));
            req.setSource(1);
            req.setReturnTime(DateUtil.getStrDateTime(new Date()));
            req.setReturnAmount(orderRefundReq.getRefundAmount());
            log.info("KKMALL订单退货退款调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseKkMallResp<KkMallOrderRefundRespEntity>> execute = kkMallRemoteHttpApi.orderReturn(header,req).execute();
            BaseKkMallResp<KkMallOrderRefundRespEntity> body = execute.body();
            commonResponse.setMsg(body.getMessage());
            commonResponse.setCode(body.getCode());
            if(body.getCode() == 0){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                KkMallOrderRefundRespEntity refundRespEntity = body.getResult();
                KkMallOrderRespEntity kkMallOrderRespEntity = new KkMallOrderRespEntity();
                kkMallOrderRespEntity.setOrdNum(orderRefundReq.getOrdNum());
                kkMallOrderRespEntity.setExchangeId(refundRespEntity.getId());
                kkMallOrderRespEntity.setExchangeNo(orderRefundReq.getRefundNo());
                commonResponse.setData(kkMallOrderRespEntity);
                // 更新返回的参数
                cashierMallOrderLog.setIsExecute("Y");
                cashierMallOrderLog.setUpdateExchangeId(refundRespEntity.getId());
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);

                // 上传结算系统
                uploadRefundOrderToSystem(orderRefundReq, assetStoreRef,cashierMallOrderLog);
            }else {
                commonResponse.setData(body.getResult());
            }
            log.info("KKMALL订单退货退款返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} KKMALL订单退货退款失败",orderRefundReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    private void uploadRefundOrderToSystem(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef,CashierMallOrderLog cashierMallOrderLog) throws IOException {
        String refundNo = orderRefundReq.getRefundNo();
        KkMallUploadOrderInfo orderInfo = new KkMallUploadOrderInfo();
        String integralSkuCode = assetStoreRef.getIntegralSkuCode();
        String[] items = integralSkuCode.split(",");
        String[] tillIds = items[1].split("-");
        String tillId = tillIds[1];
        String dateTime = DateUtil.timeStamp3Date(orderRefundReq.getRefundTime(),null);
        String day = dateTime.substring(0,10);

        orderInfo.setApiKey(assetStoreRef.getPlatformAppId());
        orderInfo.setSignature("");
        String docKey = day.concat(".").concat(assetStoreRef.getPlatformMallStoreId()).concat(".").concat(tillId).concat(".").concat(refundNo);
        orderInfo.setDocKey(docKey);
        TransHeader transHeader = new TransHeader();
        transHeader.setTxDate(day);
        transHeader.setLedgerDatetime(dateTime);
        transHeader.setStoreCode(assetStoreRef.getPlatformMallStoreId());
        transHeader.setTillId(tillId);
        transHeader.setDocNo(refundNo);
        transHeader.setTxAttrib("");
        transHeader.setVoidDocNo("");
        orderInfo.setTransHeader(transHeader);

        List<ProductItem> productItemList = orderRefundReq.getProductItemList();
        SalesTotal salesTotal = new SalesTotal();
        String[] cashiers = items[0].split("-");
        String cashier = cashiers[1];
        salesTotal.setCashier(cashier);
        salesTotal.setVipCode("");
        salesTotal.setNetQty(-productItemList.size());
        salesTotal.setNetAmount(orderRefundReq.getRefundAmount().abs().multiply(new BigDecimal(-1)));
        salesTotal.setExtendParameter("");
        salesTotal.setCalculateVipBonus("");
        orderInfo.setSalesTotal(salesTotal);
        // 商品
        List<SalesItem> salesItem = new ArrayList<>();
        String[] itemOrgIds = items[2].split("-");
        String itemOrgId = itemOrgIds[1];
        String[] itemCodes = items[3].split("-");
        String itemCode = itemCodes[1];
        for(int i=0;i < productItemList.size(); i++){
            ProductItem productItem = productItemList.get(i);
            SalesItem item = new SalesItem();
            item.setSalesLineNumber(i+1);
            List<String> salesman = new ArrayList<>();
            salesman.add(assetStoreRef.getPlatformMallStoreId());
            item.setSalesman(salesman);
            item.setItemCode(itemCode);
            item.setItemOrgId(itemOrgId);
            item.setItemLotNum("*");
            item.setSerialNumber("");
            item.setInventoryType(0);
            item.setQty(productItem.getItemQty());
            item.setItemDiscountLess(0);
            item.setTotalDiscountLess(0);
            item.setNetAmount(productItem.getItemPrice().abs().multiply(new BigDecimal(-1)));
            item.setSalesItemRemark("");
            item.setExtendParameter("");
            salesItem.add(item);
        }
        orderInfo.setSalesItem(salesItem);
        // 付款
        List<PayItem> payItemList = orderRefundReq.getPayItemList();
        List<SalesTender> salesTender = new ArrayList<>();
        payItemList.forEach(payItem -> {
            if(payItem.getId() == 62 || payItem.getId() == 231){
                SalesTender salesTender1 = new SalesTender();
                salesTender1.setBaseCurrencyCode("RMB");
                salesTender1.setTenderCode("CH");
                salesTender1.setPayAmount(payItem.getPayamount());
                salesTender1.setBaseAmount(payItem.getPayamount());
                salesTender1.setExcessAmount(BigDecimal.ZERO);
                salesTender1.setExtendParameter("");
                salesTender.add(salesTender1);
            }
        });
        orderInfo.setSalesTender(salesTender);
        log.info("KKMALL订单退货上传结算系统传输参数：{}", JSON.toJSONString(orderInfo));
        Response<KkMallUploadOrderResp> response = kkMallRemoteHttpApi.uploadOrderInfo(orderInfo).execute();
        KkMallUploadOrderResp resp = response.body();
        if(resp.getErrorCode() != 0){
            pushMsg(orderInfo, resp);
        }
        cashierMallOrderLog.setRemark("订单售后上传结算系统返回参数：" + resp.getErrorMessage());
        cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
        log.info("KKMALL订单退货上传结算系统返回参数：{}", JSON.toJSONString(resp));
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef assetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderRefundReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setExchangeNo(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setBjStoreId(orderRefundReq.getStoreId());
        cashierMallOrderLog.setMallId(assetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(assetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(assetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(2);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult prepareRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("KKMALL订单退货校验输入参数：{}", JSON.toJSONString(orderRefundReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            String token = kkMallTokenService.getUserToken(orderRefundReq.getStoreId());
            Map<String, String> header = new HashMap<>();
            log.info("KKMALL订单退货校验获取token：{}", token);
            header.put("X-Access-Token",token);

            KkMallOrderRefundReqEntity req = new KkMallOrderRefundReqEntity();
            req.setSourceReceiptNo(orderRefundReq.getOrdNum());
            req.setTransTime(DateUtil.timeStamp3Date(orderRefundReq.getOrdFinishTime(),null));
            req.setReturnAmount(orderRefundReq.getRefundAmount());
            log.info("KKMALL订单退货校验调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseKkMallResp<KkMallOrderRefundRespEntity>> execute = kkMallRemoteHttpApi.orderReturnCheck(header,req).execute();
            BaseKkMallResp<KkMallOrderRefundRespEntity> body = execute.body();
            commonResponse.setMsg(body.getMessage());
            commonResponse.setCode(body.getCode());
            if(body.getCode() == 0){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                KkMallOrderRefundRespEntity refundRespEntity = body.getResult();
                KkMallOrderRespEntity kkMallOrderRespEntity = new KkMallOrderRespEntity();
                kkMallOrderRespEntity.setOrdNum(orderRefundReq.getOrdNum());
                kkMallOrderRespEntity.setExchangeId(refundRespEntity.getId());
                kkMallOrderRespEntity.setExchangeNo(orderRefundReq.getRefundNo());
                commonResponse.setData(kkMallOrderRespEntity);
            }else {
                commonResponse.setData(body.getResult());
            }
            log.info("KKMALL订单退货校验返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} KKMALL订单退货校验失败",orderRefundReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    @Override
    public ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

}
