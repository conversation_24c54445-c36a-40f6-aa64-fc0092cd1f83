package com.jnby.mallasset.remote.huarun;

import com.alibaba.fastjson.JSON;
import com.jnby.mallasset.config.retrofit.RetrofitConfig;
import com.jnby.mallasset.remote.huarun.entity.BaseHuaRunResp;
import com.jnby.mallasset.remote.huarun.entity.UserLoginReqEntity;
import com.jnby.mallasset.remote.huarun.entity.UserLoginRespEntity;
import com.jnby.mallasset.remote.mallcoo.IMallCooRemoteHttpApi;
import com.jnby.mallasset.remote.mallcoo.entity.*;
import com.jnby.mallasset.util.DateUtil;
import retrofit2.Response;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

public class HuaRunUtils {

    public static Map<String, String> getHeaders(String appId, String publicKey, String privateKey, String json) throws NoSuchAlgorithmException {
        String timeStamp = DateUtil.getShortStrDateTime(new Date());
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put("AppID", appId);
        headers.put("TimeStamp", timeStamp);
        headers.put("PublicKey", publicKey);

        String encryptString = new StringBuilder()
                .append("{publicKey:").append(publicKey)
                .append(",timeStamp:").append(timeStamp).append(",data:").append(json)
                .append(",privateKey:").append(privateKey)
                .append("}").toString();
        String str = getMD5(encryptString);
        headers.put("Sign", str);
        return headers;
    }

    public static void main(String[] args) {

        UserLoginReqEntity req = new UserLoginReqEntity();
        req.setUserName("jnby");
        req.setUserPassWord("4CB0438E24882CB3BD0EA8BEBA115228");
        try {
            IHuaRunRemoteHttpApi api = RetrofitConfig.getMainApi("https://exchangeapi.lncrland.cn", IHuaRunRemoteHttpApi.class);
            Response<BaseHuaRunResp<UserLoginRespEntity>> execute = api.userLogin(req).execute();
            BaseHuaRunResp<UserLoginRespEntity> body = execute.body();
            System.out.println(body.isSuccess());
            System.out.println(JSON.toJSONString(body.getQuery().getUserToken()));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static String getMD5(String signStr) throws NoSuchAlgorithmException {
        MessageDigest md5Instance = MessageDigest.getInstance("MD5");
        md5Instance.update(signStr.getBytes(StandardCharsets.UTF_8));
        byte[] source = md5Instance.digest();

        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        int j = source.length;
        char[] str = new char[j * 2];
        int k = 0;
        for (byte byte0 : source) {
            str[k++] = hexDigits[byte0 >>> 4 & 0xf];
            str[k++] = hexDigits[byte0 & 0xf];
        }

        return new String(str).substring(8, 24);
    }
}
