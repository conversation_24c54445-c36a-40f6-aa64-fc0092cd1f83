package com.jnby.mallasset.strategy.factory;

import java.util.stream.Stream;

/**
 * 平台类目枚举
 * */
public enum PlatformCategoryTypeEnum {

    MEMBER(1, "会员"),
    POINTS(2, "积分"),
    COUPON(3, "券"),
    ORDER(4, "订单"),
    AUTH(5, "鉴权"),
    ;
    /**
     * 商场编号
     */
    private final Integer code;

    /**
     * 商场名称
     */
    private final String name;

    PlatformCategoryTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 通过code获取对于枚举对象
     */
    public static PlatformCategoryTypeEnum getEnumByCode(Integer code) {
        return Stream.of(PlatformCategoryTypeEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("商场不存在,请添加配置"));
    }
}
