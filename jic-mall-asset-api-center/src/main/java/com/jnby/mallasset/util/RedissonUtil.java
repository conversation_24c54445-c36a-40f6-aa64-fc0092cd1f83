package com.jnby.mallasset.util;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RGeo;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date:2023/2/15 19:43
 */
@Component
@Slf4j
public class RedissonUtil {
    @Resource
    private RedissonClient redissonClient;
    /**
     *  WAIT_TIME等待加锁时间 为-1 获取不到锁直接获取锁失败返回，为其他值等待时间进行加锁
     *
     *  LEASE_TIME加锁时间 不为-1 不启动看门狗机制， 如果需要看门狗机制进行延时设置为1即可
     */

    private static final int DEFAULT_INT=-1;

    // 等待时间
    private static final int WAIT_TIME = -1;

    // 离开时间
    private static final int LEASE_TIME = -1;

    /**
     * 分布式锁 默认前缀，便于清除
     */
    private static final String REDISSON_KEY_PRE="redisson:";


    /**
     * 获取锁实例
     * @param lockKey
     * @return
     */
    public RLock getLockInstance(String lockKey){
        lockKey= REDISSON_KEY_PRE+lockKey;
        RLock lock = redissonClient.getLock(lockKey);
        return lock;
    }

    /**
     * 加锁
     *
     * @param lockKey
     * @return
     */
    public RLock lock(String lockKey) {
        RLock lock =getLockInstance(lockKey);
        lock.lock();
        return lock;
    }

    /**
     * 释放锁
     *
     * @param lockKey
     */
    public void unlock(String lockKey) {
        RLock lock = getLockInstance(lockKey);
        // 当前线程的分布式锁
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            unlock(lock);
        }
    }

    /**
     * 释放锁
     *
     * @param lock
     */
    public void unlock(RLock lock) {
        // 当前线程的分布式锁
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    /**
     * 带超时的锁
     *
     * @param lockKey
     * @param timeout 超时时间   单位：秒
     */
    public RLock lock(String lockKey, int timeout) {
        RLock lock = getLockInstance(lockKey);
        lock.lock(timeout, TimeUnit.SECONDS);
        return lock;
    }

    /**
     * 带超时的锁
     *
     * @param lockKey
     * @param unit    时间单位
     * @param timeout 超时时间
     */
    public RLock lock(String lockKey, TimeUnit unit, int timeout) {
        RLock lock = getLockInstance(lockKey);
        lock.lock(timeout, unit);
        return lock;
    }

    /**
     * 尝试获取锁 默认加锁等待1秒 锁持有时间15秒
     *
     * @param lockKey
     * @return
     */
    public boolean tryLock(String lockKey) {
        return tryLock(lockKey, TimeUnit.SECONDS, WAIT_TIME, LEASE_TIME);
    }


    /**
     * 尝试获取锁
     *
     * @param lockKey
     * @param waitTime  最多等待时间
     * @param leaseTime 上锁后自动释放锁时间
     * @return
     */
    public boolean tryLock(String lockKey, int waitTime, int leaseTime) {
        return tryLock(lockKey, TimeUnit.SECONDS, waitTime, leaseTime);
    }

    /**
     * 尝试获取锁
     *
     * @param lockKey
     * @param unit      时间单位
     * @param waitTime  最多等待时间
     * @param leaseTime 上锁后自动释放锁时间
     * @return
     */
    public boolean tryLock(String lockKey, TimeUnit unit, int waitTime, int leaseTime) {
        RLock lock = getLockInstance(lockKey);
        try {
            return lock.tryLock(waitTime, leaseTime, unit);
        } catch (Exception e) {
            log.error("lockKey [{}] 尝试获取分布式锁失败  异常 e{}",lockKey,e);
            return false;
        }
    }

    /**
     * 是否加锁成功
     * @param lockKey
     * @return
     */
    public boolean isLock(String lockKey){
        RLock lock = getLockInstance(lockKey);
        try {
            return lock.isLocked();
        } catch (Exception e) {
            log.error("lockKey {} 是否加锁成功 异常 e{}",lockKey,e);
            return false;
        }
    }

    public RGeo getGeo(String key){
        return redissonClient.getGeo(key);
    }
}
