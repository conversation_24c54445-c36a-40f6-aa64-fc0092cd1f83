package com.jnby.mallasset.remote.dayuecheng.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DycMemberRegisterReqEntity.java
 * 文件简介: 大悦城公共参数请求
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */

@Data
public class DycCommonReqEntity implements Serializable {
    @ApiModelProperty(value = "应用标识", required = true)
    private String app_id;
    @ApiModelProperty(value = "调用的服务名", required = true)
    private String method;
    @ApiModelProperty(value = "时间戳", required = true)
    private long timestamp;
    @ApiModelProperty(value = "签名字符串", required = true)
    private String sign;
}
