<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.mallasset.module.mapper.box.CashierMallStoreConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.mallasset.module.model.CashierMallStoreConfig">
        <id column="ID" property="id" />
        <result column="IS_DELETE" property="isDelete" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="MALL_ASSET_CONFIG_ID" property="mallAssetConfigId" />
        <result column="MALL_NAME" property="mallName" />
        <result column="PLATFORM_MALL_STORE_ID" property="platformMallStoreId" />
        <result column="MALL_STORE_NAME" property="mallStoreName" />
        <result column="PAY_CHANNEL" property="payChannel" />
        <result column="PAY_SIGN" property="paySign" />
        <result column="BJ_STORE_ID" property="bjStoreId" />
        <result column="POINTS_DEDUCTION_SCALE" property="pointsDeductionScale" />
        <result column="POINTS_DEDUCTION_THRESHOLD" property="pointsDeductionThreshold" />
        <result column="POINTS_DEDUCTION_UPPER_LIMIT" property="pointsDeductionUpperLimit" />
        <result column="POINTS_DEDUCTION_LADDER" property="pointsDeductionLadder" />
        <result column="MCH_ID" property="mchId" />
        <result column="PLATFORM_MALL_ID" property="platformMallId" />
        <result column="PLATFORM_APP_ID" property="platformAppId" />
        <result column="PLATFORM_PUBLIC_KEY" property="platformPublicKey" />
        <result column="PLATFORM_PRIVATE_KEY" property="platformPrivateKey" />
        <result column="SQB_APP_ID" property="sqbAppId" />
        <result column="SQB_TERMINAL_SN" property="sqbTerminalSn" />
        <result column="HAS_LINK_CASHIER" property="hasLinkCashier" />
        <result column="INTEGRAL_SKU_CODE" property="integralSkuCode" />
        <result column="COLLECTION_TYPE" property="collectionType" />
        <result column="USE_ASSET_ONE_TIME_REFUND" property="useAssetOneTimeRefund" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, IS_DELETE, CREATE_TIME, UPDATE_TIME, MALL_ASSET_CONFIG_ID, MALL_NAME, PLATFORM_MALL_STORE_ID, MALL_STORE_NAME, PAY_CHANNEL, PAY_SIGN, BJ_STORE_ID, POINTS_DEDUCTION_SCALE,
          POINTS_DEDUCTION_THRESHOLD, POINTS_DEDUCTION_UPPER_LIMIT, POINTS_DEDUCTION_LADDER, MCH_ID, PLATFORM_MALL_ID, PLATFORM_APP_ID, PLATFORM_PUBLIC_KEY, PLATFORM_PRIVATE_KEY,
          SQB_APP_ID, SQB_TERMINAL_SN, HAS_LINK_CASHIER,INTEGRAL_SKU_CODE, COLLECTION_TYPE,USE_ASSET_ONE_TIME_REFUND
    </sql>

    <select id="selectByStoreId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM jnby.cashier_mall_store_config WHERE BJ_STORE_ID = #{storeId}
    </select>

    <select id="selectInfoByStoreId" resultType="com.jnby.mallasset.module.model.CashierMallAssetStoreRef">
        select
            a.PLATFORM_APP_ID platformAppId,
            a.PLATFORM_PUBLIC_KEY platformPublicKey,
            a.PLATFORM_PRIVATE_KEY platformPrivateKey,
            a.PLATFORM_MALL_STORE_ID platformMallStoreId,
            b.API_PLATFORM apiPlatform,
            a.PLATFORM_MALL_ID platformMallId,
            a.INTEGRAL_SKU_CODE integralSkuCode,
            a.ALL_REFUND allRefund,
            b.HAS_USE_PARKING_COUPON hasUseParkingCoupon
            from jnby.cashier_mall_store_config a
        left join cashier_mall_asset_config b
        on a.MALL_ASSET_CONFIG_ID = b.id
        where a.BJ_STORE_ID = #{storeId}
    </select>
</mapper>