package com.jnby.mallasset.remote.mallcoo.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PointsDeductionByRefundRespEntity implements Serializable {
    @ApiModelProperty("扣除消费积分值（只有当前此消费小票处于可退状态且退消费积分成功时才返回对应的值）")
    private Long ReturnScore;
    @ApiModelProperty("订单号")
    private String ordNum;
    @ApiModelProperty("业务ID，唯一值")
    private String exchangeId;
    @ApiModelProperty("外部单号")
    private String exchangeNo;
}
