package com.jnby.mallasset.remote.k11.entity.pos;

import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

@Data
@Root(name = "PostSales", strict = false)
@Namespace(reference = "http://tempuri.org/")
public class KElevenRequestModel {

    @Element(name = "strCallUserCode" , required = false)
    private String strCallUserCode;

    @Element(name = "strCallPassword" , required = false)
    private String strCallPassword;

    @Element(name = "strStoreCode" , required = false)
    private String strStoreCode;

    @Element(name = "strType" , required = false)
    private String strType;

    @Element(name = "strSalesDate_YYYYMMDD" , required = false)
    private String strSalesDate;

    @Element(name = "strSalesTime_HHMISS" , required = false)
    private String strSalesTime;

    @Element(name = "strSalesDocNo" , required = false)
    private String strSalesDocNo;

    @Element(name = "strVipCode" , required = false)
    private String strVipCode;

    @Element(name = "strTenderCode" , required = false)
    private String strTenderCode;

    @Element(name = "strRemark" , required = false)
    private String strRemark;

    @Element(name = "strItems" , required = false)
    private String strItems;
}
