package com.jnby.mallasset.remote.haiancheng.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class HacOrderResponse implements Serializable {
    @ApiModelProperty("订单号")
    private String ordNum;
    @ApiModelProperty("业务ID，唯一值")
    private String exchangeId;
    @ApiModelProperty("外部单号")
    private String exchangeNo;
}
