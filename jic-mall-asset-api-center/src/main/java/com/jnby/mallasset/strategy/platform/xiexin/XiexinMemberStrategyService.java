package com.jnby.mallasset.strategy.platform.xiexin;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.remote.wanke.entity.BaseWanKeResp;
import com.jnby.mallasset.remote.xiexin.IXiexinRemoteHttpApi;
import com.jnby.mallasset.remote.xiexin.entity.BaseXiexinResp;
import com.jnby.mallasset.remote.xiexin.entity.vip.XiexinMemberRegisterEntity;
import com.jnby.mallasset.remote.xiexin.entity.vip.XiexinMemberRequestEntity;
import com.jnby.mallasset.remote.xiexin.entity.vip.XiexinMemberResponseEntity;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.DateUtil;
import com.jnby.mallasset.util.Md5Util;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.Map;
import java.util.TreeMap;

/**
 * 大融城会员平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.XIE_XIN, category = PlatformCategoryTypeEnum.MEMBER)
@AllArgsConstructor
@Slf4j
public class XiexinMemberStrategyService extends AbstractMemberService {

    private IXiexinRemoteHttpApi xiexinRemoteHttpApi;
    private CashierMallMemberLogMapper cashierMallMemberLogMapper;
    
    @Override
    public ResponseResult openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        ResponseResult commonResponse = new ResponseResult();
        log.info("协信会员注册输入参数：{}", JSON.toJSONString(memberRegisterReq));
        try{
            CashierMallMemberLog cashierMallMemberLog = recordCashierMallMemberLog(memberRegisterReq, cashierMallAssetStoreRef);
            String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
            String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
            String platformMallStoreId = cashierMallAssetStoreRef.getPlatformMallStoreId();
            String mobile = memberRegisterReq.getMobile();
            String nickName = memberRegisterReq.getNickName() != null ? memberRegisterReq.getNickName() : "未知";
            String timestamp = DateUtil.getCurrentTime() + "";
            // 查询是否新会员
            ResponseResult<CommonMemberResponse> responseResponseResult = getMemberInfo(memberRegisterReq,cashierMallAssetStoreRef);
            CommonMemberResponse commonMemberResponse = responseResponseResult.getData();
            if(commonMemberResponse != null && commonMemberResponse.getMemberFlag() == 0){
                // 进行注册会员
                // 生成签名
                String uuid = IdUtil.simpleUUID();
                Map<String, String> parameters = new TreeMap<>();
                StringBuilder stringBuilder = new StringBuilder();
                parameters.put("app_no",platformAppId);
                parameters.put("timestamp",timestamp);
                parameters.put("store_id",platformMallStoreId);
                parameters.put("mobile",mobile);
                parameters.put("nick_name",nickName);
                parameters.put("nonce_str",uuid);
                for(Map.Entry<String,String> entry : parameters.entrySet()){
                    String str = entry.getKey() + "=" + entry.getValue();
                    stringBuilder.append(str).append("&");
                }
                String json = stringBuilder.toString();
                json = json + "key=" + platformPrivateKey;
                String sign = Md5Util.getMd5Hash(json);
                log.info("协信会员注册生成签名：{}", sign);
                XiexinMemberRegisterEntity req = new XiexinMemberRegisterEntity();
                req.setApp_no(platformAppId);
                req.setStore_id(platformMallStoreId);
                req.setNonce_str(uuid);
                req.setMobile(mobile);
                req.setTimestamp(timestamp);
                req.setNick_name(nickName);
                req.setSign(sign);
                log.info("协信会员注册调用远程接口输入参数：{}", JSON.toJSONString(req));
                Response<BaseXiexinResp> execute = xiexinRemoteHttpApi.registerMember(req).execute();
                BaseXiexinResp baseXiexinResp = execute.body();
                log.info("协信会员注册调用远程接口返回结果：{}", JSON.toJSONString(baseXiexinResp));
                if(baseXiexinResp != null && baseXiexinResp.getCode() == 0){
                    commonResponse.setCode(200);
                    commonMemberResponse.setMemberFlag(0);
                    commonResponse.setData(commonMemberResponse);
                    // 更新执行标识
                    cashierMallMemberLog.setIsExecute("Y");
                    cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
                }
            }else {
                commonResponse.setCode(200);
                commonMemberResponse.setMemberFlag(1);
                commonResponse.setData(commonMemberResponse);
                // 更新执行标识
                cashierMallMemberLog.setIsExecute("Y");
                cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 协信注册会员异常",memberRegisterReq.getMobile(), e);
        }
        return commonResponse;
    }

    private CashierMallMemberLog recordCashierMallMemberLog(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallMemberLog cashierMallMemberLog = new CashierMallMemberLog();
        cashierMallMemberLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallMemberLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallMemberLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallMemberLog.setBjStoreId(memberRegisterReq.getStoreId());
        cashierMallMemberLog.setMobile(memberRegisterReq.getMobile());
        cashierMallMemberLog.setIsExecute("N");
        cashierMallMemberLogMapper.insertEntity(cashierMallMemberLog);
        return cashierMallMemberLog;
    }

    @Override
    public ResponseResult<CommonMemberResponse> getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        ResponseResult<CommonMemberResponse> commonResponse = new ResponseResult<>();
        log.info("协信会员查询输入参数：{}", JSON.toJSONString(memberRegisterReq));
        try{
            String platformAppId = cashierMallAssetStoreRef.getPlatformAppId();
            String platformPrivateKey = cashierMallAssetStoreRef.getPlatformPrivateKey();
            String platformMallStoreId = cashierMallAssetStoreRef.getPlatformMallStoreId();
            String mobile = memberRegisterReq.getMobile();
            String timestamp = DateUtil.getCurrentTime() + "";
            // 生成签名
            String uuid = IdUtil.simpleUUID();
            Map<String, String> parameters = new TreeMap<>();
            StringBuilder stringBuilder = new StringBuilder();
            parameters.put("app_no",platformAppId);
            parameters.put("timestamp",timestamp);
            parameters.put("store_id",platformMallStoreId);
            parameters.put("mobile",mobile);
            parameters.put("nonce_str",uuid);
            for(Map.Entry<String,String> entry : parameters.entrySet()){
                String str = entry.getKey() + "=" + entry.getValue();
                stringBuilder.append(str).append("&");
            }
            String json = stringBuilder.toString();
            json = json + "key=" + platformPrivateKey;
            String sign = Md5Util.getMd5Hash(json);
            log.info("协信会员查询生成签名：{}", sign);
            XiexinMemberRequestEntity req = new XiexinMemberRequestEntity();
            req.setApp_no(platformAppId);
            req.setStore_id(platformMallStoreId);
            req.setNonce_str(uuid);
            req.setMobile(mobile);
            req.setTimestamp(timestamp);
            req.setSign(sign);
            log.info("协信会员查询调用远程接口输入参数：{}", JSON.toJSONString(req));
            Response<BaseXiexinResp<XiexinMemberResponseEntity>> execute = xiexinRemoteHttpApi.queryMember(req).execute();
            BaseXiexinResp<XiexinMemberResponseEntity> body = execute.body();
            CommonMemberResponse commonMemberResponse = new CommonMemberResponse();
            log.info("协信会员查询调用远程接口返回结果：{}", JSON.toJSONString(body));
            if(body.getCode() != null && body.getCode() == 0){
                XiexinMemberResponseEntity response = body.getData();
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setMsg(body.getMessage());
                commonMemberResponse.setMemberFlag(response.getIs_exist());
                commonResponse.setData(commonMemberResponse);
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.info("{} 协信查询会员异常",memberRegisterReq.getMobile(), e);
        }
        return commonResponse;
    }

}
