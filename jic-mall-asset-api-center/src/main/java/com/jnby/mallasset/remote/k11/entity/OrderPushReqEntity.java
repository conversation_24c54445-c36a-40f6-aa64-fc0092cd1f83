package com.jnby.mallasset.remote.k11.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 文件名: com.jnby.mallasset.remote.haiancheng.entity-BaseHacResp.java
 * 文件简介: 海岸城基础返回对象
 *
 * <AUTHOR>
 * @date 2025/1/13 9:51
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Data
public class OrderPushReqEntity implements Serializable {
    @ApiModelProperty(value = "销售单号")
    private String order_code;
    @ApiModelProperty(value = "订单时间,时间戳, 如: 1546272000")
    private Long sales_time;
    @ApiModelProperty(value = "实际支付金额，带2位小数点数字 如:200.00")
    private BigDecimal sales_amount;
    @ApiModelProperty(value = "付款明细")
    private List<OrderPayItemEntity> sales_payments;
    @ApiModelProperty(value = "核销店铺编码")
    private String store_code;
    @ApiModelProperty(value = "会员卡号")
    private String member_code;
    @ApiModelProperty(value = "地区代码，如GZ01")
    private String area_code;
    @ApiModelProperty(value = "订单原始金额")
    private BigDecimal origin_amount;

}
