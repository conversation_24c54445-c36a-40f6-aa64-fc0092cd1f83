package com.jnby.mallasset.api.dto.points;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("积分回参")
@Data
public class PointsInfoRespDto implements Serializable {
    @ApiModelProperty("可用积分")
    private Integer canUsePoints;
    @ApiModelProperty(value = "积分换算比例。1积分换算多少元。例如0.01=1积分兑换0.01元")
    private BigDecimal pointsDeductionScale;
    @ApiModelProperty(value = "单订单积分抵扣起扣门槛")
    private Long pointsDeductionThreshold;
    @ApiModelProperty(value = "单订单积分抵扣上限")
    private Long pointsDeductionUpperLimit;
    @ApiModelProperty(value = "单订单积分抵扣阶梯单位。100代表积分每满100才可以抵扣计算")
    private Long pointsDeductionLadder;
}
