package com.jnby.mallasset.dto.req.order;

import com.jnby.mallasset.api.dto.BaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("订单POS数据请求入参")
public class OrderPosReq extends BaseReq {

    @ApiModelProperty(value = "门店")
    private String storeId;
    @ApiModelProperty(value = "订单编号",required = true)
    private String ordNum;
    @ApiModelProperty(value = "订单付款编号")
    private String ordPayNo;
    @ApiModelProperty(value = "会员手机号",required = true)
    private String memberTel;
    @ApiModelProperty(value = "订单实付金额",required = true)
    private BigDecimal ordAmount;
    @ApiModelProperty(value = "商品数量")
    private int totQty;
    @ApiModelProperty(value = "商品总价格")
    private BigDecimal totPriceList;
    @ApiModelProperty(value = "订单总金额，包括券")
    private BigDecimal totAmtActual;
    @ApiModelProperty(value = "订单消费时间",required = true)
    private Long consumeTime;
}
