package com.jnby.mallasset.remote.huarun.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class RefundOrderRespEntity {
    @ApiModelProperty("数据交换平台交换ID")
    private String exchangeId;
    @ApiModelProperty("异步回调附加数据")
    private String notifyAttach;
    @ApiModelProperty("数字化交易订单号")
    private String orderNo;
    @ApiModelProperty("数字化交易退单号")
    private String refundNo;
}
