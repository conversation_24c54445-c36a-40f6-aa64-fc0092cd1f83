package com.jnby.mallasset.remote.yintai.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TransProduct {
    @ApiModelProperty(value = "商品数量")
    private int productNum;
    @ApiModelProperty(value = "商品原单价")
    private BigDecimal unitPrice;
    @ApiModelProperty(value = "顺序号")
    private int sort;
    @ApiModelProperty(value = "商品编码")
    private String productId;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "商品实付金额")
    private BigDecimal realAmt;
}
