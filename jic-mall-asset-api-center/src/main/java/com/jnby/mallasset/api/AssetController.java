package com.jnby.mallasset.api;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.api.dto.BaseReq;
import com.jnby.mallasset.api.dto.asset.*;
import com.jnby.mallasset.api.dto.coupon.CouponInfoRespDto;
import com.jnby.mallasset.api.dto.member.MemberInfoRespDto;
import com.jnby.mallasset.api.dto.points.PointsInfoRespDto;
import com.jnby.mallasset.config.exception.MallException;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.convert.MallConvertor;
import com.jnby.mallasset.dto.req.coupon.CouponSendReqDto;
import com.jnby.mallasset.module.service.IAssetBizService;
import com.jnby.mallasset.util.RedissonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RequestMapping("/asset/api")
@RestController
@Slf4j
@Api(tags = "资产接口")
public class AssetController {

    @Autowired
    private IAssetBizService assetBizService;

    @Autowired
    private RedissonUtil redissonUtil;
    @Autowired
    private MallConvertor mallConvertor;

    @PostMapping("/getMallConfig")
    @ApiOperation(value = "查询商场配置")
    public ResponseResult<MallConfigRespDto> getMallConfig(@RequestBody MallConfigReqDto req) {
        log.info("查询商场配置请求 入参:{}", JSON.toJSONString(req));
        req.check();
        ResponseResult<MallConfigRespDto> mallConfig = assetBizService.getMallConfig(req);
        log.info("查询商场配置请求 回参:{}", JSON.toJSONString(mallConfig));
        return mallConfig;
    }

    @ApiOperation(value = "BOX查询商场简化配置")
    @PostMapping("/listMallConfigForBox")
    public ResponseResult<List<MallConfigListRespDto>> listMallConfigForBox(@RequestBody MallConfigListReqDto req) {
        req.check();
        log.info("BOX查询商场简化配置 入参:{}", JSON.toJSONString(req));
        List<MallConfigListRespDto> rspList = Lists.newArrayList();
        req.getStoreCodeList().forEach(storeCode -> {
            MallConfigReqDto reqDto = new MallConfigReqDto();
            reqDto.setStoreId(storeCode);
            reqDto.setBusinessType(req.getBusinessType());
            reqDto.setPayChannel(req.getPayChannel());
            ResponseResult<MallConfigRespDto> mallConfig = assetBizService.getMallConfig(reqDto);
            if (mallConfig.getData() != null) {
                rspList.add(mallConvertor.mallConfigRespDto2MallConfigListRespDto(mallConfig.getData()));
            }
        });
        log.info("BOX查询商场简化配置 回参:{}", JSON.toJSONString(rspList));
        return ResponseResult.success(rspList);
    }

    @PostMapping("/mallMember")
    @ApiOperation(value = "会员相关信息")
    public ResponseResult<MemberInfoRespDto> isMallMember(@RequestBody BaseReq req) {
        req.check();
        log.info("会员相关信息请求入参:{}", JSON.toJSONString(req));
        MemberInfoRespDto dto = new MemberInfoRespDto();
        dto.setHasMallMember(assetBizService.isMallMember(req));
        dto.setHasChosePrivacy(assetBizService.isChosePrivacy(req));
        log.info("会员相关信息请求回参:{}", JSON.toJSONString(dto));
        return ResponseResult.success(dto);
    }

    @PostMapping("/submitChosePrivacy")
    @ApiOperation(value = "提交勾选协议")
    public ResponseResult<Boolean> submitChosePrivacy(@RequestBody BaseReq req) {
        req.check();
        log.info("提交勾选协议请求入参:{}", JSON.toJSONString(req));
        ResponseResult<Boolean> responseResult = assetBizService.submitChosePrivacy(req);
        log.info("提交勾选协议请求回参:{}", JSON.toJSONString(responseResult));
        return responseResult;
    }

    @PostMapping("/listCanUseCoupon")
    @ApiOperation(value = "获取可用券")
    public ResponseResult<List<CouponInfoRespDto>> listCanUseCoupon(@RequestBody BaseReq req) {
        log.info("获取可用券请求入参:{}", JSON.toJSONString(req));
        ResponseResult<List<CouponInfoRespDto>> listResponseResult;
        try {
            req.check();
            listResponseResult = assetBizService.listCoupon(req);
        } catch (Exception e) {
            listResponseResult = ResponseResult.success(new ArrayList());
        }
        log.info("获取可用券请求回参:{}", JSON.toJSONString(listResponseResult));
        return listResponseResult;
    }

    @PostMapping("/getPoints")
    @ApiOperation(value = "获取总积分和使用规则")
    public ResponseResult<PointsInfoRespDto> getPoints(@RequestBody BaseReq req) {
        log.info("获取总积分和使用规则请求入参:{}", JSON.toJSONString(req));
        ResponseResult<PointsInfoRespDto> responseResult = null;
        try {
            req.check();
            responseResult = assetBizService.getPoints(req);
        } catch (Exception e) {
            responseResult = ResponseResult.success(null);
        }
        log.info("获取总积分和使用规则请求回参:{}", JSON.toJSONString(responseResult));
        return responseResult;
    }

    @PostMapping("/use")
    @ApiOperation(value = "使用资产")
    public ResponseResult<AssetUseRespDto> useAsset(@RequestBody AssetOperateReqDto req) {
        log.info("使用资产请求入参:{}", JSON.toJSONString(req));
        req.check();
        String key = "MALL_ASSET_USE:" + req.getCustomerId();
        if (!redissonUtil.tryLock(key)) {
            log.info("使用资产获取买家锁失败[{}]", key);
            throw new MallException(SystemErrorEnum.REPEAT_OPERATION_ERROR);
        }
        try {
            return assetBizService.useAsset(req);
        } finally {
            log.info("使用资产释放锁[{}]", key);
            redissonUtil.unlock(key);
        }
    }


    @PostMapping("/return")
    @ApiOperation(value = "返还资产")
    public ResponseResult<AssetUseRespDto> returnAsset(@RequestBody AssetOperateReqDto req) {
        log.info("返还资产请求入参:{}", JSON.toJSONString(req));
        req.check();
        String key = "MALL_ASSET_RETURN:" + req.getCustomerId();
        if (!redissonUtil.tryLock(key)) {
            log.info("返还资产获取买家锁失败[{}]", key);
            throw new MallException(SystemErrorEnum.REPEAT_OPERATION_ERROR);
        }
        try {
            return assetBizService.returnAsset(req);
        } finally {
            log.info("返还资产释放锁[{}]", key);
            redissonUtil.unlock(key);
        }
    }

    @PostMapping("/send")
    @ApiOperation(value = "发券")
    public ResponseResult<AssetUseRespDto> sendCoupon(@RequestBody CouponSendReqDto req) {
        log.info("发券 请求入参:{}", JSON.toJSONString(req));
        req.check();
        String key = "MALL_ASSET_SEND_COUPON:" + req.getCustomerId();
        if (!redissonUtil.tryLock(key)) {
            log.info("发券 获取买家锁失败[{}]", key);
            throw new MallException(SystemErrorEnum.REPEAT_OPERATION_ERROR);
        }
        try {
            return assetBizService.sendCoupon(req);
        } finally {
            log.info("发券 释放锁[{}]", key);
            redissonUtil.unlock(key);
        }
    }

    @PostMapping("/listPayConfig")
    @ApiOperation(value = "查询门店支付方式配置")
    public ResponseResult<List<PayConfigRespDto>> listPayConfig(@RequestBody PayConfigReqDto req) {
        log.info("查询门店支付方式配置 入参:{}", JSON.toJSONString(req));
        req.check();
        ResponseResult<List<PayConfigRespDto>> payConfig;
        if (StringUtils.isBlank(req.getStoreId())) {
            // 微商城的场景获取的是vid做转换
            String storeCode = assetBizService.vid2StoreCode(req.getVid());
            req.setStoreId(storeCode);
            payConfig = assetBizService.listPayConfig(req);;
        } else {
            payConfig = assetBizService.listPayConfig(req);;
        }
        log.info("查询门店支付方式配置 回参:{}", JSON.toJSONString(payConfig));
        return payConfig;
    }
}
