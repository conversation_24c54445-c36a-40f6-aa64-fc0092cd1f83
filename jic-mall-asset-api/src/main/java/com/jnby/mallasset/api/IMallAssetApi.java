package com.jnby.mallasset.api;

import com.jnby.common.ResponseResult;
import com.jnby.mallasset.api.dto.BaseReq;
import com.jnby.mallasset.api.dto.asset.*;
import com.jnby.mallasset.api.dto.coupon.CouponInfoRespDto;
import com.jnby.mallasset.api.dto.points.PointsInfoRespDto;
import com.jnby.mallasset.api.fallback.JicMallAssetServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

@FeignClient(value = "jic-mall-asset-api-center", fallbackFactory = JicMallAssetServiceFallbackFactory.class)
public interface IMallAssetApi {

    /**
     * 查询商场配置
     */
    @PostMapping("/asset/api/getMallConfig")
    ResponseResult<MallConfigRespDto> getMallConfig(@RequestBody MallConfigReqDto req, @RequestHeader("auth") String auth);

    /**
     * BOX查询商场配置列表(精简版)
     */
    @PostMapping("/asset/api/listMallConfigForBox")
    ResponseResult<List<MallConfigListRespDto>> listMallConfigForBox(@RequestBody MallConfigListReqDto req, @RequestHeader("auth") String auth);

    /**
     * 获取可用券
     */
    @PostMapping("/asset/api/listCanUseCoupon")
    ResponseResult<List<CouponInfoRespDto>> listCanUseCoupon(@RequestBody BaseReq req, @RequestHeader("auth") String auth);

    /**
     * 获取总积分和使用规则
     */
    @PostMapping("/asset/api/getPoints")
    ResponseResult<PointsInfoRespDto> getPoints(@RequestBody BaseReq req, @RequestHeader("auth") String auth);

    /**
     * 使用资产
     */
    @PostMapping("/asset/api/use")
    ResponseResult<AssetUseRespDto> useAsset(@RequestBody AssetOperateReqDto req, @RequestHeader("auth") String auth);

    /**
     * 返还资产
     */
    @PostMapping("/asset/api/return")
    ResponseResult<AssetUseRespDto> returnAsset(@RequestBody AssetOperateReqDto req, @RequestHeader("auth") String auth);

    /**
     * 查询门店支付方式配置
     */
    @PostMapping("/asset/api/listPayConfig")
    ResponseResult<List<PayConfigRespDto>> listPayConfig(@RequestBody PayConfigReqDto req, @RequestHeader("auth") String auth);
}
