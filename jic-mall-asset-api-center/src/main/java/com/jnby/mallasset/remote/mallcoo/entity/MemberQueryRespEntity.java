package com.jnby.mallasset.remote.mallcoo.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * {
 *         "OpenUserID": "b2955f0c39ebd909",
 *             "NickName": "用户7907",
 *             "Avatar": "https://i1.mallcoo.cn/sp_mall/7691o191-4197-499f-8ca1-2c2db55eec87_0.png",
 *             "InternationalMobileCode": "",
 *             "Mobile": "19975377907",
 *             "WXOpenID": null,
 *             "MallCardNo": "500615240",
 *             "MallCardName": "电子会员",
 *             "ThirdPartyCardID": "1020701330378854400",
 *             "ThirdPartyCardNo": "500615240",
 *             "Score": 509,
 *             "MallCardTypeID": 5101,
 *             "UserName": "",
 *             "Gender": 0,
 *             "Age": 0,
 *             "Birthday": "",
 *             "UserCreateTime": "2024/07/18 14:22:40",
 *             "MallCardApplyTime": "2024/07/18 14:22:40",
 *             "MallCardCode": "1",
 *             "SourceMallID": 11944
 *     }
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MemberQueryRespEntity implements Serializable {
    @ApiModelProperty("会员ID")
    private String OpenUserID;
    @ApiModelProperty("昵称")
    private String NickName;
    @ApiModelProperty("头像")
    private String Avatar;
    @ApiModelProperty("国家码")
    private String InternationalMobileCode;
    @ApiModelProperty("手机号")
    private String Mobile;
    @ApiModelProperty("微信OpenID")
    private String WXOpenID;
    @ApiModelProperty("会员卡号")
    private String MallCardNo;
    @ApiModelProperty("会员卡名称")
    private String MallCardName;
    @ApiModelProperty("第三方卡号")
    private String ThirdPartyCardID;
    @ApiModelProperty("第三方卡号")
    private String ThirdPartyCardNo;
    @ApiModelProperty("积分")
    private Integer Score;
    @ApiModelProperty("会员卡类型ID")
    private Integer MallCardTypeID;
    @ApiModelProperty("用户名")
    private String UserName;
    @ApiModelProperty("性别")
    private Integer Gender;
    @ApiModelProperty("年龄")
    private Integer Age;
    @ApiModelProperty("生日")
    private String Birthday;
    @ApiModelProperty("创建时间")
    private String UserCreateTime;
    @ApiModelProperty("会员卡申请时间")
    private String MallCardApplyTime;
    @ApiModelProperty("会员卡编码")
    private String MallCardCode;
    @ApiModelProperty("来源商城ID")
    private Integer SourceMallID;
}
