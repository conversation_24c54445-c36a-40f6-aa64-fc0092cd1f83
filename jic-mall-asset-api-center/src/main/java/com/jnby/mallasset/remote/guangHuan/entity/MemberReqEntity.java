package com.jnby.mallasset.remote.guangHuan.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件名: com.jnby.mallasset.remote.drc.entity-DrcMemberRegisterReqEntity.java
 * 文件简介: 会员注册请求
 *
 * <AUTHOR>
 * @date 2024/7/24 11:02
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MemberReqEntity implements Serializable {
    @ApiModelProperty(value = "拓展渠道", required = true)
    private String expandingChannel;
    @ApiModelProperty(value = "来源组织 ID", required = true)
    private String fromOrgId;
    @ApiModelProperty(value = "手机号", required = true)
    private String mobileNo;
}
