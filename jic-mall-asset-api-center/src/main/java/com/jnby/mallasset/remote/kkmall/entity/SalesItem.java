package com.jnby.mallasset.remote.kkmall.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SalesItem {

    private int salesLineNumber;

    private List<String> salesman;

    private String itemCode;

    private String itemOrgId;

    private String itemLotNum;

    private String serialNumber;

    private int inventoryType;

    private int qty;

    private int itemDiscountLess;

    private int totalDiscountLess;

    private BigDecimal netAmount;

    private String salesItemRemark;

    private String extendParameter;
}
