package com.jnby.mallasset.remote.xiexin;


import com.jnby.mallasset.remote.xiexin.entity.order.XiexinOrderPosResponseSoapEnvelope;
import com.jnby.mallasset.remote.xiexin.entity.order.XiexinOrderPosSoapEnvelope;
import com.jnby.mallasset.remote.xiexin.entity.order.XiexinOrderResponseSoapEnvelope;
import com.jnby.mallasset.remote.xiexin.entity.order.XiexinOrderSoapEnvelope;
import com.jnby.mallasset.remote.xiexin.entity.vip.XiexinVipResponseSoapEnvelope;
import com.jnby.mallasset.remote.xiexin.entity.vip.XiexinVipSoapEnvelope;
import com.jnby.mallasset.remote.xiexin.entity.vip.XiexinVipTagResponseSoapEnvelope;
import com.jnby.mallasset.remote.xiexin.entity.vip.XiexinVipTagSoapEnvelope;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;


/**
 * 文件名: com.jnby.mallasset.remote.xiexin-IXiexinRemoteHttpApi.java
 * 文件简介: 协信平台
 *
 * <AUTHOR>
 * @date 2024/12/19 9:35
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface IXiexinSoapRemoteHttpApi {


    @Headers({
            "Content-Type:text/xml; charset=utf-8",
            "SOAPAction:http://tempuri.org/GetVipCard"
    })
    @POST("/WebServiceWeChat.asmx")
    Call<XiexinVipResponseSoapEnvelope> getVipCard(@Body XiexinVipSoapEnvelope req);


    @Headers({
            "Content-Type:text/xml; charset=utf-8",
            "SOAPAction:http://tempuri.org/SaleBillCent"
    })
    @POST("/WebServiceWeChat.asmx")
    Call<XiexinOrderResponseSoapEnvelope> saleBillCent(@Body XiexinOrderSoapEnvelope req);

    @Headers({
            "Content-Type:text/xml; charset=utf-8",
            "SOAPAction:http://tempuri.org/SaleShopItem"
    })
    @POST("/WebServiceWeChat.asmx")
    Call<XiexinOrderPosResponseSoapEnvelope> saleShopItem(@Body XiexinOrderPosSoapEnvelope req);

    @Headers({
            "Content-Type:text/xml; charset=utf-8",
            "SOAPAction:http://tempuri.org/GetVipLableList"
    })
    @POST("/WebServiceWeChat.asmx")
    Call<XiexinVipTagResponseSoapEnvelope> getVipLableList(@Body XiexinVipTagSoapEnvelope req);
}
