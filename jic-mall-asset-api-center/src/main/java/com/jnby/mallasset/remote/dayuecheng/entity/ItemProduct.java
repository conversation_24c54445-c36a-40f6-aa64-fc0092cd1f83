package com.jnby.mallasset.remote.dayuecheng.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 文件名: com.jnby.mallasset.remote.dayuecheng.entity-ItemProduct.java
 * 文件简介: 商品明细
 *
 * <AUTHOR>
 * @date 2025/1/3 16:43
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */

@Data
public class ItemProduct implements Serializable {
    @ApiModelProperty(value = "商品编号")
    private String itemCode;
    @ApiModelProperty(value = "商品价格")
    private BigDecimal price;
    @ApiModelProperty(value = "商品数量")
    private int quantity;
}
