package com.jnby.mallasset.enums;

import com.jnby.mallasset.config.exception.MallException;

import java.util.stream.Stream;

/**
 * 支付业务渠道
 */
public enum PayBusinessTypeEnum {

    WXMALL(1, "微商城"),
    BOX(2, "BOX"),
    POS(3, "POS+"),
    REPURCHASE(4, "复购计划"),
    POS_ONLINE(5, "POS+线上（离店）"),
    STORE_CARD(6, "储值卡"),
    ;
    /**
     * 编号
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;

    PayBusinessTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static PayBusinessTypeEnum getByCode(Integer code) {
        return Stream.of(PayBusinessTypeEnum.values()).filter(item -> item.getCode().equals(code)).findFirst()
                .orElseThrow(()->new MallException("支付业务渠道暂不支持"));
    }

    public static void main(String[] args) {
        System.out.println(getByCode(4));
    }
}
