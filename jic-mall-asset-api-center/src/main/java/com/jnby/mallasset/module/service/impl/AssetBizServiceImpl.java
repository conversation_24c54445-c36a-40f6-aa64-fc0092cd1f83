package com.jnby.mallasset.module.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jnby.common.ResponseResult;
import com.jnby.common.util.IdLeaf;
import com.jnby.mallasset.api.dto.BaseReq;
import com.jnby.mallasset.api.dto.asset.*;
import com.jnby.mallasset.api.dto.coupon.CouponInfoRespDto;
import com.jnby.mallasset.api.dto.points.PointsInfoRespDto;
import com.jnby.mallasset.config.exception.MallException;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.convert.CouponConvertor;
import com.jnby.mallasset.convert.MallConvertor;
import com.jnby.mallasset.dto.req.coupon.CouponSendReqDto;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.enums.AssetTypeEnum;
import com.jnby.mallasset.enums.OperationStatusTypeEnum;
import com.jnby.mallasset.enums.OperationTypeEnum;
import com.jnby.mallasset.enums.PayBusinessTypeEnum;
import com.jnby.mallasset.module.mapper.bojun.CStoreMapper;
import com.jnby.mallasset.module.mapper.box.*;
import com.jnby.mallasset.module.mapper.wx.JicMallOrgMapper;
import com.jnby.mallasset.module.model.*;
import com.jnby.mallasset.module.model.bojun.CStore;
import com.jnby.mallasset.module.model.wx.JicMallOrg;
import com.jnby.mallasset.module.service.IAssetBizService;
import com.jnby.mallasset.module.service.ICashierMallAssetLogService;
import com.jnby.mallasset.module.service.IMemberBizService;
import com.jnby.mallasset.strategy.category.AbstractCouponService;
import com.jnby.mallasset.strategy.category.AbstractPointsService;
import com.jnby.mallasset.strategy.context.CouponContext;
import com.jnby.mallasset.strategy.context.UserContext;
import com.jnby.mallasset.strategy.factory.CouponServiceFactory;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.strategy.factory.PointsServiceFactory;
import com.jnby.mallasset.util.PriceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 资产业务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class AssetBizServiceImpl implements IAssetBizService {
    private static final String DEFAULT_BJ_CODE = "DEFAULT";
    /**
     * 龙湖门店
     */
    private static final List<String> LONG_CODE_LIST = Lists.newArrayList("5DA00160", "5DA00138", "JDA26319", "5DA26348", "JDA59506", "JDA59501", "5DA44941", "5DA00148", "2DA00132", "5DA26351", "5DA26324", "9DA26313", "5DA59525", "5DA59532", "5DA59523", "5DA66413");

    @Value("${cashier.mall.asset.log.id}")
    private String mallAssetLogId;
    @Value("${pay.config.change.switch}")
    private Integer payConfigChangeSwitch;
    @Autowired
    private CouponServiceFactory couponServiceFactory;
    @Autowired
    private PointsServiceFactory pointsServiceFactory;
    @Autowired
    private CashierMallStoreConfigMapper storeConfigMapper;
    @Autowired
    private CashierMallAssetConfigMapper mallConfigMapper;
    @Autowired
    private CouponConvertor couponConvertor;
    @Autowired
    private MallConvertor mallConvertor;
    @Autowired
    private ICashierMallAssetLogService cashierMallAssetLogService;
    @Autowired
    private JicMallOrgMapper jicMallOrgMapper;
    @Autowired
    private CStoreMapper cStoreMapper;
    @Autowired
    private IMemberBizService iMemberBizService;
    @Autowired
    private CashierMallMemberLogMapper cashierMallMemberLogMapper;
    @Autowired
    private CashierPaymentConfigMapper cashierPaymentConfigMapper;
    @Autowired
    private CashierPayConfigMapper cashierPayConfigMapper;

    @Override
    public ResponseResult<List<CouponInfoRespDto>> listCoupon(BaseReq req) {
        // 获取商户配置
        CashierMallStoreConfig storeConfig = getStoreConfig(req.getStoreId());
        CashierMallAssetConfig mallConfig = getMallConfig(storeConfig.getMallAssetConfigId());

        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getEnumByCode(Math.toIntExact(mallConfig.getApiPlatform()));
        AbstractCouponService abstractCouponService = couponServiceFactory.router(platformTypeEnum, PlatformCategoryTypeEnum.COUPON);
        UserContext user = UserContext.builder().phone(req.getCustomerId()).mallConfig(mallConfig).storeConfig(storeConfig).build();
        // 该是否能使用券资产
        if (Boolean.FALSE.equals(user.getMallConfig().getCouponCanUse())) {
            throw new MallException(SystemErrorEnum.COUPON_CONFIG_CAN_NOT_USE_ERROR);
        }
        List<CouponInfoRespDto> canUseCouponList = abstractCouponService.getCanUseCouponList(user);
        Optional.ofNullable(canUseCouponList).orElse(new ArrayList<>()).forEach(dto -> {
            if ("0.00".equals(dto.getThreshold())) {
                dto.setThreshold(null);
            } else {
                dto.setThreshold(PriceUtil.formatString(dto.getThreshold()));
            }
            dto.setReduceMoney(PriceUtil.formatString(dto.getReduceMoney()));
        });
        return ResponseResult.success(canUseCouponList);
    }

    private CashierMallAssetConfig getMallConfig(Long mallAssetConfigId) {
        CashierMallAssetConfig mallConfig = mallConfigMapper.selectById(mallAssetConfigId);
        if (mallConfig == null || mallConfig.getApiPlatform() == null) {
            throw new MallException(SystemErrorEnum.MALL_IS_NULL_ERROR);
        }
        return mallConfig;
    }

    private CashierMallStoreConfig getStoreConfig(String bjStoreId) {
        CashierMallStoreConfig storeConfig = storeConfigMapper.selectByStoreId(bjStoreId);
        if (storeConfig == null || storeConfig.getMallAssetConfigId() == null) {
            throw new MallException(SystemErrorEnum.STORE_IS_NULL_ERROR);
        }
        return storeConfig;
    }

    private CStore getCStoreById(Long id) {
        CStore cStore = cStoreMapper.selectById(id);
        if (cStore == null || cStore.getCode() == null) {
            throw new MallException(SystemErrorEnum.STORE_IS_NULL_ERROR);
        }
        return cStore;
    }


    @Override
    public ResponseResult<AssetUseRespDto> useAsset(AssetOperateReqDto req) {
        String bizId = UUID.randomUUID().toString().replace("-", "");

        // 获取商户配置
        CashierMallStoreConfig storeConfig = getStoreConfig(req.getStoreId());
        CashierMallAssetConfig mallConfig = getMallConfig(storeConfig.getMallAssetConfigId());
        UserContext user = UserContext.builder().phone(req.getCustomerId()).mallConfig(mallConfig).storeConfig(storeConfig).build();
        log.info("获取商户的配置信息UserContext:{}", JSON.toJSONString(user));

        // 获取路由
        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getEnumByCode(Math.toIntExact(mallConfig.getApiPlatform()));
        AbstractCouponService couponService = couponServiceFactory.router(platformTypeEnum, PlatformCategoryTypeEnum.COUPON);
        AbstractPointsService pointsService = pointsServiceFactory.router(platformTypeEnum, PlatformCategoryTypeEnum.POINTS);

        boolean hasPoints = req.getPoints() != null && req.getPoints() > 0;
        boolean hasCoupon = CollectionUtils.isNotEmpty(req.getCouponNoList());
        log.info("是否使用积分:{}, 是否使用券:{}", hasPoints, hasCoupon);

        if (hasPoints && !hasCoupon) {
            // 只有积分
            justUsePoints(req, couponService, user, bizId);
        } else if (!hasPoints && hasCoupon) {
            // 只有券
            justUseCoupon(req, couponService, user, bizId);
        } else {
            // 都包含
            bothUse(req, couponService, user, bizId);
        }

        return ResponseResult.success(AssetUseRespDto.builder().bizId(bizId).build());
    }

    private void justUsePoints(AssetOperateReqDto req, AbstractCouponService couponService, UserContext user, String bizId) {
        throw new MallException(SystemErrorEnum.ASSET_USE_POINTS_CAN_NOT_SUPPORT_ERROR);
    }

    private void bothUse(AssetOperateReqDto req, AbstractCouponService couponService, UserContext user, String bizId) {
        //使用积分
        throw new MallException(SystemErrorEnum.ASSET_USE_POINTS_AND_COUPON_CAN_NOT_SUPPORT_ERROR);
    }

    private void justUseCoupon(AssetOperateReqDto req, AbstractCouponService couponService, UserContext user, String bizId) {
        CouponContext couponContext = couponConvertor.reqDto2Context(req);
        // 日志
        List<CashierMallAssetLog> logList = buildLogs(req, couponContext, AssetTypeEnum.COUPON, OperationTypeEnum.USE, bizId);
        try {
            // 该是否能使用券资产
            if (Boolean.FALSE.equals(user.getMallConfig().getCouponCanUse())) {
                throw new MallException(SystemErrorEnum.COUPON_CONFIG_CAN_NOT_USE_ERROR);
            }

            Map<String, CashierMallAssetLog> vcode2LogMap = logList.stream().collect(Collectors.toMap(CashierMallAssetLog::getCouponNo, Function.identity(), (v1, v2) -> v1));
            couponContext.setVcode2LogMap(vcode2LogMap);

            // 获取核销券的记录
            LambdaQueryWrapper<CashierMallAssetLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CashierMallAssetLog::getOrderNo, req.getOrderNo());
            queryWrapper.eq(CashierMallAssetLog::getAssetType, AssetTypeEnum.COUPON.getCode());
            queryWrapper.eq(CashierMallAssetLog::getOperationType, OperationTypeEnum.USE.getCode());
            queryWrapper.eq(CashierMallAssetLog::getOptStatus, OperationStatusTypeEnum.SUCCESS.getCode());
            List<CashierMallAssetLog> useSuccessLogList = cashierMallAssetLogService.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(useSuccessLogList)) {
                throw new MallException(SystemErrorEnum.COUPON_USE_CHECK_REPEAT_USED_ERROR);
            }

            log.info("使用券上下文组装完毕:{}", JSON.toJSONString(couponContext));
            couponService.useCoupon(user, couponContext);
            cashierMallAssetLogService.saveBatch(logList);
            log.info("使用券成功:{}", JSON.toJSONString(logList));
        } catch (Exception e) {
            logList.forEach(log -> {
                log.setOptStatus(OperationStatusTypeEnum.FAIL.getCode());
                log.setOptFailMsg((e.getMessage() != null && e.getMessage().length() > 200) ? e.getMessage().substring(0, 200) : e.getMessage());
            });
            cashierMallAssetLogService.saveBatch(logList);
            log.info("使用券失败:{}", JSON.toJSONString(logList));
            throw e;
        }
    }

    private List<CashierMallAssetLog> buildLogs(AssetOperateReqDto req, CouponContext couponContext, AssetTypeEnum assetType, OperationTypeEnum opt, String bizId) {
        List<CashierMallAssetLog> logList = Lists.newArrayList();
        Date now = new Date();
        couponContext.getCouponNoList().forEach(couponNo -> {
            CashierMallAssetLog log = buildMallAssetLog(req.getStoreId(), req.getCustomerId(), req.getOrderNo(), null, assetType, opt, bizId, couponNo, now);
            logList.add(log);
        });
        return logList;
    }

    private CashierMallAssetLog buildMallAssetLog(String bjStoreId, String customerId,
                                                  String orderNo, String points, AssetTypeEnum assetType,
                                                  OperationTypeEnum opt, String bizId, String couponNo, Date now) {
        CashierMallAssetLog log = new CashierMallAssetLog();
        String idStr = IdLeaf.getDateId(mallAssetLogId);
        log.setId(idStr);
        log.setCouponNo(couponNo);
        log.setCreateTime(now);
        log.setUpdateTime(now);
        log.setBjStoreId(bjStoreId);
        log.setCustomerId(customerId);
        log.setAssetType(assetType.getCode());
        log.setOperationType(opt.getCode());
        log.setBizId(bizId);
        log.setOrderNo(orderNo);
        log.setOptStatus(OperationStatusTypeEnum.SUCCESS.getCode());
        switch (assetType) {
            case POINTS:
                log.setPoints(Long.valueOf(points));
                break;
            case COUPON:
                log.setCouponNo(couponNo);
                break;
            default:
                break;
        }
        return log;
    }

    @Override
    public ResponseResult<AssetUseRespDto> returnAsset(AssetOperateReqDto req) {
        String bizId = UUID.randomUUID().toString().replace("-", "");

        // 获取商户配置
        CashierMallStoreConfig storeConfig = getStoreConfig(req.getStoreId());
        CashierMallAssetConfig mallConfig = getMallConfig(storeConfig.getMallAssetConfigId());
        UserContext user = UserContext.builder().phone(req.getCustomerId()).mallConfig(mallConfig).storeConfig(storeConfig).build();
        log.info("获取商户的配置信息UserContext:{}", JSON.toJSONString(user));

        // 获取路由
        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getEnumByCode(Math.toIntExact(mallConfig.getApiPlatform()));
        AbstractCouponService couponService = couponServiceFactory.router(platformTypeEnum, PlatformCategoryTypeEnum.COUPON);
        AbstractPointsService pointsService = pointsServiceFactory.router(platformTypeEnum, PlatformCategoryTypeEnum.POINTS);

        boolean hasPoints = req.getPoints() != null && req.getPoints() > 0;
        boolean hasCoupon = CollectionUtils.isNotEmpty(req.getCouponNoList());
        log.info("是否使用积分:{}, 是否使用券:{}", hasPoints, hasCoupon);
        if (hasPoints && !hasCoupon) {
            justReturnPoints(req, couponService, user, bizId);
        } else if (!hasPoints && hasCoupon) {
            justReturnCoupon(req, couponService, user, bizId);
        } else {
            bothReturn(req, couponService, user, bizId);
        }
        return ResponseResult.success(AssetUseRespDto.builder().bizId(bizId).build());

    }

    private void bothReturn(AssetOperateReqDto req, AbstractCouponService couponService, UserContext user, String bizId) {
        throw new MallException(SystemErrorEnum.ASSET_USE_POINTS_CAN_NOT_SUPPORT_ERROR);
    }

    private void justReturnPoints(AssetOperateReqDto req, AbstractCouponService couponService, UserContext user, String bizId) {
        throw new MallException(SystemErrorEnum.ASSET_USE_POINTS_AND_COUPON_CAN_NOT_SUPPORT_ERROR);
    }

    private void justReturnCoupon(AssetOperateReqDto req, AbstractCouponService couponService, UserContext user, String bizId) {
        CouponContext couponContext = couponConvertor.reqDto2Context(req);
        // 日志
        List<CashierMallAssetLog> logList = buildLogs(req, couponContext, AssetTypeEnum.COUPON, OperationTypeEnum.RETURN, bizId);
        try {
            // 该是否能使用券资产
            if (Boolean.FALSE.equals(user.getMallConfig().getCouponCanUse())) {
                throw new MallException(SystemErrorEnum.COUPON_CONFIG_CAN_NOT_USE_ERROR);
            }

            Map<String, CashierMallAssetLog> vcode2LogMap = logList.stream().collect(Collectors.toMap(CashierMallAssetLog::getCouponNo, Function.identity(), (v1, v2) -> v1));
            couponContext.setVcode2LogMap(vcode2LogMap);

            // 获取核销券的记录
            LambdaQueryWrapper<CashierMallAssetLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CashierMallAssetLog::getOrderNo, req.getOrderNo());
            queryWrapper.eq(CashierMallAssetLog::getAssetType, AssetTypeEnum.COUPON.getCode());
            queryWrapper.eq(CashierMallAssetLog::getOptStatus, OperationStatusTypeEnum.SUCCESS.getCode());
            queryWrapper.in(CashierMallAssetLog::getCouponNo, req.getCouponNoList());
            List<CashierMallAssetLog> useSuccessLogList = cashierMallAssetLogService.list(queryWrapper);
            if (CollectionUtils.isEmpty(useSuccessLogList)) {
                throw new MallException(SystemErrorEnum.COUPON_RETURN_CHECK_USE_ALL_NOT_FOUND_ERROR);
            }

            // 根据券号分组、根据时间倒叙、取第一个，校验是否可以返还
            useSuccessLogList.stream().max(Comparator.comparing(CashierMallAssetLog::getCreateTime)).ifPresent(log -> {
                // 如果操作类型是使用，则允许返还，否则不允许。
                if (!OperationTypeEnum.USE.getCode().equals(log.getOperationType())) {
                    throw new MallException(SystemErrorEnum.COUPON_RETURN_CHECK_USE_PART_NOT_FOUND_ERROR);
                }
            });

            log.info("返还券上下文组装完毕:{}", JSON.toJSONString(couponContext));
            couponService.returnCoupon(user, couponContext);
            cashierMallAssetLogService.saveBatch(logList);
            log.info("返还券成功:{}", JSON.toJSONString(logList));
        } catch (Exception e) {
            logList.forEach(log -> {
                log.setOptStatus(OperationStatusTypeEnum.FAIL.getCode());
                log.setOptFailMsg((e.getMessage() != null && e.getMessage().length() > 200) ? e.getMessage().substring(0, 200) : e.getMessage());
            });
            cashierMallAssetLogService.saveBatch(logList);
            log.info("返还券失败:{}", JSON.toJSONString(logList));
            if (e instanceof MallException) {
                int code = ((MallException) e).getCode();
                if (code == SystemErrorEnum.COUPON_RETURN_CHECK_USE_ALL_NOT_FOUND_ERROR.getErrorCode()
                        || code == SystemErrorEnum.COUPON_RETURN_CHECK_USE_PART_NOT_FOUND_ERROR.getErrorCode()) {
                    log.info("没有核销记录，无法处理返还逻辑");
                    return;
                }
            }
            throw e;
        }
    }

    @Override
    public ResponseResult<MallConfigRespDto> getMallConfig(MallConfigReqDto req) {
        String vid = req.getVid();
        String storeId = req.getStoreId();
        CashierMallStoreConfig storeConfig;
        // 获取门店配置
        if (StringUtils.isBlank(storeId)) {
            LambdaQueryWrapper<JicMallOrg> jicMallOrgQueryWrapper = new LambdaQueryWrapper<>();
            jicMallOrgQueryWrapper.select(JicMallOrg::getVidCode);
            jicMallOrgQueryWrapper.eq(JicMallOrg::getVid, vid);
            jicMallOrgQueryWrapper.in(JicMallOrg::getVidType, Lists.newArrayList(2, 10));
            JicMallOrg jicMallOrg = jicMallOrgMapper.selectOne(jicMallOrgQueryWrapper);
            if (jicMallOrg == null || StringUtils.isBlank(jicMallOrg.getVidCode())) {
                log.info("根据VID获取不到门店,设置为默认门店");
                storeId = DEFAULT_BJ_CODE;
            } else {
                log.info("根据VID获取不到门店CODE:[{}]", jicMallOrg.getVidCode());
                storeId = jicMallOrg.getVidCode();
            }
        }
        // 获取联域配置，如果不存在，则需要取默认商户配置返回。因为前端没有办法获取到CODE
        storeConfig = storeConfigMapper.selectByStoreId(storeId);
        if (storeConfig == null || storeConfig.getMallAssetConfigId() == null) {
            log.info("联域门店不存在，走默认查询");
            storeConfig = storeConfigMapper.selectOne(new LambdaQueryWrapper<CashierMallStoreConfig>().eq(CashierMallStoreConfig::getBjStoreId, DEFAULT_BJ_CODE));
        }

        MallConfigRespDto dto;
        // 非联域商场，默认配置取支付商户和伯俊CODE
        if (storeConfig.getMallAssetConfigId() == null) {
            dto = mallConvertor.storeConfig2RespDto(storeConfig, null);
            dto.setStoreId(storeId);
            processPayConfig(dto, DEFAULT_BJ_CODE, req.getBusinessType(), req.getPayChannel());
        } else {
            CashierMallAssetConfig mallConfig = getMallConfig(storeConfig.getMallAssetConfigId());
            dto = mallConvertor.storeConfig2RespDto(storeConfig, mallConfig);
            dto.setHasLinkStore(true);
            processPayConfig(dto, storeId, req.getBusinessType(), req.getPayChannel());
        }
        return ResponseResult.success(dto);
    }

    private void processPayConfig(MallConfigRespDto dto, String storeId, Integer businessType, Integer payChannel) {
        PayConfigReqDto payConfigReqDto = new PayConfigReqDto(storeId, null, businessType == null ? PayBusinessTypeEnum.WXMALL.getCode() : businessType, payChannel);
        ResponseResult<List<PayConfigRespDto>> payResponse = this.listPayConfig(payConfigReqDto);
        dto.setPayConfigList(payResponse.getData());
    }

    @Override
    public ResponseResult<PointsInfoRespDto> getPoints(BaseReq req) {
        // 获取配置
        CashierMallStoreConfig storeConfig = getStoreConfig(req.getStoreId());
        CashierMallAssetConfig mallConfig = getMallConfig(storeConfig.getMallAssetConfigId());
        PointsInfoRespDto dto = mallConvertor.storeConfig2PointsRespDto(storeConfig);

        // 获取积分总额
        Integer point = 0;
        dto.setCanUsePoints(point);
        return ResponseResult.success(dto);
    }

    @Override
    public Boolean isMallMember(BaseReq req) {
        MemberRegisterReq memberReq = new MemberRegisterReq();
        memberReq.setMobile(req.getCustomerId());
        memberReq.setCustomerId(req.getCustomerId());
        memberReq.setStoreId(req.getStoreId());
        try {
            MallConfigReqDto mallReq = new MallConfigReqDto();
            mallReq.setStoreId(req.getStoreId());
            ResponseResult<MallConfigRespDto> mallConfig = getMallConfig(mallReq);
            MallConfigRespDto data = mallConfig.getData();
            if (data != null && Boolean.TRUE.equals(data.getHasOpenCardDefault())) {
                log.info("命中静默开卡，直接认为是会员");
                return true;
            }

            ResponseResult responseResult = iMemberBizService.queryMember(memberReq);
            if (responseResult == null) {
                log.info("商场未提供接口，直接认为是会员");
                return true;
            }
            if (responseResult.getData() != null) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("是否是商场会员异常:", e);
            return false;
        }
    }

    @Override
    public Boolean isChosePrivacy(BaseReq req) {
        CashierMallMemberLog cashierMallMemberLog = null;
        // 部分门店只需要校验一次开卡，有任一门店的勾选记录则认为已勾选。
        if (LONG_CODE_LIST.contains(req.getStoreId())) {
            log.info("当前查询的门店CODE[{}],进入龙湖门店判断逻辑，查询以下CODE是否有勾选协议的记录：{}", req.getStoreId(), JSON.toJSONString(LONG_CODE_LIST));
            List<CashierMallMemberLog> cashierMallMemberLogs = cashierMallMemberLogMapper.selectList(
                    new LambdaQueryWrapper<CashierMallMemberLog>()
                            .select(CashierMallMemberLog::getId)
                            .eq(CashierMallMemberLog::getMobile, req.getCustomerId())
                            .in(CashierMallMemberLog::getBjStoreId, LONG_CODE_LIST)
                            .eq(CashierMallMemberLog::getHasChosePrivacy, true));
            log.info("当前查询的门店CODE[{}],进入龙湖门店判断逻辑，查询是否有勾选协议的记录：{}", req.getStoreId(), JSON.toJSONString(cashierMallMemberLogs));
            if (CollectionUtils.isNotEmpty(cashierMallMemberLogs)) {
                cashierMallMemberLog = cashierMallMemberLogs.get(0);
            }
        } else {
            // 本地会员记录查询
            cashierMallMemberLog = cashierMallMemberLogMapper.selectOne(
                    new LambdaQueryWrapper<CashierMallMemberLog>()
                            .select(CashierMallMemberLog::getId)
                            .eq(CashierMallMemberLog::getMobile, req.getCustomerId())
                            .eq(CashierMallMemberLog::getBjStoreId, req.getStoreId())
                            .eq(CashierMallMemberLog::getHasChosePrivacy, true)
            );
        }
        return cashierMallMemberLog != null;
    }

    @Override
    public ResponseResult<Boolean> submitChosePrivacy(BaseReq req) {
        // 查询是否已经存在，不存在则插入，存在则更新（静默开卡记录）
        CashierMallMemberLog cashierMallMemberLog = cashierMallMemberLogMapper.selectOne(
                new LambdaQueryWrapper<CashierMallMemberLog>()
                        .select(CashierMallMemberLog::getId)
                        .eq(CashierMallMemberLog::getMobile, req.getCustomerId())
                        .eq(CashierMallMemberLog::getBjStoreId, req.getStoreId())
        );
        Date date = new Date();
        if (cashierMallMemberLog == null) {
            cashierMallMemberLog = new CashierMallMemberLog();
            cashierMallMemberLog.setId(cashierMallMemberLogMapper.getMaxId());
            cashierMallMemberLog.setIsDelete(0);
            cashierMallMemberLog.setCreateTime(date);
            cashierMallMemberLog.setUpdateTime(date);
            cashierMallMemberLog.setIsExecute("Y");
            cashierMallMemberLog.setHasChosePrivacy(true);
            cashierMallMemberLog.setMobile(req.getCustomerId());
            cashierMallMemberLog.setBjStoreId(req.getStoreId());
            cashierMallMemberLogMapper.insert(cashierMallMemberLog);
            log.info("新增勾选隐私政策记录");
        } else {
            // 更新选择标识
            CashierMallMemberLog updateLog = new CashierMallMemberLog();
            updateLog.setId(cashierMallMemberLog.getId());
            updateLog.setHasChosePrivacy(true);
            updateLog.setUpdateTime(date);
            cashierMallMemberLogMapper.updateById(updateLog);
            log.info("更新开卡记录的勾选隐私政策标识");
        }
        return ResponseResult.success(true);
    }

    @Override
    public ResponseResult<AssetUseRespDto> sendCoupon(CouponSendReqDto req) {
        // 获取商户配置
        CashierMallStoreConfig storeConfig = getStoreConfig(req.getStoreId());
        CashierMallAssetConfig mallConfig = getMallConfig(storeConfig.getMallAssetConfigId());
        String bizId = req.getBizId();
        if (storeConfig.getPlatformAppId() != null) {
            bizId = req.getBizId() + storeConfig.getPlatformAppId();
        }

        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getEnumByCode(Math.toIntExact(mallConfig.getApiPlatform()));
        AbstractCouponService abstractCouponService = couponServiceFactory.router(platformTypeEnum, PlatformCategoryTypeEnum.COUPON);
        UserContext user = UserContext.builder().phone(req.getCustomerId()).mallConfig(mallConfig).storeConfig(storeConfig).build();
        List<CashierMallAssetLog> logList = new ArrayList<>();
        try {
            // 获取一下是否已经发放过
            LambdaQueryWrapper<CashierMallAssetLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(CashierMallAssetLog::getId);
            queryWrapper.eq(CashierMallAssetLog::getTemplateNo, mallConfig.getParkingCouponTemplateNo());
            queryWrapper.eq(CashierMallAssetLog::getCustomerId, req.getCustomerId());
            queryWrapper.eq(CashierMallAssetLog::getBjStoreId, req.getStoreId());
            queryWrapper.eq(CashierMallAssetLog::getOptStatus, OperationStatusTypeEnum.SUCCESS.getCode());
            queryWrapper.eq(CashierMallAssetLog::getIsDelete, 0);
            queryWrapper.last("and rownum = 1");
            CashierMallAssetLog sendLog = cashierMallAssetLogService.getOne(queryWrapper);
            if (sendLog != null) {
                throw new MallException("该用户已发放，请勿重复发放");
            }

            String parkingCouponTemplateNo = mallConfig.getParkingCouponTemplateNo();
            if (Boolean.FALSE.equals(mallConfig.getHasUseParkingCoupon())) {
                throw new MallException(SystemErrorEnum.COUPON_SEND_CHECK_CAN_NOT_USE_ERROR);
            }
            CouponContext couponContext = CouponContext.builder().traceId(bizId).templateNo(parkingCouponTemplateNo).build();
            log.info("发券 上下文组装完毕:{}", JSON.toJSONString(couponContext));
            abstractCouponService.sendCoupon(user, couponContext);

            if (CollectionUtils.isNotEmpty(couponContext.getCouponNoList())) {
                List<String> couponNoList = couponContext.getCouponNoList();
                for (String coupon : couponNoList) {
                    // 停车券，目前使用固定数据，后续可拓展其他类型
                    CashierMallAssetLog couponLog = buildMallAssetLog(req.getStoreId(), req.getCustomerId(),
                            null, null, AssetTypeEnum.COUPON, OperationTypeEnum.SEND, bizId, null, new Date());
                    couponLog.setTemplateNo(mallConfig.getParkingCouponTemplateNo());
                    couponLog.setTemplateAmount(mallConfig.getParkingCouponAmount());
                    couponLog.setCouponNo(coupon);
                    logList.add(couponLog);
                }
            }
            cashierMallAssetLogService.saveBatch(logList);
            log.info("发券 成功:{}", JSON.toJSONString(logList));
        } catch (Exception e) {
            // 停车券，目前使用固定数据，后续可拓展其他类型
            CashierMallAssetLog couponLog = buildMallAssetLog(req.getStoreId(), req.getCustomerId(),
                    null, null, AssetTypeEnum.COUPON, OperationTypeEnum.SEND, bizId, null, new Date());
            couponLog.setTemplateNo(mallConfig.getParkingCouponTemplateNo());
            couponLog.setTemplateAmount(mallConfig.getParkingCouponAmount());
            couponLog.setOptStatus(OperationStatusTypeEnum.FAIL.getCode());
            couponLog.setOptFailMsg((e.getMessage() != null && e.getMessage().length() > 200) ? e.getMessage().substring(0, 200) : e.getMessage());
            logList.add(couponLog);
            cashierMallAssetLogService.saveBatch(logList);
            log.info("发券 失败:{}", JSON.toJSONString(logList));
            throw e;
        }
        return ResponseResult.success(AssetUseRespDto.builder().bizId(bizId).build());
    }

    @Override
    public String vid2StoreCode(String vid) {
        String storeId = null;
        // 获取门店配置
        LambdaQueryWrapper<JicMallOrg> jicMallOrgQueryWrapper = new LambdaQueryWrapper<>();
        jicMallOrgQueryWrapper.select(JicMallOrg::getVidCode);
        jicMallOrgQueryWrapper.eq(JicMallOrg::getVid, vid);
        jicMallOrgQueryWrapper.in(JicMallOrg::getVidType, Lists.newArrayList(2, 10));
        JicMallOrg jicMallOrg = jicMallOrgMapper.selectOne(jicMallOrgQueryWrapper);
        if (jicMallOrg == null || StringUtils.isBlank(jicMallOrg.getVidCode())) {
            log.info("根据VID获取不到门店,设置为默认门店");
            storeId = DEFAULT_BJ_CODE;
        } else {
            log.info("根据VID获取不到门店CODE:[{}]", jicMallOrg.getVidCode());
            storeId = jicMallOrg.getVidCode();
        }
        return storeId;
    }

    @Override
    public ResponseResult<List<PayConfigRespDto>> listPayConfig(PayConfigReqDto req) {
        // TODO 下个版本需要下线老版本支付
        if (1 == payConfigChangeSwitch) {
            return listPayConfigV2(req);
        } else {
            log.info("门店支付方式配置获取 入参:{}", JSON.toJSONString(req));
            // 根据门店查询信息
            List<CashierPaymentConfig> dbConfigList = ((AssetBizServiceImpl) AopContext.currentProxy()).listCashierPayConfig(req.getStoreId());
            log.info("门店支付方式配置获取 回参:{}", JSON.toJSONString(dbConfigList));

            List<CashierPaymentConfig> cashierPaymentConfigs = Optional.ofNullable(dbConfigList)
                    .orElse(Lists.newArrayList()).stream()
                    .filter(config -> filterPayConfig(req, config))
                    .collect(Collectors.toList());
            // 如果查询数据为空，并且门店不是默认的时候，则需要用默认数据补充查询一次。避免业务方逻辑出错。
            if (CollectionUtils.isEmpty(cashierPaymentConfigs) && !DEFAULT_BJ_CODE.equals(req.getStoreId())) {
                req.setStoreId(DEFAULT_BJ_CODE);
                return listPayConfig(req);
            }
            List<PayConfigRespDto> respDtos = mallConvertor.payConfig2Dto(cashierPaymentConfigs);
            return ResponseResult.success(respDtos);
        }
    }

    private ResponseResult<List<PayConfigRespDto>> listPayConfigV2(PayConfigReqDto req) {
        log.info("门店支付方式配置获取-V2 入参:{}", JSON.toJSONString(req));
        // 根据门店查询信息
        List<CashierPayConfig> dbConfigList = ((AssetBizServiceImpl) AopContext.currentProxy()).listCashierPayConfigV2(req.getStoreId());
        log.info("门店支付方式配置获取-V2 回参:{}", JSON.toJSONString(dbConfigList));

        List<CashierPayConfig> cashierPayConfigs = Optional.ofNullable(dbConfigList)
                .orElse(Lists.newArrayList()).stream()
                .filter(config -> filterPayConfigV2(req, config))
                .collect(Collectors.toList());
        // 如果查询数据为空，并且门店不是默认的时候，则需要用默认数据补充查询一次。避免业务方逻辑出错。
        if (CollectionUtils.isEmpty(cashierPayConfigs) && !DEFAULT_BJ_CODE.equals(req.getStoreId())) {
            req.setStoreId(DEFAULT_BJ_CODE);
            return listPayConfigV2(req);
        }
        List<PayConfigRespDto> respDtos = mallConvertor.payConfig2DtoV2(cashierPayConfigs);
        return ResponseResult.success(respDtos);
    }

    private boolean filterPayConfigV2(PayConfigReqDto req, CashierPayConfig config) {
        boolean businessTypeIsTrue = false;
        boolean payChannelIsTrue = false;
        if (req.getPayChannel() == null || req.getPayChannel() == config.getPayChannel()) {
            payChannelIsTrue = true;
        }
        if (req.getBusinessType() == config.getBusinessType()) {
            businessTypeIsTrue = true;
        }
        if (businessTypeIsTrue && payChannelIsTrue) {
            return true;
        }
        return false;
    }

    private boolean filterPayConfig(PayConfigReqDto req, CashierPaymentConfig config) {
        boolean isTrue = false;
        boolean payChannelIsTrue = false;
        if (req.getPayChannel() == null || req.getPayChannel() == config.getPayChannel()) {
            payChannelIsTrue = true;
        }
        PayBusinessTypeEnum code = PayBusinessTypeEnum.getByCode(req.getBusinessType());
        switch (code) {
            case WXMALL:
                if (Boolean.TRUE.equals(config.getHasWxmall()) && payChannelIsTrue) {
                    isTrue = true;
                }
                break;
            case BOX:
                if (Boolean.TRUE.equals(config.getHasBox()) && payChannelIsTrue) {
                    isTrue = true;
                }
                break;
            case POS:
                if (Boolean.TRUE.equals(config.getHasPos()) && payChannelIsTrue) {
                    isTrue = true;
                }
                break;
            case POS_ONLINE:
                if (Boolean.TRUE.equals(config.getHasPosOnline()) && payChannelIsTrue) {
                    isTrue = true;
                }
                break;
            case STORE_CARD:
                if (Boolean.TRUE.equals(config.getHasStoreCard()) && payChannelIsTrue) {
                    isTrue = true;
                }
                break;
            case REPURCHASE:
                if (Boolean.TRUE.equals(config.getHasWxmall()) && payChannelIsTrue && DEFAULT_BJ_CODE.equals(req.getStoreId())) {
                    isTrue = true;
                }
                break;
            default:
                break;
        }
        return isTrue;
    }

    @Cacheable(cacheNames = "mall-asset-redis-cache", key = "'listCashierPayConfig:'+#storeId")
    public List<CashierPaymentConfig> listCashierPayConfig(String storeId) {
        log.info("未命中缓存 获取门店支付配置 入参:{}", JSON.toJSONString(storeId));
        LambdaQueryWrapper<CashierPaymentConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashierPaymentConfig::getIsDelete, false)
                .eq(CashierPaymentConfig::getBjStoreId, storeId);
        List<CashierPaymentConfig> dbConfigList = cashierPaymentConfigMapper.selectList(queryWrapper);
        log.info("未命中缓存 获取门店支付配置 回参:{}", JSON.toJSONString(dbConfigList));
        return dbConfigList;
    }

    @Caching(evict = {
            @CacheEvict(cacheNames = "mall-asset-redis-cache", key = "'listCashierPayConfig:'+#storeId"),
            @CacheEvict(cacheNames = "mall-asset-redis-cache", key = "'listCashierPayConfigV2:'+#storeId"),
    })
    @Override
    public ResponseResult flushPayConfigCache(String storeId) {
        log.info("刷新门店支付方式配置 成功");
        return ResponseResult.success();
    }

    @Cacheable(cacheNames = "mall-asset-redis-cache", key = "'listCashierPayConfigV2:'+#storeId")
    public List<CashierPayConfig> listCashierPayConfigV2(String storeId) {
        log.info("未命中缓存 获取门店支付配置 V2 入参:{}", JSON.toJSONString(storeId));
        LambdaQueryWrapper<CashierPayConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashierPayConfig::getIsDelete, false)
                .eq(CashierPayConfig::getBjStoreId, storeId);
        List<CashierPayConfig> dbConfigList = cashierPayConfigMapper.selectList(queryWrapper);
        log.info("未命中缓存 获取门店支付配置 V2 回参:{}", JSON.toJSONString(dbConfigList));
        return dbConfigList;
    }


}
