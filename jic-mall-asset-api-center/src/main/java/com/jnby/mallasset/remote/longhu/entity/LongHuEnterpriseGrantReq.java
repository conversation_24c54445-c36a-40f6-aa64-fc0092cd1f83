package com.jnby.mallasset.remote.longhu.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class LongHuEnterpriseGrantReq {


    @ApiModelProperty(value = "请求号，调用方生成，保证唯一")
    private String requestNo;

    @ApiModelProperty(value = "企业编号")
    private String enterpriseNo;

    @ApiModelProperty(value = "发放类型")
    private String grantType;

    @ApiModelProperty(value = "业务系统ID")
    private String appId;

    @ApiModelProperty(value = "手机号")
    private String phoneNo;

    @ApiModelProperty(value = "发放金额 发放的珑珠数量，单位为珑珠，一位小数（1元=10珑珠）")
    private BigDecimal grantAmount;

    @ApiModelProperty(value = "请求时间戳")
    private Long timestamp;

}
