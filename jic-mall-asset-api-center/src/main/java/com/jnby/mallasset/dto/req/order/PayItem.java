package com.jnby.mallasset.dto.req.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel("付款明细")
public class PayItem implements Serializable {

    @ApiModelProperty(value = "付款ID")
    private Long id;
    @ApiModelProperty(value = "支付方式名称")
    private String name;
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payamount;
}
