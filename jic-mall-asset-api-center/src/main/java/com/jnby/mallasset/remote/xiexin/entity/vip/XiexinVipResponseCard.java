package com.jnby.mallasset.remote.xiexin.entity.vip;

import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Root;

import java.util.List;

@Data
@Root(name = "vipCard" )
public class XiexinVipResponseCard {

    @Element(name = "CardId" , required = false)
    private String cardId;
    @Element(name = "CardCode" , required = false)
    private String cardCode;
    @Element(name = "VipName" , required = false)
    private String vipName;
    @Element(name = "CardTypeId" , required = false)
    private Integer cardTypeId;
    @Element(name = "CardTypeName" , required = false)
    private String cardTypeName;
    @Element(name = "CanCent" , required = false)
    private boolean canCent;
    @Element(name = "CanOwnCoupon" , required = false)
    private boolean canOwnCoupon;
    @Element(name = "CanDisc" , required = false)
    private boolean canDisc;
    @Element(name = "ValidCent" , required = false)
    private Double validCent;
    @Element(name = "Hello" , required = false)
    private String hello;
    @Element(name = "QRCode" , required = false)
    private String qrCode;
    @Element(name = "Birthday" , required = false)
    private String birthday;
    @Element(name = "SexType" , required = false)
    private Integer sexType;
    @Element(name = "Mobile" , required = false)
    private String mobile;
    @Element(name = "Address" , required = false)
    private String address;
    @Element(name = "EMail" , required = false)
    private String email;
    @Element(name = "PlateNO" , required = false)
    private String plateNO;
    @Element(name = "OpenCardDate" , required = false)
    private String openCardDate;
    @Element(name = "photo" , required = false)
    private String photo;
    @Element(name = "idcard_code" , required = false)
    private String idcardCode;
    @Element(name = "vippoint" , required = false)
    private XiexinVipPoint vippoint;
}
