package com.jnby.mallasset.api.dto.asset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("商场配置")
public class MallConfigRespDto {
    @ApiModelProperty(value = "伯俊CODE")
    private String storeId;
    @ApiModelProperty(value = "商场ID")
    private String platformMallId;
    @ApiModelProperty(value = "商场名称")
    private String mallName;
    @ApiModelProperty(value = "商场门店ID")
    private String platformMallStoreId;
    @ApiModelProperty(value = "商场门店名称")
    private String mallStoreName;
    @ApiModelProperty(value = "平台APP_ID/")
    private String platformAppId;
    @ApiModelProperty(value = "积分商品条码/货号等信息")
    private String integralSkuCode;
    @ApiModelProperty(value = "支付平台：1微信直连 2收钱吧 3支付宝直连 4电银")
    private String payChannel;
    @ApiModelProperty(value = "积分换算比例。1积分换算多少元。例如0.01=1积分兑换0.01元")
    private BigDecimal pointsDeductionScale;
    @ApiModelProperty(value = "单订单积分抵扣起扣门槛")
    private Long pointsDeductionThreshold;
    @ApiModelProperty(value = "单订单积分抵扣上限")
    private Long pointsDeductionUpperLimit;
    @ApiModelProperty(value = "单订单积分抵扣阶梯单位。100代表积分每满100才可以抵扣计算")
    private Long pointsDeductionLadder;
    @ApiModelProperty(value = "微信直连的商户ID")
    private String mchId;
    @ApiModelProperty(value = "是否静默开卡")
    private Boolean hasOpenCardDefault;
    @ApiModelProperty(value = "是否外跳开卡")
    private Boolean hasOpenCardJumpOut;
    @ApiModelProperty(value = "外跳开卡地址")
    private String openCardJumpUrl;
    @ApiModelProperty(value = "外跳开卡appId")
    private String jumpOutAppId;
    @ApiModelProperty(value = "开卡提示文案。如果没有使用原来的逻辑取值，如果有则需要覆盖")
    private String openCardSubTitle;
    @ApiModelProperty(value = "开卡开关是否显示。默认true,如果数据库配置了不展示则返回false")
    private Boolean hasOpenCardButton = true;
    @ApiModelProperty(value = "商场LOGO")
    private String mallLogoUrl;

    @ApiModelProperty(value = "积分增加类型: 1=无感积分、2=订单手动积分")
    private Integer pointsIncrementType;
    @ApiModelProperty(value = "是否有积分抵扣")
    private Boolean pointsCanUse;
    @ApiModelProperty(value = "积分名称")
    private String pointsName;
    @ApiModelProperty(value = "是否使用商场券")
    private Boolean couponCanUse;
    @ApiModelProperty(value = "使用规则")
    private String useRuleContent;
    @ApiModelProperty(value = "使用规则：0=没有，1=有")
    private Boolean hasUseRule;
    @ApiModelProperty(value = "隐私协议：0=无，1=有")
    private Boolean hasPrivacy;
    @ApiModelProperty(value = "隐私内容")
    private String privacyContent;
    @ApiModelProperty(value = "商场品牌名称")
    private String mallBrandName;

    @ApiModelProperty(value = "是否有联域协议")
    private Boolean hasLinkAgreement;
    @ApiModelProperty(value = "联域协议内容")
    private String linkAgreementContent;
    @ApiModelProperty(value = "券资产使用规则")
    private String couponUseRule;
    @ApiModelProperty(value = "联域协议标题")
    private String linkAgreementTitle;
    @ApiModelProperty(value = "使用规则标题")
    private String useRuleTitle;
    @ApiModelProperty(value = "隐私协议标题")
    private String privacyTitle;
    @ApiModelProperty(value = "券资产使用规则标题")
    private String couponUseRuleTitle;

    @ApiModelProperty(value = "是否联域收银（显示开卡和资产逻辑）")
    private Boolean hasLinkCashier;
    @ApiModelProperty(value = "是否联域门店")
    private Boolean hasLinkStore = false;
    @ApiModelProperty(value = "门店收款方式：1=自收银、自退款，2=商场收银、自退款，3=商场收银、商场退款，4=华润自研")
    private Integer collectionType;
    @ApiModelProperty(value = "支付方式列表")
    private List<PayConfigRespDto> payConfigList;

    @ApiModelProperty(value = "使用资产后是否整单退。true=整单退，false=部分退")
    private Boolean useAssetOneTimeRefund = false;
}
