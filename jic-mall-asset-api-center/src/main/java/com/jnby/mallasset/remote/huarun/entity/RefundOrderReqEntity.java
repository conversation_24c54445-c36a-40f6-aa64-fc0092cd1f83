package com.jnby.mallasset.remote.huarun.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class RefundOrderReqEntity {
    @ApiModelProperty("数据交换平台交换ID")
    @JSONField(ordinal = 1)
    private String exchangeId;
    @ApiModelProperty("数字化交易订单号")
    @JSONField(ordinal = 2)
    private String orderNo;
    @ApiModelProperty("网商平台订单号")
    @JSONField(ordinal = 3)
    private String ordNum;
    @ApiModelProperty("是否隔日退, 0：否 1：是")
    @JSONField(ordinal = 4)
    private int refundDayReturnFlag;
    @ApiModelProperty("退款总金额，单位(分)")
    @JSONField(ordinal = 5)
    private Integer refundAmount;
    @ApiModelProperty("退款需要的万象会员手机号")
    @JSONField(ordinal = 6)
    private String memberTel;
    @ApiModelProperty("退货单号")
    @JSONField(ordinal = 7)
    private String refundNo;
    @ApiModelProperty("第三方订单号")
    @JSONField(ordinal = 8)
    private String ordPayNo;
    @ApiModelProperty("第三方平台退款用户标识，如微信OPENID")
    @JSONField(ordinal = 9)
    private String ordOpenId;
    @ApiModelProperty("退款原因")
    @JSONField(ordinal = 10)
    private String ordReason;
    @ApiModelProperty("商品信息")
    @JSONField(ordinal = 11)
    private List<OrdItem> ordItem;
    @ApiModelProperty("异步回调地址")
    @JSONField(ordinal = 12)
    private String notifyUrl;
    @ApiModelProperty("微信退款单号")
    @JSONField(ordinal = 13)
    private String refundWxNum;
    @ApiModelProperty(value = "订单完成时间")
    @JSONField(ordinal = 14)
    private Long ordFinishTime;
    @ApiModelProperty("付款信息")
    @JSONField(ordinal = 15)
    private List<PayItems> payItem;
    @ApiModelProperty("门店编码")
    @JSONField(ordinal = 16)
    private String storeCode;
}
