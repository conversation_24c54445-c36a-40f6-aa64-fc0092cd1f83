package com.jnby.mallasset.strategy.context;

import com.jnby.mallasset.module.model.CashierMallAssetLog;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 券上下文
 */
@Builder
@Data
public class CouponContext {
    /**
     * 流水ID
     */
    private String traceId;
    /**
     * 券号列表
     */
    private List<String> couponNoList;
    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 券号:日志对象
     */
    Map<String, CashierMallAssetLog> vcode2LogMap;
    /**
     * 模版编号
     */
    private String templateNo;
}
