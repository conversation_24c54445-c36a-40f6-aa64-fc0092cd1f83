package com.jnby.mallasset.strategy.platform.xiexin;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.api.vo.MemberInfoRespVo;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.remote.xiexin.IXiexinRemoteHttpApi;
import com.jnby.mallasset.remote.xiexin.IXiexinSoapRemoteHttpApi;
import com.jnby.mallasset.remote.xiexin.entity.BaseXiexinResp;
import com.jnby.mallasset.remote.xiexin.entity.vip.*;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.DateUtil;
import com.jnby.mallasset.util.Md5Util;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 大融城会员平台策略
 */
@Service
@AllArgsConstructor
@Slf4j
public class XiexinVIpInfoService {

    private IXiexinSoapRemoteHttpApi xiexinSoapRemoteHttpApi;

    public MemberInfoRespVo getXieXinMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef){
        MemberInfoRespVo memberInfoRespVo = null;

        String platformPublicKey = cashierMallAssetStoreRef.getPlatformPublicKey();
        String[] pubKeys = platformPublicKey.split("-");
        String userId = pubKeys[0];
        String password = pubKeys[1];
        String codeStr = cashierMallAssetStoreRef.getIntegralSkuCode();
        String[] items = codeStr.split(",");
        String[] condTypes = items[0].split("-");

        try{
            XiexinVipSoapEnvelope req = getXiexinVipSoapEnvelope(memberRegisterReq, userId, password, condTypes);
            Response<XiexinVipResponseSoapEnvelope> execute = xiexinSoapRemoteHttpApi.getVipCard(req).execute();
            XiexinVipResponseSoapEnvelope resBody = execute.body();
            XiexinVipResponseResult vipResponseResult = resBody.getBody().getVipResponseResult();
            if(vipResponseResult != null && vipResponseResult.isVipCardResult()){
                XiexinVipResponseCard vipCard = vipResponseResult.getVipCard();
                int vipId = Integer.parseInt(vipCard.getCardId());
                // 查询会员标签
                XiexinVipTagSoapEnvelope request = getXiexinVipTagSoapEnvelope(userId, password, vipId);

                Response<XiexinVipTagResponseSoapEnvelope> executeModel = xiexinSoapRemoteHttpApi.getVipLableList(request).execute();
                XiexinVipTagResponseSoapBody vipTagResponseSoapBody = executeModel.body().getBody();
                XiexinVipTagResponseResult orderPosResponseResult = vipTagResponseSoapBody.getOrderPosResponseResult();
                if(orderPosResponseResult != null && orderPosResponseResult.isGetVipCardResult()){
                    XiexinVipTagItem xiexinVipTagItem = orderPosResponseResult.getLableitem();
                    List<XiexinVipTagItemDetail> lableitemList = xiexinVipTagItem.getCrmVipLableMX();
                    if(!lableitemList.isEmpty()){
                        lableitemList = lableitemList.stream().filter(x -> x.getLabellbid() == 11).collect(Collectors.toList());
                        XiexinVipTagItemDetail crmVipLableMX = lableitemList.get(0);
                        memberInfoRespVo = new MemberInfoRespVo();
                        memberInfoRespVo.setCardName(crmVipLableMX.getGzmc());
                        memberInfoRespVo.setTel(memberRegisterReq.getMobile());
                        memberInfoRespVo.setUserId(String.valueOf(crmVipLableMX.getHyid()));
                        memberInfoRespVo.setCardNo(String.valueOf(crmVipLableMX.getHyid()));
                        memberInfoRespVo.setCardTypeCode(String.valueOf(crmVipLableMX.getLabellx()));
                        memberInfoRespVo.setCardTypeID(String.valueOf(crmVipLableMX.getJlbh()));
                    }
                }
            }

        }catch (Exception e){
            log.error("协信南坪获取会员卡[{}]信息失败",memberRegisterReq.getMobile(),e);
        }
        return memberInfoRespVo;
    }

    private static XiexinVipTagSoapEnvelope getXiexinVipTagSoapEnvelope(String userId, String password, int vipId) {
        XiexinVipTagSoapEnvelope request = new XiexinVipTagSoapEnvelope();
        XiexinVipTagSoapHeader tagHeader = new XiexinVipTagSoapHeader();
        XiexinVipTagRequestHeader tsgRequestHeader = new XiexinVipTagRequestHeader();
        tsgRequestHeader.setUserId(userId);
        tsgRequestHeader.setPassword(password);
        tagHeader.setRequestHeader(tsgRequestHeader);
        request.setHeader(tagHeader);

        XiexinVipTagSoapBody tagBody = new XiexinVipTagSoapBody();
        XiexinVipTagRequestModel tagRequestModel = new XiexinVipTagRequestModel();
        tagRequestModel.setJlbh(0);
        tagRequestModel.setVipid(vipId);
        tagBody.setRequestModel(tagRequestModel);
        request.setBody(tagBody);
        return request;
    }

    private static XiexinVipSoapEnvelope getXiexinVipSoapEnvelope(MemberRegisterReq memberRegisterReq, String userId, String password, String[] condTypes) {
        XiexinVipSoapEnvelope req = new XiexinVipSoapEnvelope();
        XiexinVipSoapHeader header = new XiexinVipSoapHeader();
        XiexinVipRequestHeader requestHeader = new XiexinVipRequestHeader();
        requestHeader.setUserId(userId);
        requestHeader.setPassword(password);
        header.setRequestHeader(requestHeader);
        req.setHeader(header);

        XiexinVipSoapBody body = new XiexinVipSoapBody();
        XiexinVipRequestModel requestModel = new XiexinVipRequestModel();
        requestModel.setCondType(condTypes[1]);
        requestModel.setCondValue(memberRegisterReq.getMobile());
        body.setRequestModel(requestModel);
        req.setBody(body);
        return req;
    }
}
