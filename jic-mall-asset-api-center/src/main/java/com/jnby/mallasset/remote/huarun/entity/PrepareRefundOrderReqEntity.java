package com.jnby.mallasset.remote.huarun.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PrepareRefundOrderReqEntity {
    @ApiModelProperty("数据交换平台交换ID")
    @JSONField(ordinal = 1)
    private String exchangeId;
    @ApiModelProperty("数字化交易订单号")
    @JSONField(ordinal = 2)
    private String orderNo;
    @ApiModelProperty("退款需要的万象会员手机号")
    @JSONField(ordinal = 3)
    private String memberTel;
    @ApiModelProperty("第三方订单号")
    @JSONField(ordinal = 4)
    private String ordPayNo;
    @ApiModelProperty("商品信息")
    @JSONField(ordinal = 5)
    private List<OrdItem> ordItem;
    @ApiModelProperty("异步回调地址")
    @JSONField(ordinal = 6)
    private String notifyUrl;
    @ApiModelProperty("退货单号")
    @JSONField(ordinal = 7)
    private String refundNo;
    @ApiModelProperty("退款金额")
    @JSONField(ordinal = 8)
    private Integer refundAmount;
}
