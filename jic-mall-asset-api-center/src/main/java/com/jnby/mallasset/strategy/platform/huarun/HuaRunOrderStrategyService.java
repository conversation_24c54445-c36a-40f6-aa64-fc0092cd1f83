package com.jnby.mallasset.strategy.platform.huarun;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.RequestAddressProperties;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.constant.MallConfigConstant;
import com.jnby.mallasset.constant.MallUrlConstant;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.dto.req.order.ProductItem;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.remote.huarun.IHuaRunRefundRemoteHttpApi;
import com.jnby.mallasset.remote.huarun.IHuaRunRemoteHttpApi;
import com.jnby.mallasset.remote.huarun.entity.*;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 华润订单平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.HUA_RUN, category = PlatformCategoryTypeEnum.ORDER)
@Slf4j
@AllArgsConstructor
public class HuaRunOrderStrategyService extends AbstractOrderService {

    private IHuaRunRemoteHttpApi huaRunRemoteHttpApi;
    private CashierMallOrderLogMapper cashierMallOrderLogMapper;
    private RequestAddressProperties requestAddressProperties;
    private HuaRunLoginService huaRunLoginService;

    private IHuaRunRefundRemoteHttpApi huaRunRefundRemoteHttpApi;

    @Override
    public ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("华润订单同步输入参数：{}", JSON.toJSONString(orderConsumeReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderConsumeReq, cashierMallAssetStoreRef);

            String userToken = huaRunLoginService.getUserToken(orderConsumeReq.getStoreId());
            Map<String, String> header = new HashMap<>();
            header.put("Authorization","Bearer " + userToken);

            SynchronizationOrderReqEntity req = initSynchronizationOrderReqEntity(orderConsumeReq,cashierMallAssetStoreRef);
            log.info("华润订单同步调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseHuaRunResp<SynchronizationOrderRespEntity>> execute = huaRunRemoteHttpApi.synchronizationOrder(header,req).execute();
            BaseHuaRunResp<SynchronizationOrderRespEntity> body = execute.body();
            log.info("华润订单同步调用远程返回参数：{}", JSON.toJSONString(body));
            if(body.getResult() == 1 || body.getResult() == 104){
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                // 更新返回的参数
                SynchronizationOrderRespEntity orderRespEntity = body.getQuery();
                cashierMallOrderLog.setUpdateExchangeId(orderRespEntity.getExchangeId());
                cashierMallOrderLog.setUpdateExchangeNo(orderRespEntity.getOrderNo());
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);

                commonResponse.setData(orderRespEntity);
            }else {
                commonResponse.setCode(body.getResult());
            }
            commonResponse.setMsg(body.getMsg());
            log.info("华润订单消费返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 华润订单同步失败",orderConsumeReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    private SynchronizationOrderReqEntity initSynchronizationOrderReqEntity(OrderConsumeReq orderConsumeReq,CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        SynchronizationOrderReqEntity req = new SynchronizationOrderReqEntity();
        req.setOrderMode(2);
        req.setMemberTel(orderConsumeReq.getMemberTel());
        req.setMemberName(orderConsumeReq.getMemberName());
        req.setOrdNum(orderConsumeReq.getOrdNum());
        req.setOrdAmount(orderConsumeReq.getOrdAmount().multiply(new BigDecimal(100)).intValue());
        req.setOrdFinishTime(orderConsumeReq.getOrdFinishTime());
        req.setOrdPayNo(orderConsumeReq.getOrdPayNo());
        req.setOrdOpenId(orderConsumeReq.getOrdOpenId());
        List<ProductItem> productItemList = orderConsumeReq.getProductItemList();
        List<OrdItem> ordItem = new ArrayList<>();
        productItemList.forEach(productItem -> {
            // 过滤0元商品
            if(productItem.getItemPrice().compareTo(BigDecimal.ZERO) != 0){
                OrdItem item = new OrdItem();
                // 校验虚拟商品
                if(MallConfigConstant.INTEGRAL_SKU_CODE.equals(productItem.getItemNo())){
                    item.setItemId(cashierMallAssetStoreRef.getIntegralSkuCode());
                    item.setItemArticleNo(cashierMallAssetStoreRef.getIntegralSkuCode());
                }else {
                    item.setItemId(productItem.getItemNo());
                    item.setItemArticleNo(productItem.getItemNo());
                }
                item.setItemPrice(productItem.getItemPrice().multiply(new BigDecimal(100)).intValue());
                item.setItemName(productItem.getItemName());
                item.setItemCount(productItem.getItemQty());
                ordItem.add(item);
            }
        });
        req.setOrdItem(ordItem);
        // 回调地址
        req.setNotifyUrl(requestAddressProperties.getCallback() + MallUrlConstant.ORDER_CALLBACK);
        return req;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeNo(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setBjStoreId(orderConsumeReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(1);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult pushRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("华润订单退货退款输入参数：{}", JSON.toJSONString(orderRefundReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderRefundReq, cashierMallAssetStoreRef);

            RefundOrderReqEntity req = initialRefundOrderReqEntity(orderRefundReq,cashierMallAssetStoreRef);

            String userToken = huaRunLoginService.getUserToken(orderRefundReq.getStoreId());
            Map<String, String> header = new HashMap<>();
            header.put("Authorization","Bearer " + userToken);

            log.info("华润订单退货退款调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseHuaRunResp<RefundOrderRespEntity>> execute = huaRunRefundRemoteHttpApi.refundOrder(header,req).execute();
            BaseHuaRunResp<RefundOrderRespEntity> body = execute.body();
            log.info("华润订单退货退款调用远程返回参数：{}", JSON.toJSONString(body));
            if(body.getResult() == 1 || body.getResult() == 104){
                RefundOrderRespEntity orderRespEntity = body.getQuery();
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setData(orderRespEntity);
                // 重新更新参数
                cashierMallOrderLog.setUpdateExchangeId(orderRespEntity.getExchangeId());
                cashierMallOrderLog.setUpdateExchangeNo(orderRefundReq.getRefundNo());
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            }else {
                commonResponse.setCode(body.getResult());
            }
            commonResponse.setMsg(body.getMsg());
            log.info("华润订单退货退款返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 华润订单退货退款失败",orderRefundReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    @Override
    public ResponseResult prepareRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("华润订单预退货输入参数：{}", JSON.toJSONString(orderRefundReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            // 记录流水
//            CashierMallOrderLog cashierMallOrderLog = tailCashierMallOrderLog(orderRefundReq, cashierMallAssetStoreRef);

            PrepareRefundOrderReqEntity req = getPrepareRefundOrderReqEntity(orderRefundReq);
            String userToken = huaRunLoginService.getUserToken(orderRefundReq.getStoreId());
            Map<String, String> header = new HashMap<>();
            header.put("Authorization","Bearer " + userToken);

            log.info("华润订单预退货调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseHuaRunResp<PrepareRefundOrderRespEntity>> execute = huaRunRefundRemoteHttpApi.prepareRefundOrder(header,req).execute();
            BaseHuaRunResp<PrepareRefundOrderRespEntity> body = execute.body();
            log.info("华润订单预退货调用远程返回参数：{}", JSON.toJSONString(body));
            if(body.getResult() == 1 || body.getResult() == 104){
                PrepareRefundOrderRespEntity orderRespEntity = body.getQuery();
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setData(orderRespEntity);
                // 重新更新参数
//                cashierMallOrderLog.setExchangeId(orderRespEntity.getExchangeId());
//                cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
            }else {
                commonResponse.setCode(body.getResult());
            }
            commonResponse.setMsg(body.getMsg());
            log.info("华润订单预退货返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 华润预退货失败",orderRefundReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    @Override
    public ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("华润订单自收银退货输入参数：{}", JSON.toJSONString(orderRefundReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
            cashierMallOrderLog.setOrdNum(orderRefundReq.getOrdNum());
            cashierMallOrderLog.setExchangeId(orderRefundReq.getRefundNo());
            cashierMallOrderLog.setExchangeNo(orderRefundReq.getRefundNo());
            cashierMallOrderLog.setBjStoreId(orderRefundReq.getStoreId());
            cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
            cashierMallOrderLog.setIsCallback("N");
            cashierMallOrderLog.setIsExecute("N");
            cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
            cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
            cashierMallOrderLog.setType(3);
            cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);

            RefundOrderReqEntity req = new RefundOrderReqEntity();
            req.setOrdNum(orderRefundReq.getOrdNum());
            req.setExchangeId(orderRefundReq.getExchangeId());
            req.setRefundDayReturnFlag(0);
            req.setRefundNo(orderRefundReq.getRefundNo());
            req.setOrderNo(orderRefundReq.getExchangeNo());
            req.setRefundAmount(orderRefundReq.getRefundAmount().multiply(new BigDecimal(100)).intValue());
            req.setOrdReason(orderRefundReq.getOrdReason());
            req.setMemberTel(orderRefundReq.getMemberTel());
            req.setOrdOpenId(orderRefundReq.getOrdOpenId());
            req.setOrdPayNo(orderRefundReq.getOrdPayNo());
            List<OrdItem> ordItem = new ArrayList<>();
            List<ProductItem> productItemList = orderRefundReq.getProductItemList();
            productItemList.forEach(productItem -> {
                OrdItem item = new OrdItem();
                item.setItemId(productItem.getItemNo());
                item.setItemArticleNo(productItem.getItemNo());
                item.setItemName(productItem.getItemName());
                item.setItemCount(productItem.getItemQty());
                item.setItemPrice(productItem.getItemPrice().multiply(new BigDecimal(100)).intValue());
                ordItem.add(item);
            });
            req.setOrdItem(ordItem);
            req.setOrdFinishTime(orderRefundReq.getOrdFinishTime());
            req.setNotifyUrl(requestAddressProperties.getCallback() + MallUrlConstant.ORDER_CALLBACK);
            req.setRefundWxNum(orderRefundReq.getOrdPayNo());

            String userToken = huaRunLoginService.getUserToken(orderRefundReq.getStoreId());
            Map<String, String> header = new HashMap<>();
            header.put("Authorization","Bearer " + userToken);
            log.info("华润订单自收银退货调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseHuaRunResp<RefundOrderRespEntity>> execute = huaRunRefundRemoteHttpApi.shopRefundOrder(header,req).execute();
            BaseHuaRunResp<RefundOrderRespEntity> body = execute.body();
            if(body.getResult() == 1 || body.getResult() == 104){
                RefundOrderRespEntity orderRespEntity = body.getQuery();
                commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                commonResponse.setData(orderRespEntity);
                // 重新更新参数
                cashierMallOrderLog.setUpdateExchangeId(orderRespEntity.getExchangeId());
                cashierMallOrderLog.setUpdateExchangeNo(orderRespEntity.getRefundNo());
                cashierMallOrderLogMapper.updateOrderLogByParam(cashierMallOrderLog);
            }else {
                commonResponse.setCode(body.getResult());
            }
            commonResponse.setMsg(body.getMsg());
            log.info("华润订单自收银退货返回参数：{}", JSON.toJSONString(commonResponse));
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 华润自收银退货失败",orderRefundReq.getOrdNum(),e);
        }

        return commonResponse;
    }

    private PrepareRefundOrderReqEntity getPrepareRefundOrderReqEntity(OrderRefundReq orderRefundReq) {
        PrepareRefundOrderReqEntity req = new PrepareRefundOrderReqEntity();
        req.setExchangeId(orderRefundReq.getExchangeId());
        req.setOrderNo(orderRefundReq.getExchangeNo());
        req.setMemberTel(orderRefundReq.getMemberTel());
        req.setOrdPayNo(orderRefundReq.getOrdPayNo());
        req.setRefundNo(orderRefundReq.getRefundNo());
        req.setRefundAmount(orderRefundReq.getRefundAmount().multiply(new BigDecimal(100)).intValue());
        List<OrdItem> ordItem = new ArrayList<>();
        List<ProductItem> productItemList = orderRefundReq.getProductItemList();
        productItemList.forEach(productItem -> {
            OrdItem item = new OrdItem();
            item.setItemName(productItem.getItemName());
            item.setItemPrice(productItem.getItemPrice().multiply(new BigDecimal(100)).intValue());
            item.setItemId(productItem.getItemNo());
            ordItem.add(item);
        });
        req.setOrdItem(ordItem);
        req.setNotifyUrl(requestAddressProperties.getCallback() + MallUrlConstant.ORDER_CALLBACK);
        return req;
    }

    private RefundOrderReqEntity initialRefundOrderReqEntity(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        RefundOrderReqEntity req = new RefundOrderReqEntity();
        req.setOrdNum(orderRefundReq.getOrdNum());
        req.setExchangeId(orderRefundReq.getExchangeId());
        req.setRefundDayReturnFlag(0);
        req.setRefundNo(orderRefundReq.getRefundNo());
        req.setOrderNo(orderRefundReq.getExchangeNo());
        req.setRefundAmount(orderRefundReq.getRefundAmount().multiply(new BigDecimal(100)).intValue());
        req.setOrdReason(orderRefundReq.getOrdReason());
        req.setMemberTel(orderRefundReq.getMemberTel());
        req.setOrdOpenId(orderRefundReq.getOrdOpenId());
        req.setOrdPayNo(orderRefundReq.getOrdPayNo());
        List<OrdItem> ordItem = new ArrayList<>();
        List<ProductItem> productItemList = orderRefundReq.getProductItemList();
        productItemList.forEach(productItem -> {
            if(productItem.getItemPrice().compareTo(BigDecimal.ZERO) != 0){
                OrdItem item = new OrdItem();
                if(MallConfigConstant.INTEGRAL_SKU_CODE.equals(productItem.getItemNo())){
                    item.setItemId(cashierMallAssetStoreRef.getIntegralSkuCode());
                    item.setItemArticleNo(cashierMallAssetStoreRef.getIntegralSkuCode());
                }else {
                    item.setItemId(productItem.getItemNo());
                    item.setItemArticleNo(productItem.getItemNo());
                }
                item.setItemName(productItem.getItemName());
                item.setItemCount(productItem.getItemQty());
                item.setItemPrice(productItem.getItemPrice().multiply(new BigDecimal(100)).intValue());
                ordItem.add(item);
            }
        });
        req.setOrdItem(ordItem);
        req.setNotifyUrl(requestAddressProperties.getCallback() + MallUrlConstant.ORDER_CALLBACK);
        return req;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderRefundReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setExchangeNo(orderRefundReq.getRefundNo());
        cashierMallOrderLog.setBjStoreId(orderRefundReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(2);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

}
