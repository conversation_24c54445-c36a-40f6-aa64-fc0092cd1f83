package com.jnby.mallasset.remote.xiexin;


import com.jnby.mallasset.remote.xiexin.entity.BaseXiexinResp;
import com.jnby.mallasset.remote.xiexin.entity.vip.XiexinMemberRegisterEntity;
import com.jnby.mallasset.remote.xiexin.entity.vip.XiexinMemberRequestEntity;
import com.jnby.mallasset.remote.xiexin.entity.vip.XiexinMemberResponseEntity;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;


/**
 * 文件名: com.jnby.mallasset.remote.xiexin-IXiexinRemoteHttpApi.java
 * 文件简介: 协信平台
 *
 * <AUTHOR>
 * @date 2024/12/19 9:35
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface IXiexinRemoteHttpApi {


    @POST("/platform/api/member/isExist")
    Call<BaseXiexinResp<XiexinMemberResponseEntity>> queryMember(@Body XiexinMemberRequestEntity req);

    @POST("/platform/api/member/register")
    Call<BaseXiexinResp> registerMember(@Body XiexinMemberRegisterEntity req);
    
}
