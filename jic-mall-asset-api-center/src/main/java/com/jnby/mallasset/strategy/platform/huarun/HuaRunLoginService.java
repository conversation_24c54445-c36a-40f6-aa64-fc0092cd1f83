package com.jnby.mallasset.strategy.platform.huarun;

import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import com.jnby.mallasset.constant.MallRedisKeyConstant;
import com.jnby.mallasset.module.mapper.box.CashierMallStoreConfigMapper;
import com.jnby.mallasset.module.model.CashierMallStoreConfig;
import com.jnby.mallasset.remote.huarun.IHuaRunRemoteHttpApi;
import com.jnby.mallasset.remote.huarun.entity.BaseHuaRunResp;
import com.jnby.mallasset.remote.huarun.entity.UserLoginReqEntity;
import com.jnby.mallasset.remote.huarun.entity.UserLoginRespEntity;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;

/**
 * 华润用户登录token
 */
@Service
@AllArgsConstructor
@Slf4j
public class HuaRunLoginService {

    private IHuaRunRemoteHttpApi huaRunRemoteHttpApi;
    private RedisPoolUtil redisPoolUtil;
    private CashierMallStoreConfigMapper cashierMallStoreConfigMapper;

    public String getUserToken(String storeId) throws IOException {
        String tokenKey = MallRedisKeyConstant.User.TOKEN_KEY_HUA_RUN + storeId;
        String token = RedisTemplateUtil.get(redisPoolUtil,tokenKey);
        if(token != null){
            return token;
        }
        UserLoginReqEntity req = new UserLoginReqEntity();
        // 根据storeId查询用户名、密码
        CashierMallStoreConfig config = cashierMallStoreConfigMapper.selectByStoreId(storeId);
        req.setUserName(config.getPlatformPublicKey());
        req.setUserPassWord(config.getPlatformPrivateKey());
        Response<BaseHuaRunResp<UserLoginRespEntity>> execute = huaRunRemoteHttpApi.userLogin(req).execute();
        BaseHuaRunResp<UserLoginRespEntity> body = execute.body();
        if(body.isSuccess()){
            token = body.getQuery().getUserToken();
            RedisTemplateUtil.setex(redisPoolUtil, tokenKey, token, MallRedisKeyConstant.Expire.TWO_HOUR);
        }
        return token;
    }
}
