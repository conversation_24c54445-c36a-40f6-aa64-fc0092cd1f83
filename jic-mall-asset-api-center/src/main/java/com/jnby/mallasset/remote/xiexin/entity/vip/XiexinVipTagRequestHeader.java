package com.jnby.mallasset.remote.xiexin.entity.vip;

import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

@Data
@Root(name = "CrmSoapHeader", strict = false)
@Namespace(reference = "http://tempuri.org/")
public class XiexinVipTagRequestHeader {

    @Element(name = "UserId" , required = false)
    private String userId;

    @Element(name = "Password" , required = false)
    private String password;
}
