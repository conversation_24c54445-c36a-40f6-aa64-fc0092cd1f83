package com.jnby.mallasset.remote.mallcoo.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PointsDeductionByRefundReqEntity implements Serializable {
    @ApiModelProperty("外部退货单号：也就是PointsPlusByOrderReqEntity的TransID")
    @JSONField(ordinal = 1)
    private String returnTradeID;
    @ApiModelProperty("该商户在我猫酷系统中的唯一编号,商家提供")
    @JSONField(ordinal = 2)
    private Long mcShopID;
    @ApiModelProperty("退货金额,支持部分退")
    @JSONField(ordinal = 3)
    private Double returnAmount;
    @ApiModelProperty("交易时间")
    @JSONField(ordinal = 4)
    private String tradeTime;
    @ApiModelProperty("消费金额")
    @JSONField(ordinal = 5)
    private Double amount;
    @JSONField(ordinal = 6)
    @ApiModelProperty("交易流水号")
    private String tradeSerialNo;
}
