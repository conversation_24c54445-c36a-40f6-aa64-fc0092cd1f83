package com.jnby.mallasset.gateway.filter;//package org.springcenter.shopping.gateway.filter;
//
//import org.springcenter.shopping.gateway.common.RedisUtil;
//import org.springcenter.shopping.gateway.common.TokenUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.cloud.gateway.filter.GatewayFilterChain;
//import org.springframework.cloud.gateway.filter.GlobalFilter;
//import org.springframework.core.Ordered;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.server.reactive.ServerHttpRequest;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StringUtils;
//import org.springframework.web.server.ResponseStatusException;
//import org.springframework.web.server.ServerWebExchange;
//import reactor.core.publisher.Mono;
//
//import java.util.Arrays;
//import java.util.stream.Collectors;
//
//import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR;
//import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.addOriginalRequestUrl;
//
///**
// *
// */
//@Slf4j
//@Component
//public class GlobalAccessTokenFilter implements GlobalFilter, Ordered {
//    public final static String X_ACCESS_TOKEN = "X-Access-Token";
//    public final static String X_GATEWAY_BASE_PATH = "X_GATEWAY_BASE_PATH";
//
//    @Autowired
//    private RedisUtil redisUtil;
//
//    @Override
//    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
//        String url = exchange.getRequest().getURI().getPath();
//
//        String scheme = exchange.getRequest().getURI().getScheme();
//        String host = exchange.getRequest().getURI().getHost();
//        int port = exchange.getRequest().getURI().getPort();
//        String basePath = scheme + "://" + host + ":" + port;
//
//        //这里取出token进行校验
//        HttpHeaders headers = exchange.getRequest().getHeaders();
//        String token = TokenUtils.getTokenByRequest(headers);
//        if (token != null){
//            //token验证
//            boolean verify = TokenUtils.verifyToken(token, redisUtil);
//            if (!verify){
//                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR);
//            }
//        }
//
//        // 1. 重写StripPrefix(获取真实的URL)
//        addOriginalRequestUrl(exchange, exchange.getRequest().getURI());
//        String rawPath = exchange.getRequest().getURI().getRawPath();
//        String newPath = "/" + Arrays.stream(StringUtils.tokenizeToStringArray(rawPath, "/")).skip(1L).collect(Collectors.joining("/"));
//        ServerHttpRequest newRequest = exchange.getRequest().mutate().path(newPath).build();
//        exchange.getAttributes().put(GATEWAY_REQUEST_URL_ATTR, newRequest.getURI());
//
//        //将现在的request，添加当前身份
//        ServerHttpRequest mutableReq = exchange.getRequest().mutate().header("Authorization-UserName", "").header(X_GATEWAY_BASE_PATH,basePath).build();
//        ServerWebExchange mutableExchange = exchange.mutate().request(mutableReq).build();
//        return chain.filter(mutableExchange);
//    }
//
//    @Override
//    public int getOrder() {
//        return 0;
//    }
//
//}
