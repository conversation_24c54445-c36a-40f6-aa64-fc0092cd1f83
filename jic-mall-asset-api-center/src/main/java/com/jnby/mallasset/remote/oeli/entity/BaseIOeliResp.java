package com.jnby.mallasset.remote.oeli.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BaseIOeliResp<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "状态码", required = true)
    private int code;

    @ApiModelProperty(value = "返回消息", required = true)
    private String msg;

    @ApiModelProperty("响应数据")
    private T data;

    public boolean isSuccess(){
        if (this.code == 200) {
            return true;
        }
        return false;
    }
}
