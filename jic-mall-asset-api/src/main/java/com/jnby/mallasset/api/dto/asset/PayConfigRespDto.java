package com.jnby.mallasset.api.dto.asset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("支付配置回参")
public class PayConfigRespDto {
    @ApiModelProperty(value = "伯俊门店CODE")
    private String bjStoreId;
    @ApiModelProperty(value = "支付平台：1=微信直连、2=收钱吧-线上支付、3=支付宝直连、4=电银、5=华润自研、6=银联-B扫C、7=收钱吧-轻POS支付、8=银联-轻POS支付、9=收钱吧-B扫C")
    private Integer payChannel;
    @ApiModelProperty(value = "支付配置ID：对应支付中心的不同平台配置表的ID")
    private String payConfigId;
    @ApiModelProperty(value = "设备号")
    private String deviceNumber;
}
