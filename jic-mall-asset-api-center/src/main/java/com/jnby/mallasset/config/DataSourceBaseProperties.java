package com.jnby.mallasset.config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 1/22/21 4:57 PM
 */
@Component
@RefreshScope
public class DataSourceBaseProperties {
    //box datasource
    @Value(value = "${boxJdbc}")
    private String boxJdbc;

    @Value(value = "${boxUserName}")
    private String boxUserName;

    @Value(value = "${boxPassword}")
    private String boxPassword;

    @Value(value = "${wxJdbc}")
    private String wxJdbc;

    @Value(value = "${wxUserName}")
    private String wxUserName;

    @Value(value = "${wxPassword}")
    private String wxPassword;

    @Value(value = "${bojunJdbc}")
    private String bojunJdbc;

    @Value(value = "${bojunUserName}")
    private String bojunUserName;

    @Value(value = "${bojunPassword}")
    private String bojunPassword;

    // common config value
    private Integer initialSize = 10;
    private Integer maxActive = 30;
    private long maxWait = 3000L;
    private Integer minIdle = 10;
    private long timeBetweenEvictionRunsMillis = 90000L;
    private long removeAbandonedTimeoutMillis = 12*1000L;
    private boolean removeAbandoned = true;
    private boolean testOnBorrow = true;

    public String getBoxJdbc() {
        return boxJdbc;
    }

    public void setBoxJdbc(String boxJdbc) {
        this.boxJdbc = boxJdbc;
    }

    public String getBoxUserName() {
        return boxUserName;
    }

    public void setBoxUserName(String boxUserName) {
        this.boxUserName = boxUserName;
    }

    public String getBoxPassword() {
        return boxPassword;
    }

    public void setBoxPassword(String boxPassword) {
        this.boxPassword = boxPassword;
    }

    public String getWxJdbc() {
        return wxJdbc;
    }

    public void setWxJdbc(String wxJdbc) {
        this.wxJdbc = wxJdbc;
    }

    public String getWxUserName() {
        return wxUserName;
    }

    public void setWxUserName(String wxUserName) {
        this.wxUserName = wxUserName;
    }

    public String getWxPassword() {
        return wxPassword;
    }

    public void setWxPassword(String wxPassword) {
        this.wxPassword = wxPassword;
    }

    public String getBojunJdbc() {
        return bojunJdbc;
    }

    public void setBojunJdbc(String bojunJdbc) {
        this.bojunJdbc = bojunJdbc;
    }

    public String getBojunUserName() {
        return bojunUserName;
    }

    public void setBojunUserName(String bojunUserName) {
        this.bojunUserName = bojunUserName;
    }

    public String getBojunPassword() {
        return bojunPassword;
    }

    public void setBojunPassword(String bojunPassword) {
        this.bojunPassword = bojunPassword;
    }

    public Integer getInitialSize() {
        return initialSize;
    }

    public void setInitialSize(Integer initialSize) {
        this.initialSize = initialSize;
    }

    public Integer getMaxActive() {
        return maxActive;
    }

    public void setMaxActive(Integer maxActive) {
        this.maxActive = maxActive;
    }

    public long getMaxWait() {
        return maxWait;
    }

    public void setMaxWait(long maxWait) {
        this.maxWait = maxWait;
    }

    public Integer getMinIdle() {
        return minIdle;
    }

    public void setMinIdle(Integer minIdle) {
        this.minIdle = minIdle;
    }

    public long getTimeBetweenEvictionRunsMillis() {
        return timeBetweenEvictionRunsMillis;
    }

    public void setTimeBetweenEvictionRunsMillis(long timeBetweenEvictionRunsMillis) {
        this.timeBetweenEvictionRunsMillis = timeBetweenEvictionRunsMillis;
    }

    public long getRemoveAbandonedTimeoutMillis() {
        return removeAbandonedTimeoutMillis;
    }

    public void setRemoveAbandonedTimeoutMillis(long removeAbandonedTimeoutMillis) {
        this.removeAbandonedTimeoutMillis = removeAbandonedTimeoutMillis;
    }

    public boolean isRemoveAbandoned() {
        return removeAbandoned;
    }

    public void setRemoveAbandoned(boolean removeAbandoned) {
        this.removeAbandoned = removeAbandoned;
    }

    public boolean isTestOnBorrow() {
        return testOnBorrow;
    }

    public void setTestOnBorrow(boolean testOnBorrow) {
        this.testOnBorrow = testOnBorrow;
    }

}
