package com.jnby.mallasset.remote.mallcoo.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Builder
@ApiModel("批量返还券回参")
@Data
public class CouponBatchReturnRespEntity implements Serializable {

    private List<ReturnCoupon> CouponInfoList;

    @Data
    public class ReturnCoupon implements Serializable {
        @ApiModelProperty("券码")
        private String VCode;
        @ApiModelProperty("追踪ID【不可重复(当前应用下必须唯一)】(保持请求的的唯一性，防止重复提交)")
        private String TraceID;
        @ApiModelProperty("操作是否成功")
        private Boolean IsSuccess;
        @ApiModelProperty("操作描述")
        private String Msg;
    }
}
