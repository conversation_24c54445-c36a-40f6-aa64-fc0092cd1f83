package com.jnby.mallasset.strategy.platform.drc;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.config.exception.MallException;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.res.CommonMemberResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallMemberLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.remote.drc.IDrcRemoteHttpApi;
import com.jnby.mallasset.remote.drc.entity.BaseDrcDataResp;
import com.jnby.mallasset.remote.drc.entity.BaseDrcResp;
import com.jnby.mallasset.remote.drc.entity.DrcMemberRegisterReqEntity;
import com.jnby.mallasset.remote.drc.entity.DrcMemberRegisterRespEntity;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 大融城会员平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.DA_RONG_CHENG, category = PlatformCategoryTypeEnum.MEMBER)
@AllArgsConstructor
@Slf4j
public class DrcMemberStrategyService extends AbstractMemberService {

    private IDrcRemoteHttpApi drcRemoteHttpApi;
    private CashierMallMemberLogMapper cashierMallMemberLogMapper;
    private DrcAccessTokenService drcAccessTokenService;

    @Override
    public ResponseResult openCard(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        log.info("大融城会员注册输入参数：{}", JSON.toJSONString(memberRegisterReq));
        ResponseResult commonResponse = new ResponseResult();
        try{
            String storeId = memberRegisterReq.getStoreId();
            String mobile = memberRegisterReq.getMobile();
            // 记录流水
            CashierMallMemberLog cashierMallMemberLog = recordMemberLog(cashierMallAssetStoreRef, storeId, mobile);

            String accessToken = drcAccessTokenService.getAccessToken(storeId);
            Map<String, String> header = new HashMap<>();
            header.put("token",accessToken);
            // 调用大融城查询会员是否已存在
            DrcMemberRegisterReqEntity registerReqEntity = new DrcMemberRegisterReqEntity();
            registerReqEntity.setMember_phone(mobile);
            registerReqEntity.setMall_id(cashierMallAssetStoreRef.getPlatformMallId());
            log.info("大融城查询会员调用远程接口参数：{}", JSON.toJSONString(registerReqEntity));
            Response<BaseDrcResp<BaseDrcDataResp<DrcMemberRegisterRespEntity>>> execute = drcRemoteHttpApi.queryMemberInfo(header,registerReqEntity).execute();
            BaseDrcResp<BaseDrcDataResp<DrcMemberRegisterRespEntity>> body = execute.body();
            // 判断新会员、老会员
            CommonMemberResponse commonMemberResponse = new CommonMemberResponse();
            if(body.getCode() == 200){
                BaseDrcDataResp<DrcMemberRegisterRespEntity> baseDrcDataResp = body.getResult();
                log.info("大融城查询会员返回数据：{}", JSON.toJSONString(baseDrcDataResp));
                if(baseDrcDataResp.isSuccess() && baseDrcDataResp.getData() != null){
                    // 查询出数据说明是老会员
                    String memberCode = baseDrcDataResp.getData().getMember_code();
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    commonMemberResponse.setMemberFlag(1);
                    commonMemberResponse.setMemberId(memberCode);
                    commonResponse.setMsg(body.getMsg());
                    commonResponse.setData(commonMemberResponse);
                    // 更新执行标识
                    cashierMallMemberLog.setOpenUserId(memberCode);
                    cashierMallMemberLog.setIsExecute("Y");
                    cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
                    return commonResponse;
                }
            }
            // 新会员调注册接口
            DrcMemberRegisterReqEntity req = new DrcMemberRegisterReqEntity();
            req.setMall_id(cashierMallAssetStoreRef.getPlatformMallId());
            req.setMember_phone(mobile);
            req.setData_source("API");
            req.setOperators(cashierMallAssetStoreRef.getPlatformAppId());
            log.info("大融城注册会员调用远程接口参数：{}", JSON.toJSONString(req));
            Response<BaseDrcResp<BaseDrcDataResp<DrcMemberRegisterRespEntity>>> response = drcRemoteHttpApi.createMember(header,req).execute();
            BaseDrcResp<BaseDrcDataResp<DrcMemberRegisterRespEntity>> baseDrcResp = response.body();
            commonResponse.setCode(baseDrcResp.getCode());
            commonResponse.setMsg(baseDrcResp.getMsg());
            BaseDrcDataResp<DrcMemberRegisterRespEntity> registerRespEntity = baseDrcResp.getResult();
            if(baseDrcResp.getCode() == 200){
                String memberCode = registerRespEntity.getData().getMember_code();
                commonMemberResponse.setMemberFlag(0);
                commonMemberResponse.setMemberId(memberCode);
                commonResponse.setData(commonMemberResponse);
                // 更新执行标识
                cashierMallMemberLog.setOpenUserId(memberCode);
                cashierMallMemberLog.setIsExecute("Y");
                cashierMallMemberLogMapper.updateInfoByParam(cashierMallMemberLog);
            }else {
                commonResponse.setData(registerRespEntity);
            }
            log.info("大融城会员注册返回参数：{}", JSON.toJSONString(commonResponse));
            return commonResponse;
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 大融城会员注册开卡失败",memberRegisterReq.getMobile(),e);
        }
        return commonResponse;
    }

    private CashierMallMemberLog recordMemberLog(CashierMallAssetStoreRef cashierMallAssetStoreRef, String storeId, String mobile) {
        CashierMallMemberLog cashierMallMemberLog = new CashierMallMemberLog();
        cashierMallMemberLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallMemberLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallMemberLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallMemberLog.setBjStoreId(storeId);
        cashierMallMemberLog.setMobile(mobile);
        cashierMallMemberLog.setIsExecute("N");
        cashierMallMemberLogMapper.insertEntity(cashierMallMemberLog);
        return cashierMallMemberLog;
    }

    @Override
    public ResponseResult getMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        ResponseResult commonResponse = new ResponseResult();
        try {
            String storeId = memberRegisterReq.getStoreId();
            String mobile = memberRegisterReq.getMobile();
            String accessToken = drcAccessTokenService.getAccessToken(storeId);
            Map<String, String> header = new HashMap<>();
            header.put("token",accessToken);
            // 调用大融城查询会员是否已存在
            DrcMemberRegisterReqEntity registerReqEntity = new DrcMemberRegisterReqEntity();
            registerReqEntity.setMember_phone(mobile);
            registerReqEntity.setMall_id(cashierMallAssetStoreRef.getPlatformMallId());
            log.info("大融城查询会员调用远程接口参数：{}", JSON.toJSONString(registerReqEntity));
            Response<BaseDrcResp<BaseDrcDataResp<DrcMemberRegisterRespEntity>>> execute = drcRemoteHttpApi.queryMemberInfo(header,registerReqEntity).execute();
            BaseDrcResp<BaseDrcDataResp<DrcMemberRegisterRespEntity>> body = execute.body();
            // 判断新会员、老会员
            CommonMemberResponse commonMemberResponse = new CommonMemberResponse();
            if(body.getCode() == 200){
                BaseDrcDataResp<DrcMemberRegisterRespEntity> baseDrcDataResp = body.getResult();
                log.info("大融城查询会员返回数据：{}", JSON.toJSONString(baseDrcDataResp));
                if(baseDrcDataResp.isSuccess() && baseDrcDataResp.getData() != null){
                    // 查询出数据说明是老会员
                    String memberCode = baseDrcDataResp.getData().getMember_code();
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    commonMemberResponse.setMemberFlag(1);
                    commonMemberResponse.setMemberId(memberCode);
                    commonResponse.setMsg(body.getMsg());
                    commonResponse.setData(commonMemberResponse);
                }
            }
        } catch (IOException e) {
            log.error("大融城查询会员超时", e);
            throw new MallException(SystemErrorEnum.MEMBER_INTERFACE_TIMEOUT_ERROR);
        } catch (Exception e) {
            log.error("大融城查询会员异常", e);
            throw new MallException(SystemErrorEnum.MEMBER_UNKNOWN_ERROR);
        }
        return commonResponse;
    }
}
