package com.jnby.mallasset.config.swagger;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;


/**
 * <AUTHOR>
 * @date 2021/3/5
 */
public class SwaggerEnableCondition implements Condition {

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        String active = context.getEnvironment().getProperty("spring.profiles.active");
        return "dev".equals(active) || "test".equals(active);
    }
}
