package com.jnby.mallasset.remote.kaide.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 文件名: com.jnby.mallasset.remote.kaide.entity-KaiDeOrderRespEntity.java
 * 文件简介: 凯德订单响应
 *
 * <AUTHOR>
 * @date 2024/7/26 16:54
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class KaiDeOrderRespEntity implements Serializable {
    @ApiModelProperty(value = "交易标识:退货时需使用")
    private String identifier;
    @ApiModelProperty(value = "单据编号")
    private String receiptNo;
}
