package com.jnby.mallasset.enums;

/**
 * 状态枚举
 */
public enum OperationStatusTypeEnum {

    SUCCESS(1, "成功"),
    FAIL(2, "失败"),
    ;
    /**
     * 编号
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;

    OperationStatusTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
