package com.jnby.mallasset.remote.xiexin.entity.vip;

import lombok.Data;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

@Data
@Root(name = "GetVipCardResponse" )
@Namespace(reference = "http://tempuri.org/")
public class XiexinVipResponseResult {

    @Element(name = "GetVipCardResult" , required = false)
    private boolean vipCardResult;

    @Element(name = "msg" , required = false)
    private String msg;

    @Element(name = "vipCard" , required = false)
    private XiexinVipResponseCard vipCard;
}
