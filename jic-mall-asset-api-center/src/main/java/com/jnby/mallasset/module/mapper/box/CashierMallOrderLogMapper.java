package com.jnby.mallasset.module.mapper.box;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.mallasset.module.model.CashierMallMemberLog;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @Author: CodeGenerator
 * @Date: 2024-07-15 13:35:45
 * @Description: 商场资产规则配置Mapper
 */
@Mapper
public interface CashierMallOrderLogMapper extends BaseMapper<CashierMallOrderLog> {

    void insertEntity(CashierMallOrderLog cashierMallOrderLog);

    void updateEntityByParam(CashierMallOrderLog cashierMallOrderLog);

    void updateOrderLogByParam(CashierMallOrderLog cashierMallOrderLog);

    List<CashierMallOrderLog> selectMallOrderLogList();

    void updateInfoByExchangeId(CashierMallOrderLog cashierMallOrderLog);

    CashierMallOrderLog selectOrderLogByOrderNumAndExchangeId(@Param("ordNum") String ordNum, @Param("exchangeId") String exchangeId);

    CashierMallOrderLog selectSumRefundAmount(@Param("ordNum") String ordNum);

    void updateExchangeNoWithParam(CashierMallOrderLog cashierMallOrderLog);

    void insertAddLog(CashierMallOrderLog cashierMallOrderLog);

    List<CashierMallOrderLog> selectLogByOrdNumAndType(@Param("ordNum") String ordNum);

    CashierMallOrderLog queryFirstRefundOrderByOrdNum(@Param("ordNum") String ordNum);

    Long selectYinTaiOrderNoSeq();

    Long selectDrcOrderNoSeq();

    Long selectXinxieOrderSequese();

    int selectSumRefundProductQty(@Param("ordNum") String ordNum);
}
