package com.jnby.mallasset.strategy.factory;

import com.jnby.mallasset.strategy.category.AbstractMallService;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class MallServiceFactory {
    /**
     * 该方式可以将父类所有的子类都注入进来
     */
    @Autowired
    private List<AbstractMallService> abstractMallServiceList;

    /**
     * key：，value：AbstractAssetService
     */
    public static final Map<String, AbstractMallService> SERVICE_MAP = new HashMap<>();

    @PostConstruct
    public void registryService() {
        if (CollectionUtils.isEmpty(abstractMallServiceList)) {
            return;
        }

        for (AbstractMallService service : abstractMallServiceList) {
            // 获取接口上的注解
            PlatformTypeAnnotation annotation = service.getClass().getAnnotation(PlatformTypeAnnotation.class);
            // 如果注解为空，则跳过
            if (annotation == null) {
                log.error("策略类注解不能为空，请检查");
                System.exit(0);
            }

            // 获取注解的属性值
            PlatformTypeEnum platform = annotation.platform();
            PlatformCategoryTypeEnum category = annotation.category();

            if (platform == null || category == null) {
                log.error("策略类注解属性值不能为空，请检查");
                System.exit(0);
            }

            String mapName = getMapName(platform, category);

            log.info("加载mapkey[{}]商场策略类[{}]", mapName, service);
            SERVICE_MAP.put(mapName, service);
        }
    }

    /**
     * 接口路由
     *
     * @param platform 平台
     * @param category 类目
     * @return AbstractMallService
     */
    public AbstractMallService router(PlatformTypeEnum platform, PlatformCategoryTypeEnum category) {
        if (platform == null) {
            throw new RuntimeException("platform mast be not null");
        }

        if (SERVICE_MAP.isEmpty()) {
            throw new RuntimeException("SERVICE_MAP is empty");
        }

        return SERVICE_MAP.get(getMapName(platform, category));
    }

    public static String getMapName(PlatformTypeEnum platform, PlatformCategoryTypeEnum category) {
        return platform.name() + "-" + category.name();
    }
}
